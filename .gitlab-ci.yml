stages:
  - build
  #- test
  - deploy
  - deploy-prod

variables:
  ECS_CLUSTER_NAME: "portal-v2-api-dev"
  ECS_SERVICE_NAME: "portal-v2-api-dev-api"
  ECS_TASK_DEFINITION_NAME: "portal-v2-api-dev-api"
  ECS_CONTAINER_NAME: "portal-v2-api-dev-api"
  DOCKER_HOST: tcp://docker:2375/
  DOCKER_DRIVER: overlay2
  IMAGE_TAG: ${AWS_ACCOUNT_ID}.dkr.ecr.$AWS_REGION.amazonaws.com/${IMAGE_REPO_NAME}:$CI_COMMIT_SHORT_SHA
  IMAGE_TAG_STAGING: ${AWS_ACCOUNT_ID}.dkr.ecr.ap-southeast-1.amazonaws.com/portal:$CI_COMMIT_SHORT_SHA

before_script:
  - export PATH="$PATH:/root/.dotnet/tools"

build:
  stage: build
  environment: dev
  image: bentolor/docker-dind-awscli
  services:
    - docker:dind
  before_script:
    - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin https://$AWS_ACCOUNT_ID.dkr.ecr.$AWS_REGION.amazonaws.com
    
  script:
    - cd src
    - docker build -t $IMAGE_REPO_NAME .
    - docker tag $IMAGE_REPO_NAME $IMAGE_TAG
    - docker push $IMAGE_TAG
    - aws ecr get-login-password --region ap-southeast-1 | docker login --username AWS --password-stdin https://$AWS_ACCOUNT_ID.dkr.ecr.ap-southeast-1.amazonaws.com
    - docker tag $IMAGE_TAG $IMAGE_TAG_STAGING
    - docker push $IMAGE_TAG_STAGING


#test:
#  stage: test
#  script:
#    - dotnet test
deploy_dev:
  stage: deploy
  environment: dev
  when: manual
  dependencies:
    - build
  image: bentolor/docker-dind-awscli
  services:
    - name: docker:dind
      command: ["dockerd", "--host=tcp://0.0.0.0:2375"]
      alias: 'docker'
  script:
    - apk add curl openssl bash --no-cache
    - curl -LO "https://storage.googleapis.com/kubernetes-release/release/$(curl -s https://storage.googleapis.com/kubernetes-release/release/stable.txt)/bin/linux/amd64/kubectl" 
    - chmod +x ./kubectl 
    - mv ./kubectl /usr/local/bin/kubectl 
    - curl -fsSL -o get_helm.sh https://raw.githubusercontent.com/helm/helm/master/scripts/get-helm-3 
    - chmod +x get_helm.sh && ./get_helm.sh
    - aws sts get-caller-identity
    - aws eks --region ap-southeast-1 update-kubeconfig --name sanas-apps
    - cd helm/deployment/charts
    - helm upgrade --install sanas-portal ./sanas-portal --values ./config/values-dev.yaml --namespace=portal-dev --set=image.tag=$CI_COMMIT_SHORT_SHA
      --set-string environmentVariables.Auth0ManagementClient__Audience="${Auth0ManagementClient__Audience}"
      --set-string environmentVariables.Auth0ManagementClient__ClientID="${Auth0ManagementClient__ClientID}"
      --set-string environmentVariables.Auth0ManagementClient__ClientSecret="${Auth0ManagementClient__ClientSecret}"
      --set-string environmentVariables.Auth0ManagementClient__Domain="${Auth0ManagementClient__Domain}"
      --set-string environmentVariables.Auth0ManagementClient__ResourceServerID="${Auth0ManagementClient__ResourceServerID}"
      --set-string environmentVariables.Auth0ManagementClient__TokenUrl="${Auth0ManagementClient__TokenUrl}"
      --set-string environmentVariables.Auth0ManagementClient__WebhookApiKey="${Auth0ManagementClient__WebhookApiKey}"
      --set-string environmentVariables.Auth0__Audience="${Auth0__Audience}"
      --set-string environmentVariables.FreshdeskSettings__Enabled=${FreshdeskSettings__Enabled}
      --set-string environmentVariables.Auth0__Authority="${Auth0__Authority}"
      --set-string environmentVariables.AzLogAnalytics__ApiEndpoint="${AzLogAnalytics__ApiEndpoint}"
      --set-string environmentVariables.AzLogAnalytics__ClientId="${AzLogAnalytics__ClientId}"
      --set-string environmentVariables.AzLogAnalytics__ClientSecret="${AzLogAnalytics__ClientSecret}"
      --set-string environmentVariables.AzLogAnalytics__TenantId="${AzLogAnalytics__TenantId}"
      --set-string environmentVariables.AzLogAnalytics__WorkspaceId="${AzLogAnalytics__WorkspaceId}"
      --set-string environmentVariables.ConnectionStrings__SanasPortalDB="${ConnectionStrings__SanasPortalDB}"
      --set-string environmentVariables.ResponseSettings__PrivateKey="${ResponseSettings__PrivateKey}"
      --set-string environmentVariables.Swaggerclient__AuthUrl="${Swaggerclient__AuthUrl}"
      --set-string environmentVariables.Swaggerclient__ClientId="${Swaggerclient__ClientId}"
      --set-string environmentVariables.Swaggerclient__ClientSecret="${Swaggerclient__ClientSecret}"
      --set-string environmentVariables.Swaggerclient__TokenUrl="${Swaggerclient__TokenUrl}"
      --set-string environmentVariables.FreshdeskSettings__ApiPath="${FreshdeskSettings__ApiPath}"
      --set-string environmentVariables.FreshdeskSettings__Enabled=${FreshdeskSettings__Enabled}
      --set-string environmentVariables.FreshdeskSettings__EndPoint="${FreshdeskSettings__EndPoint}"
      --set-string environmentVariables.S3Settings__Region="${S3Settings__Region}"
      --set-string environmentVariables.S3Settings__FeedbackBucketName="${S3Settings__FeedbackBucketName}"
      --set-string environmentVariables.FreshdeskSettings__ApiKey="${FreshdeskSettings__ApiKey}"
      --set-string environmentVariables.SmtpSettings__Password="${SmtpSettings__Password}"
      --set-string environmentVariables.Fcm__AuthKey="${Fcm__AuthKey}"
      --set-string environmentVariables.Bns__BaseUrl="${Bns__BaseUrl}"
      --set-string environmentVariables.Settings__MaxRecordsPerPage="${Settings__MaxRecordsPerPage}"
      --set-string environmentVariables.Settings__DefaultTeamName="${Settings__DefaultTeamName}"
      --set-string environmentVariables.Settings__PortalEndpoint="${Settings__PortalEndpoint}"
      --set-string environmentVariables.Settings__SanasApiKey="${Settings__SanasApiKey}"
      --set-string "environmentVariables.Sso__Domains=${Sso__Domains//,/\\,}"



deploy_staging:
  stage: deploy
  environment: staging
  when: manual
  dependencies:
    - build
  image: bentolor/docker-dind-awscli
  services:
    - name: docker:dind
      command: ["dockerd", "--host=tcp://0.0.0.0:2375"]
      alias: 'docker'
  script:
    - apk add curl openssl bash --no-cache
    - curl -LO "https://storage.googleapis.com/kubernetes-release/release/$(curl -s https://storage.googleapis.com/kubernetes-release/release/stable.txt)/bin/linux/amd64/kubectl" 
    - chmod +x ./kubectl 
    - mv ./kubectl /usr/local/bin/kubectl 
    - curl -fsSL -o get_helm.sh https://raw.githubusercontent.com/helm/helm/master/scripts/get-helm-3 
    - chmod +x get_helm.sh && ./get_helm.sh
    - aws sts get-caller-identity
    - aws eks --region ap-southeast-1 update-kubeconfig --name sanas-apps
    - cd helm/deployment/charts
    - helm --debug upgrade --install sanas-portal ./sanas-portal --values ./config/values-staging.yaml --namespace=portal-staging --set=image.tag=$CI_COMMIT_SHORT_SHA
      --set-string environmentVariables.Auth0ManagementClient__Audience="${Auth0ManagementClient__Audience}"
      --set-string environmentVariables.Auth0ManagementClient__ClientID="${Auth0ManagementClient__ClientID}"
      --set-string environmentVariables.Auth0ManagementClient__ClientSecret="${Auth0ManagementClient__ClientSecret}"
      --set-string environmentVariables.Auth0ManagementClient__Domain="${Auth0ManagementClient__Domain}"
      --set-string environmentVariables.Auth0ManagementClient__ResourceServerID="${Auth0ManagementClient__ResourceServerID}"
      --set-string environmentVariables.Auth0ManagementClient__TokenUrl="${Auth0ManagementClient__TokenUrl}"
      --set-string environmentVariables.Auth0ManagementClient__WebhookApiKey="${Auth0ManagementClient__WebhookApiKey}"
      --set-string environmentVariables.Auth0__Audience="${Auth0__Audience}"
      --set-string environmentVariables.Auth0__Authority="${Auth0__Authority}"
      --set-string environmentVariables.AzLogAnalytics__ApiEndpoint="${AzLogAnalytics__ApiEndpoint}"
      --set-string environmentVariables.AzLogAnalytics__ClientId="${AzLogAnalytics__ClientId}"
      --set-string environmentVariables.AzLogAnalytics__ClientSecret="${AzLogAnalytics__ClientSecret}"
      --set-string environmentVariables.AzLogAnalytics__TenantId="${AzLogAnalytics__TenantId}"
      --set-string environmentVariables.AzLogAnalytics__WorkspaceId="${AzLogAnalytics__WorkspaceId}"
      --set-string environmentVariables.ConnectionStrings__SanasPortalDB="${ConnectionStrings__SanasPortalDB}"
      --set-string environmentVariables.ResponseSettings__PrivateKey="${ResponseSettings__PrivateKey}"
      --set-string environmentVariables.Swaggerclient__AuthUrl="${Swaggerclient__AuthUrl}"
      --set-string environmentVariables.Swaggerclient__ClientId="${Swaggerclient__ClientId}"
      --set-string environmentVariables.Swaggerclient__ClientSecret="${Swaggerclient__ClientSecret}"
      --set-string environmentVariables.Swaggerclient__TokenUrl="${Swaggerclient__TokenUrl}"
      --set-string environmentVariables.FreshdeskSettings__ApiPath="${FreshdeskSettings__ApiPath}"
      --set-string environmentVariables.FreshdeskSettings__Enabled=${FreshdeskSettings__Enabled}
      --set-string environmentVariables.FreshdeskSettings__EndPoint="${FreshdeskSettings__EndPoint}"
      --set-string environmentVariables.S3Settings__Region="${S3Settings__Region}"
      --set-string environmentVariables.S3Settings__FeedbackBucketName="${S3Settings__FeedbackBucketName}"
      --set-string environmentVariables.FreshdeskSettings__ApiKey="${FreshdeskSettings__ApiKey}"
      --set-string environmentVariables.SmtpSettings__Password="${SmtpSettings__Password}"
      --set-string environmentVariables.Fcm__AuthKey="${Fcm__AuthKey}"
      --set-string environmentVariables.Bns__BaseUrl="${Bns__BaseUrl}"
      --set-string environmentVariables.NEW_RELIC_LICENSE_KEY="${NEW_RELIC_LICENSE_KEY}"
      --set-string environmentVariables.Settings__MaxRecordsPerPage="${Settings__MaxRecordsPerPage}"
      --set-string environmentVariables.Settings__DefaultTeamName="${Settings__DefaultTeamName}"
      --set-string environmentVariables.Settings__PortalEndpoint="${Settings__PortalEndpoint}"
      --set-string environmentVariables.Settings__SanasApiKey="${Settings__SanasApiKey}"
      --set-string "environmentVariables.Sso__Domains=${Sso__Domains//,/\\,}"

deploy_uat:
  stage: deploy
  environment: uat
  when: manual
  dependencies:
    - build
  image: bentolor/docker-dind-awscli
  services:
    - name: docker:dind
      command: ["dockerd", "--host=tcp://0.0.0.0:2375"]
      alias: 'docker'
  script:
    - apk add curl openssl bash --no-cache
    - curl -LO "https://storage.googleapis.com/kubernetes-release/release/$(curl -s https://storage.googleapis.com/kubernetes-release/release/stable.txt)/bin/linux/amd64/kubectl" 
    - chmod +x ./kubectl 
    - mv ./kubectl /usr/local/bin/kubectl 
    - curl -fsSL -o get_helm.sh https://raw.githubusercontent.com/helm/helm/master/scripts/get-helm-3 
    - chmod +x get_helm.sh && ./get_helm.sh
    - aws sts get-caller-identity
    - aws eks --region ap-southeast-1 update-kubeconfig --name sanas-apps
    - cd helm/deployment/charts
    - helm --debug upgrade --install sanas-portal ./sanas-portal --values ./config/values-uat.yaml --namespace=portal-uat --set=image.tag=$CI_COMMIT_SHORT_SHA
      --set-string environmentVariables.Auth0ManagementClient__Audience="${Auth0ManagementClient__Audience}"
      --set-string environmentVariables.Auth0ManagementClient__ClientID="${Auth0ManagementClient__ClientID}"
      --set-string environmentVariables.Auth0ManagementClient__ClientSecret="${Auth0ManagementClient__ClientSecret}"
      --set-string environmentVariables.Auth0ManagementClient__Domain="${Auth0ManagementClient__Domain}"
      --set-string environmentVariables.Auth0ManagementClient__ResourceServerID="${Auth0ManagementClient__ResourceServerID}"
      --set-string environmentVariables.Auth0ManagementClient__TokenUrl="${Auth0ManagementClient__TokenUrl}"
      --set-string environmentVariables.Auth0ManagementClient__WebhookApiKey="${Auth0ManagementClient__WebhookApiKey}"
      --set-string environmentVariables.Auth0__Audience="${Auth0__Audience}"
      --set-string environmentVariables.Auth0__Authority="${Auth0__Authority}"
      --set-string environmentVariables.AzLogAnalytics__ApiEndpoint="${AzLogAnalytics__ApiEndpoint}"
      --set-string environmentVariables.AzLogAnalytics__ClientId="${AzLogAnalytics__ClientId}"
      --set-string environmentVariables.AzLogAnalytics__ClientSecret="${AzLogAnalytics__ClientSecret}"
      --set-string environmentVariables.AzLogAnalytics__TenantId="${AzLogAnalytics__TenantId}"
      --set-string environmentVariables.AzLogAnalytics__WorkspaceId="${AzLogAnalytics__WorkspaceId}"
      --set-string environmentVariables.ConnectionStrings__SanasPortalDB="${ConnectionStrings__SanasPortalDB}"
      --set-string environmentVariables.ResponseSettings__PrivateKey="${ResponseSettings__PrivateKey}"
      --set-string environmentVariables.Swaggerclient__AuthUrl="${Swaggerclient__AuthUrl}"
      --set-string environmentVariables.Swaggerclient__ClientId="${Swaggerclient__ClientId}"
      --set-string environmentVariables.Swaggerclient__ClientSecret="${Swaggerclient__ClientSecret}"
      --set-string environmentVariables.Swaggerclient__TokenUrl="${Swaggerclient__TokenUrl}"
      --set-string environmentVariables.FreshdeskSettings__ApiPath="${FreshdeskSettings__ApiPath}"
      --set-string environmentVariables.FreshdeskSettings__Enabled=${FreshdeskSettings__Enabled}
      --set-string environmentVariables.FreshdeskSettings__EndPoint="${FreshdeskSettings__EndPoint}"
      --set-string environmentVariables.S3Settings__Region="${S3Settings__Region}"
      --set-string environmentVariables.S3Settings__FeedbackBucketName="${S3Settings__FeedbackBucketName}"
      --set-string environmentVariables.FreshdeskSettings__ApiKey="${FreshdeskSettings__ApiKey}"
      --set-string environmentVariables.SmtpSettings__Password="${SmtpSettings__Password}"
      --set-string environmentVariables.Fcm__AuthKey="${Fcm__AuthKey}"
      --set-string environmentVariables.Bns__BaseUrl="${Bns__BaseUrl}"
      --set-string environmentVariables.NEW_RELIC_LICENSE_KEY="${NEW_RELIC_LICENSE_KEY}"
      --set-string environmentVariables.Settings__MaxRecordsPerPage="${Settings__MaxRecordsPerPage}"
      --set-string environmentVariables.Settings__DefaultTeamName="${Settings__DefaultTeamName}"
      --set-string environmentVariables.Settings__PortalEndpoint="${Settings__PortalEndpoint}"
      --set-string environmentVariables.Settings__SanasApiKey="${Settings__SanasApiKey}"
      --set-string "environmentVariables.Sso__Domains=${Sso__Domains//,/\\,}"


deploy_prod:
  stage: deploy-prod
  environment: production
  when: manual
  only:
      refs:
        - main
  dependencies:
    - build
  image: bentolor/docker-dind-awscli
  services:
    - name: docker:dind
      command: ["dockerd", "--host=tcp://0.0.0.0:2375"]
      alias: 'docker'
  script:
    - apk add curl openssl bash --no-cache
    - curl -LO "https://storage.googleapis.com/kubernetes-release/release/$(curl -s https://storage.googleapis.com/kubernetes-release/release/stable.txt)/bin/linux/amd64/kubectl" 
    - chmod +x ./kubectl 
    - mv ./kubectl /usr/local/bin/kubectl 
    - curl -fsSL -o get_helm.sh https://raw.githubusercontent.com/helm/helm/master/scripts/get-helm-3 
    - chmod +x get_helm.sh && ./get_helm.sh
    - aws sts get-caller-identity
    - aws eks --region ap-southeast-1 update-kubeconfig --name sanas-apps
    - cd helm/deployment/charts
    - helm upgrade --install sanas-portal ./sanas-portal --values ./config/values-prod.yaml --namespace=portal-production --set=image.tag=$CI_COMMIT_SHORT_SHA
      --set-string environmentVariables.Auth0ManagementClient__Audience="${Auth0ManagementClient__Audience}"
      --set-string environmentVariables.Auth0ManagementClient__ClientID="${Auth0ManagementClient__ClientID}"
      --set-string environmentVariables.Auth0ManagementClient__ClientSecret="${Auth0ManagementClient__ClientSecret}"
      --set-string environmentVariables.Auth0ManagementClient__Domain="${Auth0ManagementClient__Domain}"
      --set-string environmentVariables.Auth0ManagementClient__ResourceServerID="${Auth0ManagementClient__ResourceServerID}"
      --set-string environmentVariables.Auth0ManagementClient__TokenUrl="${Auth0ManagementClient__TokenUrl}"
      --set-string environmentVariables.Auth0ManagementClient__WebhookApiKey="${Auth0ManagementClient__WebhookApiKey}"
      --set-string environmentVariables.Auth0__Audience="${Auth0__Audience}"
      --set-string environmentVariables.Auth0__Authority="${Auth0__Authority}"
      --set-string environmentVariables.AzLogAnalytics__ApiEndpoint="${AzLogAnalytics__ApiEndpoint}"
      --set-string environmentVariables.AzLogAnalytics__ClientId="${AzLogAnalytics__ClientId}"
      --set-string environmentVariables.AzLogAnalytics__ClientSecret="${AzLogAnalytics__ClientSecret}"
      --set-string environmentVariables.AzLogAnalytics__TenantId="${AzLogAnalytics__TenantId}"
      --set-string environmentVariables.AzLogAnalytics__WorkspaceId="${AzLogAnalytics__WorkspaceId}"
      --set-string environmentVariables.ConnectionStrings__SanasPortalDB="${ConnectionStrings__SanasPortalDB}"
      --set-string environmentVariables.ResponseSettings__PrivateKey="${ResponseSettings__PrivateKey}"
      --set-string environmentVariables.Swaggerclient__AuthUrl="${Swaggerclient__AuthUrl}"
      --set-string environmentVariables.Swaggerclient__ClientId="${Swaggerclient__ClientId}"
      --set-string environmentVariables.Swaggerclient__ClientSecret="${Swaggerclient__ClientSecret}"
      --set-string environmentVariables.Swaggerclient__TokenUrl="${Swaggerclient__TokenUrl}"
      --set-string environmentVariables.FreshdeskSettings__ApiPath="${FreshdeskSettings__ApiPath}"
      --set-string environmentVariables.FreshdeskSettings__Enabled=${FreshdeskSettings__Enabled}
      --set-string environmentVariables.FreshdeskSettings__EndPoint="${FreshdeskSettings__EndPoint}"
      --set-string environmentVariables.S3Settings__Region="${S3Settings__Region}"
      --set-string environmentVariables.S3Settings__FeedbackBucketName="${S3Settings__FeedbackBucketName}"
      --set-string environmentVariables.FreshdeskSettings__ApiKey="${FreshdeskSettings__ApiKey}"
      --set-string environmentVariables.SmtpSettings__Password="${SmtpSettings__Password}"
      --set-string environmentVariables.Fcm__AuthKey="${Fcm__AuthKey}"
      --set-string environmentVariables.NEW_RELIC_LICENSE_KEY="${NEW_RELIC_LICENSE_KEY}"
      --set-string environmentVariables.Bns__BaseUrl="${Bns__BaseUrl}"
      --set-string environmentVariables.Settings__MaxRecordsPerPage="${Settings__MaxRecordsPerPage}"
      --set-string environmentVariables.Settings__DefaultTeamName="${Settings__DefaultTeamName}"
      --set-string environmentVariables.Settings__PortalEndpoint="${Settings__PortalEndpoint}"
      --set-string environmentVariables.Settings__SanasApiKey="${Settings__SanasApiKey}"
      --set-string "environmentVariables.Sso__Domains=${Sso__Domains//,/\\,}"

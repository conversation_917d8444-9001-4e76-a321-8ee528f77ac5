name: Client CI

on:
  push:
    branches:
      - main
    paths:
      - "portal-frontend/**"
  pull_request:
    types: 
      - opened
      - synchronize
      - reopened
    paths:
      - "portal-frontend/**"
  
  workflow_dispatch:

defaults:
  run:
    working-directory: ./portal-frontend

jobs:
  build:
    name: Build
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Install Dependencies
        run: yarn

      - name: Build
        run: yarn build

  lint:
    name: <PERSON><PERSON> and T<PERSON> checks
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Install Dependencies
        run: yarn

      - name: Linting
        run: yarn lint

      - name: TS check
        run: yarn tsc

  test:
    name: Jest tests
    runs-on: ubuntu-latest

    steps:
      - name: Checkout
        uses: actions/checkout@v3

      - name: Install Dependencies
        run: yarn

      - name: Unit Tests
        run: yarn test

  e2e-test:
    name: e2e tests
    timeout-minutes: 60
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v3
      - uses: actions/setup-node@v3
        with:
          node-version: 18
      - name: Install dependencies
        run: yarn
      - name: Install Playwright Browsers
        run: yarn playwright install --with-deps
      - name: Run Playwright tests
        run: yarn e2e
      - uses: actions/upload-artifact@v3
        if: always()
        with:
          name: playwright-report
          path: playwright-report/
          retention-days: 30

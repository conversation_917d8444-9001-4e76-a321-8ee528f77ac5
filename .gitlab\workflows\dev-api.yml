on:
    push:
      branches:
        - main
      paths:
        - "src/**"
        - ".gitlab/workflows/**"
  
    pull_request:
      types: 
        - opened
        - synchronize
        - reopened
      paths:
        - "src/**"
  
    workflow_dispatch:
  
name: Portal API - DEV
  
env:
    PROJECT: Portal API
  
defaults:
    run:
      working-directory: ./src
  
jobs:
    build-and-deploy-dev:
      name: Build and Deploy API - dev
      runs-on: ubuntu-latest
      
      environment: dev
  
      steps:
        - name: Checkout
          uses: actions/checkout@v2
  
        - name: Configure AWS credentials
          uses: aws-actions/configure-aws-credentials@v1
          with:
            aws-access-key-id: ${{ vars.AWS_ACCESS_KEY_ID_DEV }}
            aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY_DEV }}
            aws-region: ${{ vars.AWS_REGION }}
  
        - name: Login to Amazon ECR
          id: login-ecr
          uses: aws-actions/amazon-ecr-login@v1
        
        - name: Build, tag, and push API image to Amazon ECR
          id: build-api
          env:
            ECR_REGISTRY: ${{ steps.login-ecr.outputs.registry }}
            ECR_REPOSITORY: ${{ vars.AWS_ECR_API }}
            IMAGE_TAG: ${{ github.sha }}
            ENVIRONMENT: ${{ vars.ENVIRONMENT }}
          run: |
            docker buildx build -t ${ECR_REGISTRY}/${ECR_REPOSITORY}:${IMAGE_TAG} -t ${ECR_REGISTRY}/${ECR_REPOSITORY}:latest -t ${ECR_REGISTRY}/${ECR_REPOSITORY}:${ENVIRONMENT}-latest -f ./Sanas.Backend.API/Dockerfile .
            docker push -a ${ECR_REGISTRY}/${ECR_REPOSITORY}
            echo "::set-output name=image::${ECR_REGISTRY}/${ECR_REPOSITORY}:${IMAGE_TAG}"
  
        - name: Download task definition for Api
          env:
            ECS_API_TASK_DEFINITION: ${{ vars.AWS_ECS_API_TASK_DEFINITION }}
          run: |
            aws ecs describe-task-definition --task-definition ${ECS_API_TASK_DEFINITION} --query taskDefinition > task-definition.json

        - name: Fill in the new Api image ID in the Amazon ECS task definition
          id: task-def-api
          uses: aws-actions/amazon-ecs-render-task-definition@v1
          with:
            task-definition: src/task-definition.json
            container-name: ${{ vars.AWS_ECS_API_CONTAINER_IN_TASK }}
            image: ${{ steps.build-api.outputs.image }}
  
        - name: Deploy Api Amazon ECS task definition
          uses: aws-actions/amazon-ecs-deploy-task-definition@v1
          with:
            task-definition: ${{ steps.task-def-api.outputs.task-definition }}
            service: ${{ vars.AWS_ECS_API_SERVICE_NAME }}
            cluster: ${{ vars.AWS_ECS_CLUSTER_NAME }}
            wait-for-service-stability: true
on:
  push:
    branches:
      - main
    paths:
      - "portal-frontend/**"
      - ".github/workflows/**"

  pull_request:
    types: 
      - opened
      - synchronize
      - reopened
    paths:
      - "portal-frontend/**"

  workflow_dispatch:

name: Portal Web - DEV

env:
  PROJECT: Portal Web

defaults:
  run:
    working-directory: ./portal-frontend

jobs:
  build-and-deploy-dev:
    name: Build and Deploy Web - dev
    runs-on: ubuntu-latest
    
    environment: dev

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Use Node.js 18.16.0
        uses: actions/setup-node@v2
        with:
          node-version: '18.16.0'

      - name: Npm Install
        run: |
          npm i

      - name: Npm Build
        env:
          NODE_ENV:             ${{ vars.NODE_ENV }}
          API_BASE_URL:         ${{ vars.API_BASE_URL }}
          REACT_APP_MOCK_API:   ${{ vars.REACT_APP_MOCK_API }}
          # Auth0 vars
          REACT_APP_DOMAIN:     ${{ vars.REACT_APP_DOMAIN }}
          REACT_APP_CLIENT_ID:  ${{ vars.REACT_APP_CLIENT_ID }}
          REACT_APP_AUDIENCE:   ${{ vars.REACT_APP_AUDIENCE }}

        run: |
          npm run build

      - name: Deploy Frontend to S3
        uses: jakejarvis/s3-sync-action@master
        with:
          args: --acl public-read --delete
        env:
          AWS_ACCESS_KEY_ID:      ${{ vars.AWS_ACCESS_KEY_ID_DEV }}
          AWS_SECRET_ACCESS_KEY:  ${{ secrets.AWS_SECRET_ACCESS_KEY_DEV }}
          AWS_REGION:             ${{ vars.AWS_REGION }}
          AWS_S3_BUCKET:          ${{ vars.AWS_S3_BUCKET_PORTAL }}
          SOURCE_DIR:             "portal-frontend/build"

      - name: Invalidate Cloudfront Cache
        uses: chetan/invalidate-cloudfront-action@master
        env:
          AWS_ACCESS_KEY_ID:      ${{ vars.AWS_ACCESS_KEY_ID_DEV }}
          AWS_SECRET_ACCESS_KEY:  ${{ secrets.AWS_SECRET_ACCESS_KEY_DEV }}
          AWS_REGION:             ${{ vars.AWS_REGION }}
          DISTRIBUTION:           ${{ vars.AWS_CF_DISTRIBUTION_ID_PORTAL }}
          PATHS: '/*'
# Sanas Portal Backend - Dev Environment Setup Guide

This guide will walk you through setting up your development environment for Sanas Portal Backend.

## Prerequisites

Before you begin, make sure you have the following software installed:

- **Microsoft .NET SDK**: Version 8. You can download it from [dotnet.microsoft.com](https://dotnet.microsoft.com/en-us/download/dotnet/8.0).
- **Visual Studio Community Edition** Version 2022 or higher. You can download it from [here](https://visualstudio.microsoft.com/vs/community)
- **Visual Studio Code (VS Code)**: Not Mandatory but good to have. A powerful code editor. Download and install VSC from [visualstudio.com](https://code.visualstudio.com/).
- **PostgreSQL** Version 16 or higher. You can download from [here](https://sbp.enterprisedb.com/getfile.jsp?fileid=1258792)
- **pgAdmin4**: Version 8.1. You can download it from [here](https://www.postgresql.org/ftp/pgadmin/pgadmin4/v8.1/windows/)
- **Docker**: Container application development platform. Download and install Docker from [Docker download page](https://docs.docker.com/get-docker/).

## Project Setup

### 1. Clone the repository
git clone https://github.com/sanas-ai/sanas-backend cd <project_directory>

### 2. Update database Connection string in AppSettings.Development.json
For local database - *Server=localhost;Database=SanasPortal;Port=5432;User Id=<UserId>;Password=<Password>*
For RDS database - *Server=db-portal-v2-api-dev-postgres.cxa0ecylkghl.us-west-2.rds.amazonaws.com;Database=portal_v2_api_dev;Port=5432;User Id=<UserId>;Password=<Password>*

## 3. Run the application 
Hit F5 and make sure it is running on https port 5000. Navigate to https://localhost:5000/swagger

You're ready to start coding! Happy developing!

## Additional Information
- If you do not want to install PostgreSQL locally, you can also pull a docker container and use it. 
- For specific project setup and usage instructions, please refer to the project's documentation or README.
- If you encounter any issues or have questions, feel free to reach out to the project team for assistance.

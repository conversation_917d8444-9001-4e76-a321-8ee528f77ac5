image:
  repository: ************.dkr.ecr.ap-southeast-1.amazonaws.com/portal

containers:
  port: 8080

replicaCount: 1

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 3
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

environment:
  name: dev

ingress:
  enabled: true
  host: portal-v2-api-dev.sanas.ai

serviceaccount: portal-backend-sa

metadata:
  annotations:
    groupname: sanas-apps-dev-group
    certificatearn: arn:aws:acm:us-west-2:************:certificate/8469648f-a902-4208-aeee-2cbe13ce3da6

pod:
  initialDelaySeconds: 30

database:
  enabled: true

environmentVariables:
      ASPNETCORE_ENVIRONMENT : "dev"
      Settings__MaxRecordsPerPage: 100
      Settings__DefaultTeamName: "Default Team"
      Settings__PortalEndpoint: "https://portal-dev-v2.sanas.ai"
      S3Settings__Region: "us-west-2"
      S3Settings__FeedbackBucketName: "portal-v2-dev-feedback"
      SanasApp__Environment: "pr"
      SanasApp__Generation: "V2"
      Sso__Domains: "doordash.com"
      

image:
  repository: ************.dkr.ecr.ap-southeast-1.amazonaws.com/portal

containers:
  port: 8080

replicaCount: 1

autoscaling:
  enabled: true
  minReplicas: 1
  maxReplicas: 3
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

environment:
  name: staging

serviceaccount: portal-backend-sa

ingress:
  enabled: true
  host: portal-staging-api.sanas.ai

metadata:
  annotations:
    groupname: sanas-apps-staging-group
    certificatearn: arn:aws:acm:ap-southeast-1:************:certificate/f64d7f4e-e3e8-42ae-854f-20ffb9727b8d

resources:
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
   limits:
    cpu: 500m
    memory: 1024Mi
   requests:
    cpu: 250m
    memory: 500Mi

pod:
  initialDelaySeconds: 30

database:
  enabled: true

environmentVariables:
  ASPNETCORE_ENVIRONMENT : "staging"
  Settings__MaxRecordsPerPage: "100"
  Settings__DefaultTeamName: "Default Team"
  Settings__PortalEndpoint: "https://portal-staging.sanas.ai"
  S3Settings__Region: "us-west-2"
  S3Settings__FeedbackBucketName: "portal-v2-dev-feedback"
  SanasApp__Environment: "uat"
  SanasApp__Generation: "V2"
  SmtpSettings__Host: "smtp.gmail.com"
  SmtpSettings__Port: "587"
  SmtpSettings__Email: "<EMAIL>"
  CORECLR_ENABLE_PROFILING: 1 
  CORECLR_PROFILER: "{36032161-FFC0-4B61-B559-F6C5D41BAE5A}"
  CORECLR_NEWRELIC_HOME: "/usr/local/newrelic-dotnet-agent"
  CORECLR_PROFILER_PATH: "/usr/local/newrelic-dotnet-agent/libNewRelicProfiler.so"
  NEW_RELIC_APP_NAME: "Sanas Portal V2 staging"
  Sso__Domains: "doordash.com"
  

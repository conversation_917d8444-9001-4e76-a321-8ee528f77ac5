image:
  repository: ************.dkr.ecr.ap-southeast-1.amazonaws.com/portal

containers:
  port: 8080

replicaCount: 1

autoscaling:
  enabled: false
  minReplicas: 1
  maxReplicas: 3
  targetCPUUtilizationPercentage: 80
  targetMemoryUtilizationPercentage: 80

environment:
  name: uat

ingress:
  enabled: true
  host: portal-uat.sanasdev.com

serviceaccount: portal-backend-sa

metadata:
  annotations:
    groupname: sanas-apps-uat-group
    certificatearn: arn:aws:acm:ap-southeast-1:************:certificate/e65cb406-7def-44af-91e8-f151d442c1d9

pod:
  initialDelaySeconds: 30

database:
  enabled: true

environmentVariables:
      ASPNETCORE_ENVIRONMENT : "uat"
      Settings__MaxRecordsPerPage: 100
      Settings__DefaultTeamName: "Default Team"
      Settings__PortalEndpoint: "https://portal-uat.sanasdev.com"
      S3Settings__Region: "ap-southeast-1"
      S3Settings__FeedbackBucketName: "portal-v2-uat-feedback"
      SanasApp__Environment: "pr"
      SanasApp__Generation: "V2"
      Sso__Domains: "doordash.com"
      

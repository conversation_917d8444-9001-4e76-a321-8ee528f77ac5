apiVersion: apps/v1
kind: Deployment
metadata:
  name: {{ template "name" . }}-{{ .Values.environment.name }}
  labels:
    app: {{ .Chart.Name }}-{{ .Values.image.tag }}
    chart: {{ .Chart.Name }}-{{ .Chart.Version | replace "+" "_" }}
    release: {{ template "name" . }}
spec:
  replicas: {{ .Values.replicaCount }}
  selector:
    matchLabels:
      release: {{ template "name" . }}
  template:
    metadata:
      labels:
        app: {{ .Chart.Name }}-{{ .Values.image.tag }}
        release: {{ template "name" . }}
    spec:
      serviceAccountName: {{ .Values.serviceaccount }}
      containers:
        - name: {{ template "name" . }}-{{ .Values.environment.name }}
          image: "{{ .Values.image.repository }}:{{ .Values.image.tag }}"
          imagePullPolicy: {{ .Values.image.pullPolicy }}
          ports:
            - name: http
              containerPort: {{ .Values.containers.port }}
              protocol: TCP
          #livenessProbe:
            #httpGet:
              #path: /liveness
              #port: {{ .Values.service.port }}
          #readinessProbe:
            #httpGet:
              #path: /readiness
              #port: {{ .Values.service.port }}
          resources:
            {{- toYaml .Values.resources | nindent 12 }}
          env:
          {{- range $key, $value := .Values.environmentVariables }}
            - name: {{ $key }}
              value: {{ $value | quote }}
            {{- end }}
            {{- if .Values.database.enabled }}
#            - name: DB_USERNAME
#              valueFrom:
#                secretKeyRef:
#                  name: portal-db-creds
#                  key: DB_USERNAME
#            - name: DB_PASSWORD
#              valueFrom:
#                secretKeyRef:
#                  name: portal-db-creds
#                  key: DB_PASSWORD
#            - name: DB_HOST
#              valueFrom:
#                secretKeyRef:
#                  name: portal-db-creds
#                  key: DB_HOST
#            - name: DB_NAME
#              valueFrom:
#                secretKeyRef:
#                  name: portal-db-creds
#                  key: DB_NAME
            {{- end }}
      {{- with .Values.nodeSelector }}
      nodeSelector:
        {{- toYaml . | nindent 8 }}
      {{- end }}
    {{- with .Values.affinity }}
      affinity:
        {{- toYaml . | nindent 8 }}
    {{- end }}
    {{- with .Values.tolerations }}
      tolerations:
        {{- toYaml . | nindent 8 }}
    {{- end }}

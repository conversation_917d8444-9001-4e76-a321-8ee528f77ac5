{{- if .Values.autoscaling.enabled }}
apiVersion: autoscaling/v2
kind: HorizontalPodAutoscaler
metadata:
  labels:
    app: {{ template "name" . }}
    chart: {{ .Chart.Name }}-{{ .Chart.Version | replace "+" "_" }}
    release: {{ template "name" . }}
  name: {{ template "name" . }}-{{ .Values.environment.name }}
spec:
  minReplicas: {{ .Values.autoscaling.minReplicas }}
  maxReplicas: {{ .Values.autoscaling.maxReplicas }}
  metrics:
  - resource:
      name: cpu
      target:
        averageUtilization: {{ .Values.autoscaling.targetCPUUtilizationPercentage }}
        type: Utilization
    type: Resource
  - resource:
      name: memory
      target:
        averageUtilization: {{ .Values.autoscaling.targetMemoryUtilizationPercentage }}
        type: Utilization
    type: Resource
  scaleTargetRef:
    apiVersion: apps/v1
    kind: Deployment
    name: {{ template "name" . }}-{{ .Values.environment.name }}
{{- end }}

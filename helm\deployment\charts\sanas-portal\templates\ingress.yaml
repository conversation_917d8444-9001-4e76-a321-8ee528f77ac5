{{- if .Values.ingress.enabled }}
apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: {{ template "name" . }}-{{ .Values.environment.name }}-ingress
  labels:
    app: {{ .Chart.Name }}-{{ .Values.image.tag }}
    chart: {{ .Chart.Name }}-{{ .Chart.Version | replace "+" "_" }}
    release: {{ template "name" . }}
  annotations: 
    kubernetes.io/ingress.class: nginx
    nginx.ingress.kubernetes.io/proxy-body-size: 5m
spec:
  rules:
    - host: {{ .Values.ingress.host }}
      http:
        paths:
        - backend:
            service:
              name: {{ template "name" . }}-{{ .Values.environment.name }}
              port:
                number: 80
          path: /
          pathType: Prefix
{{- end }}
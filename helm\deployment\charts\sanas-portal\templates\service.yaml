apiVersion: v1
kind: Service
metadata:
  name: {{ template "name" . }}-{{ .Values.environment.name }}
  labels:
    app: {{ .Chart.Name }}-{{ .Values.image.tag }}
    chart: {{ .Chart.Name }}-{{ .Chart.Version | replace "+" "_" }}
    release: {{ template "name" . }}
spec:
  type: {{ .Values.service.type }}
  ports:
    - port: 80
      targetPort: {{ .Values.containers.port }}
      protocol: TCP
      name: http
  selector:
    release: {{ template "name" . }}

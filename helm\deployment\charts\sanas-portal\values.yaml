# This is a YAML-formatted file.
# Declare variables to be passed into your templates.
environment:
  name: dev

replicaCount: 1
version: v1

autoscaling:
  enabled: false

image:
  repository: 736883081464.dkr.ecr.ap-southeast-1.amazonaws.com/portal
  tag: latest
  pullPolicy: IfNotPresent
  pullSecret:
    enabled: true
    registry: https://hub.docker.com
    username: username
    password: password

service:
  type: ClusterIP
  port: 80
  canary:
    name: beta
    enabled: false

containers:
  port: 8080

ingress:
  enabled: false
#  annotations:
#    {}
#    # kubernetes.io/ingress.class: nginx
#    # kubernetes.io/tls-acme: "true"
#  uriPrefix: /
#  hosts:
#    - ""
#  tls: []
  #  - secretName: chart-example-tls
  #    hosts:
  #      - chart-example.local

egress:
  enabled: false
#  hosts:
#    - ""

pod:
  initialDelaySeconds: 15

resources:
  {}
  # We usually recommend not to specify default resources and to leave this as a conscious
  # choice for the user. This also increases chances charts run on environments with little
  # resources, such as Minikube. If you do want to specify resources, uncomment the following
  # lines, adjust them as necessary, and remove the curly braces after 'resources:'.
  # limits:
  #  cpu: 100m
  #  memory: 128Mi
  # requests:
  #  cpu: 100m
  #  memory: 128Mi

nodeSelector: {}

tolerations: []

affinity: {}

environmentVariables: {}
  #JWT_ISSUER: "https://accounts.google.com"
  #JWT_URI: "https://www.googleapis.com/oauth2/v3/certs"
  #jwt_policy_enabled: true

database:
  enabled: false

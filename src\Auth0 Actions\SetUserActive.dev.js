const axios = require('axios');

exports.onExecutePostChangePassword = async (event, api) => 
{
  if(event.user.email_verified === true) 
  {
    let user = 
    {
      email: event.user.email,
      userId: event.user.user_id
    };

    await setAccountVerifiedInPortal(user, event.secrets.SanasKey)
  }
};

async function setAccountVerifiedInPortal(user, secret)  {
    try {
      await axios.post('https://portal-v2-api-dev.sanas.ai/Webhooks/OnFirstLogin', user,
      {    
        headers: {
          'Content-Type': 'application/json',
          'X-SANAS-AUTH-KEY': secret
        }
      });

      console.log("User account set as verified in Sanas portal")

      return true;
    }
    catch(error) {
      if (error.response) 
      {
        console.log(error.response.data);
        console.log(error.response.status);
      }

      return false;
    }
}

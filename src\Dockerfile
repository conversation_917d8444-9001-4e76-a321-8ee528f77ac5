#See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
#USER app
WORKDIR /app
EXPOSE 8080
EXPOSE 8081

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["Sanas.Backend.API/Sanas.Backend.API.csproj", "Sanas.Backend.API/"]
COPY ["Sanas.Auth0Client/Sanas.Auth0Client.csproj", "Sanas.Auth0Client/"]
COPY ["Sanas.Backend.Data/Sanas.Backend.Data.csproj", "Sanas.Backend.Data/"]
COPY ["Sanas.Backend.BO/Sanas.Backend.BO.csproj", "Sanas.Backend.BO/"]
RUN dotnet restore "./Sanas.Backend.API/./Sanas.Backend.API.csproj"
COPY . .
WORKDIR "/src/Sanas.Backend.API"
RUN dotnet build "./Sanas.Backend.API.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./Sanas.Backend.API.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
RUN apt-get update && apt-get install -y wget ca-certificates gnupg \
&& echo 'deb http://apt.newrelic.com/debian/ newrelic non-free' | tee /etc/apt/sources.list.d/newrelic.list \
&& wget https://download.newrelic.com/548C16BF.gpg \
&& apt-key add 548C16BF.gpg \
&& apt-get update \
&& apt-get install -y 'newrelic-dotnet-agent' \
&& rm -rf /var/lib/apt/lists/*
WORKDIR /app
COPY --from=publish /app/publish .

ENTRYPOINT ["dotnet", "Sanas.Backend.API.dll"]
﻿using System.IdentityModel.Tokens.Jwt;
using System.Net.Http.Json;
using System.Text.Json.Nodes;
using Auth0.ManagementApi;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Sanas.Auth0Client.Interfaces;

namespace Sanas.Auth0Client
{
    public sealed class Auth0ManagementApiService(HttpClient httpClient, IConfiguration configuration, ILogger<Auth0ManagementApiService> logger) : IDisposable, IAuth0ManagementApiService
    {
        private ManagementApiClient _managementApiClient;
        private string _currentAccesstoken;

        public async Task<IManagementApiClient> GetManagementApiClientAsync()
        {
            if (_managementApiClient == null)
            {
                logger.LogInformation("Auth0: Creating new Management API client.");

                _currentAccesstoken = await GetNewTokenAsync();
                var managementApiConnection = new HttpClientManagementConnection(httpClient);
                var domain = configuration["Auth0ManagementClient:Domain"];

                _managementApiClient = new ManagementApiClient(_currentAccesstoken, domain, managementApiConnection);
            }
            else
            {
                if (!IsTokenValid())
                {
                    logger.LogInformation("Auth0 access token has expired. fetching a new one.");

                    _currentAccesstoken = await GetNewTokenAsync();
                    _managementApiClient.UpdateAccessToken(_currentAccesstoken);
                }
            }

            return _managementApiClient;
        }

        private bool IsTokenValid()
        {
            if (string.IsNullOrWhiteSpace(_currentAccesstoken))
            {
                return false;
            }

            var handler = new JwtSecurityTokenHandler();
            var jsonToken = handler.ReadToken(_currentAccesstoken) as JwtSecurityToken;

            // Check if the token has not expired
            return jsonToken?.ValidTo > DateTime.UtcNow;
        }

        private async Task<string> GetNewTokenAsync()
        {
            try
            {
                var formData = new Dictionary<string, string>()
                {
                    { "grant_type", "client_credentials" },
                    { "client_id", configuration["Auth0ManagementClient:ClientID"] },
                    { "client_secret", configuration["Auth0ManagementClient:ClientSecret"] },
                    { "audience", configuration["Auth0ManagementClient:Audience"] }
                };

                using var content = new FormUrlEncodedContent(formData);

                content.Headers.Clear();
                content.Headers.Add("Content-Type", "application/x-www-form-urlencoded");

                var response = await httpClient.PostAsync(configuration["Auth0ManagementClient:TokenUrl"], content);

                if (response.IsSuccessStatusCode)
                {
                    logger.LogInformation("Auth0: Successfully retrieved Auth0 access token");

                    var json = await response.Content.ReadFromJsonAsync<JsonNode>();
                    return json["access_token"].ToString();
                }
                else
                {
                    logger.LogError("Auth0: Could not retrieve Auth0 access token. {Reason}", response.ReasonPhrase);
                    return null;
                }
            }
            catch (Exception e)
            {
                logger.LogError("Auth0: Exception in retrieving access token. {@e}", e);
                throw;
            }
        }

        public async Task<string> GetAccessTokenAsync()
        {
            if (!IsTokenValid())
            {
                _currentAccesstoken = await GetNewTokenAsync();
            }

            return _currentAccesstoken;
        }

        public void Dispose()
        {
            _managementApiClient?.Dispose();
        }
    }
}

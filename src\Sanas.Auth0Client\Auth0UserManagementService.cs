﻿using System.Net.Http.Json;
using System.Text.Json;
using Auth0.ManagementApi.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Sanas.Auth0Client.Interfaces;

namespace Sanas.Auth0Client;

public class Auth0UserManagementService(
    IConfiguration configuration,
    ILogger<Auth0UserManagementService> logger,
    IAuth0ManagementApiService managementApiService,
    IHttpClientFactory httpClientFactory) : IAuth0UserManagementService
{
    public async Task<User> CreateVerifiedAuth0UserAsync(string firstName, string lastName, string email, string password)
    {
        var managementApiClient = await managementApiService.GetManagementApiClientAsync();

        var auth0UserRequest = new UserCreateRequest
        {
            FirstName = firstName?.Trim(),
            LastName = lastName?.Trim(),
            Email = email?.Trim(),
            Password = password?.Trim(),
            FullName = $"{firstName} {lastName}",
            EmailVerified = true,
            VerifyEmail = false,
            Connection = "Username-Password-Authentication",
        };

        return await managementApiClient.Users.CreateAsync(auth0UserRequest);
    }

    public async Task<User> UpdateUserProfileAsync(string userId, string firstName, string lastName)
    {
        logger.LogInformation("Auth0: Updating the user profile.");

        try
        {
            var managementApiClient = await managementApiService.GetManagementApiClientAsync();
            var user = await managementApiClient.Users.UpdateAsync(userId, new UserUpdateRequest
            {
                FirstName = firstName,
                LastName = lastName,
                FullName = $"{firstName} {lastName}",
            });

            return user;
        }
        catch (Exception e)
        {
            logger.LogError("Auth0: Exception occurred while updating the user profile. {Error}", e);
            throw;
        }
    }

    public async Task UpdateUserPasswordAsync(string userId, string password)
    {
        logger.LogInformation("Auth0: Updating the user profile.");

        try
        {
            var managementApiClient = await managementApiService.GetManagementApiClientAsync();
            var user = await managementApiClient.Users.UpdateAsync(userId, new UserUpdateRequest
            {
                Password = password
            });
        }
        catch (Exception e)
        {
            logger.LogError("Auth0: Exception occurred while updating the user profile. {Error}", e);
            throw;
        }
    }

    public async Task DeleteUserAsync(string userId)
    {
        try
        {
            var managementApiClient = await managementApiService.GetManagementApiClientAsync();
            await managementApiClient.Users.DeleteAsync(userId);
        }
        catch (Exception e)
        {
            logger.LogError("Auth0: Exception occurred while deleting the user {Error}", e);
            throw;
        }
    }

    public async Task<User> BlockUnblockUserAsync(string userId, bool block)
    {
        try
        {
            var managementApiClient = await managementApiService.GetManagementApiClientAsync();
            return await managementApiClient.Users.UpdateAsync(userId, new UserUpdateRequest { Blocked = block });
        }
        catch (Exception e)
        {
            logger.LogError("Auth0: Exception occurred while blocking the user {Error}", e);
            throw;
        }
    }

    public async Task<(User User, string PasswordResetUrl)> CreateAuth0UserAsync(string firstName, string lastName, string email)
    {
        var managementApiClient = await managementApiService.GetManagementApiClientAsync();

        var auth0UserRequest = new UserCreateRequest
        {
            FirstName = firstName,
            LastName = lastName,
            Email = email,
            Password = $"Temp@{email[..3]}",
            FullName = $"{firstName} {lastName}",
            EmailVerified = false,
            VerifyEmail = false,
            Connection = "Username-Password-Authentication",
        };

        var newAuth0User = await managementApiClient.Users.CreateAsync(auth0UserRequest);

        if (newAuth0User is not null)
        {
            var passwordResetUrl = await CreatePasswordChangeRequestAsync(newAuth0User.UserId);
            return (newAuth0User, passwordResetUrl);
        }
        else
        {
            return (newAuth0User, null);
        }        
    }

    public async Task<string> CreatePasswordChangeRequestAsync(string idpUserId)
    {
        try
        {
            var request = new
            {
                result_url = configuration["Settings:PortalEndpoint"],
                user_id = idpUserId,
                ttl_sec = 604800,
                mark_email_as_verified = true,
                includeEmailInRedirect = false
            };

            var httpClient = httpClientFactory.CreateClient("auth0");
            var token = await managementApiService.GetAccessTokenAsync();
            httpClient.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Bearer", token);
            var response = await httpClient.PostAsJsonAsync("/api/v2/tickets/password-change", request, CancellationToken.None);
            response.EnsureSuccessStatusCode();
            var responseString = await response.Content.ReadAsStringAsync();

            var url = JsonSerializer.Deserialize<JsonElement>(responseString).GetProperty("ticket").GetString();

            return url;
        }
        catch (Exception e)
        {
            logger.LogError("Error generating password reset page. {Error}", e);
        }

        return null;
    }
}
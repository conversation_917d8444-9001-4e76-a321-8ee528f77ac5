﻿using Auth0.ManagementApi.Models;

namespace Sanas.Auth0Client.Interfaces;

public interface IAuth0UserManagementService
{
    Task<(User User, string PasswordResetUrl)> CreateAuth0UserAsync(string firstName, string lastName, string email);
    Task DeleteUserAsync(string userId);

    Task<User> UpdateUserProfileAsync(string userId, string firstName, string lastName);
    Task UpdateUserPasswordAsync(string userId, string password);

    Task<User> BlockUnblockUserAsync(string userId, bool block);
    Task<string> CreatePasswordChangeRequestAsync(string idpUserId);
    Task<User> CreateVerifiedAuth0UserAsync(string firstName, string lastName, string email, string password);
}
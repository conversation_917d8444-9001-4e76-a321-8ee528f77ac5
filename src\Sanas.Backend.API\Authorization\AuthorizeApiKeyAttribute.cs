﻿using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;

namespace Sanas.Backend.API.Authorization;

[AttributeUsage(AttributeTargets.Class | AttributeTargets.Method)]
public sealed class AuthorizeApiKeyAttribute : Attribute, IAsyncAuthorizationFilter
{
    public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
    {
        var configuration = context.HttpContext.RequestServices.GetService<IConfiguration>();
        var logger = context.HttpContext.RequestServices.GetService<ILogger<AuthorizeAttribute>>();

        var apiKeyList = context.HttpContext.Request.Headers["X-SANAS-API-KEY"];
        var authKey = configuration["Settings:SanasApiKey"];

        if (apiKeyList.Count > 0)
        {
            if (apiKeyList.Any(apiKey => string.Equals(api<PERSON><PERSON>, auth<PERSON><PERSON>, StringComparison.Ordinal)))
            {
                logger.LogInformation("APi Key Authorization: Success!");
                return;
            }
        }

        logger.LogWarning("APi Key Authorization: Invalid API key specified.");
        context.Result = new UnauthorizedObjectResult("Invalid API Key.");

        await Task.CompletedTask;

        return;
    }
}

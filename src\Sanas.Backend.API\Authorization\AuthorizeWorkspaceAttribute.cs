﻿using Microsoft.AspNetCore.Mvc;
using Microsoft.AspNetCore.Mvc.Filters;
using Microsoft.Extensions.Primitives;
using Sanas.Backend.API.Extensions;
using Sanas.Backend.BO.Handlers;

namespace Sanas.Backend.API.Authorization;

[AttributeUsage(AttributeTargets.Method)]
public sealed class AuthorizeWorkspaceAttribute : Attribute, IAsyncAuthorizationFilter
{
    private readonly string[] _requiredPermissions;

    public AuthorizeWorkspaceAttribute(params string[] permissions)
    {
        _requiredPermissions = permissions.Where(p => !string.IsNullOrWhiteSpace(p)).ToArray();

        if (_requiredPermissions.Length == 0)
        {
            throw new ArgumentException("No permissions specified.");
        }
    }

    public async Task OnAuthorizationAsync(AuthorizationFilterContext context)
    {
        var workspaceHandler = context.HttpContext.RequestServices.GetRequiredService<IWorkspaceHandler>();
        var workspaceId = GetWorkspaceId(context);
        if (workspaceId > 0)
        {
            var workspace = await workspaceHandler.GetByIdAsync(workspaceId); // Check below condition - OK
            if (workspace is null &&
                !string.Equals(context.HttpContext.Request.Path, $"/api/workspaces/{workspaceId}/reactivate", StringComparison.OrdinalIgnoreCase))
            {
                context.Result = new UnauthorizedObjectResult("Unauthorized workspace.");
                return;
            }
        }

        var permissionsGranted = await GetGrantedPermissions(context);

        if (_requiredPermissions.Except(permissionsGranted).Any())
        {
            context.Result = new UnauthorizedObjectResult("You do not have required permission(s) to perform this operation.");
        }
    }

    private static async Task<string[]> GetGrantedPermissions(AuthorizationFilterContext context)
    {
        var usersHandler = context.HttpContext.RequestServices.GetRequiredService<IUsersHandler>();
        var rbacHandler = context.HttpContext.RequestServices.GetRequiredService<IRBACHandler>();
        var workspaceHandler = context.HttpContext.RequestServices.GetRequiredService<IWorkspaceHandler>();
        
        var userId = context.HttpContext.GetUserId();
        var user = await usersHandler.GetByUserIdAsync(userId);

        if (user is null)
        {
            return [];
        }

        var workspaceId = GetWorkspaceId(context);

        if (!user.IsSanasUser)
        {
            var userWorkspaces = await workspaceHandler.GetByUserIdAsync(user.PortalUserId);
            if (!userWorkspaces.Any(w => w.WorkspaceId == workspaceId))
            {
                return [];
            }
        }

        var permissions = await rbacHandler.GetUserPermissionsAsync(workspaceId, user.PortalUserId, user.IsSanasUser);

        return [.. permissions];
    }

    private static int GetWorkspaceId(AuthorizationFilterContext context)
    {
        int workspaceId = 0;

        if (context.HttpContext.Request.RouteValues.ContainsKey("WorkspaceId"))
        {
            workspaceId = int.Parse(context.HttpContext.Request.RouteValues["workspaceId"]?.ToString() ?? "0");
        }
        else if (context.HttpContext.Request.Headers.TryGetValue("WorkspaceId", out StringValues values))
        {
            _ = int.TryParse(values.FirstOrDefault("0"), out workspaceId);
        }

        return workspaceId;
    }
}
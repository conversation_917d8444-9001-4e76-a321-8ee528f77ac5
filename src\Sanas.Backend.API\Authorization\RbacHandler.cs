using Microsoft.AspNetCore.Authorization;

namespace Sanas.Backend.API.Authorization;

public sealed class RbacHandler : AuthorizationHandler<RbacRequirement>
{
    protected override async Task HandleRequirementAsync(AuthorizationHandlerContext context, RbacRequirement requirement)
    {
        if (!context.User.HasClaim(c => c.Type == "permissions"))
        {
            return;
        }

        var permission = context.User.FindFirst(c => c.Type == "permissions" && c.Value == requirement.Permission);

        if (permission == null)
        {
            return;
        }

        context.Succeed(requirement);

        await Task.CompletedTask;
    }
}

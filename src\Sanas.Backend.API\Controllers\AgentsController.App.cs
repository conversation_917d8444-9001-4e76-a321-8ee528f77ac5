﻿using System.Text.Json;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sanas.Backend.BO.Dto;
using Sanas.Backend.BO.Dto.Agent;
using Sanas.Backend.BO.Dto.RequestDto;
using Sanas.Backend.BO.Dto.ResponseDto;
using Sanas.Backend.BO.Helpers;

namespace Sanas.Backend.API.Controllers;

public partial class AgentsController
{
    private const string AccessTokenHeader = "X-SANAS-ACCESS-TOKEN";
    private const string RefreshTokenHeader = "X-SANAS-REFRESH-TOKEN";

    private readonly TimeSpan _accessTokenExpiry = TimeSpan.FromHours(24);
    private readonly TimeSpan _refreshTokenExpiry = TimeSpan.FromDays(5);

    [HttpPost("/api/agents/{agentId}/autologin")]
    [AllowAnonymous]
    public async Task<ActionResult<bool>> AutoLoginAgent(
       int agentId,
       AgentAutoLoginDto agentAutoLoginDto,
       [FromHeader(Name = AccessTokenHeader)] string token)
    {
        if (!Crypto.VerifyTokenAuthorization(token, agentAutoLoginDto.LoginId))
        {
            return Unauthorized();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest();
        }

        var agent = await agentsHandler.AgentAutoLoginAsync(agentId, agentAutoLoginDto);

        if (agent == null)
        {
            return NotFound("User not found");
        }
        else if (agent.IsDisabled)
        {
            return Forbid();
        }

        return Ok(true);
    }

    [HttpGet("/api/agents/{agentId}/heartbeat")]
    [AllowAnonymous]
    public async Task<ActionResult<bool>> AgentHeartbeatAsync(
        int agentId,
        AgentHeartbeatDto heartbeatDto,
        [FromHeader(Name = AccessTokenHeader)] string token)
    {
        if (!Crypto.VerifyTokenAuthorization(token, heartbeatDto.LoginId))
        {
            return Unauthorized();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest();
        }

        var agentRecord = await agentsHandler.GetByAgentIdAsync(agentId);

        if (agentRecord == null)
        {
            return NotFound();
        }

        if (agentRecord.IsDisabled)
        {
            return Forbid();
        }

        await agentsHandler.UpdateAgentLastCallDetailsAsync(agentId, heartbeatDto);

        return Ok(true);
    }

    [HttpPost("/api/agents/authenticate")]
    [AllowAnonymous]
    public async Task<ActionResult<AgentAuthenticationResponseDto>> AuthenticateAgentAsync(AgentLoginDto agentLoginDto)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest();
        }

        var agent = await agentsHandler.AuthenticateAgentAsync(agentLoginDto);

        if (agent is null || agent.IsDisabled)
        {
            logger.LogWarning("An unauthorized agent login detected.");
            return Unauthorized();
        }

        var response = CreateAuthorizedAgentResponse(agent);
        var signature = Crypto.SignWithPrivateKey<AgentAuthDto>(agent);

        // Add the signature to the response headers
        Response.Headers.Append("X-ResponseSignature", signature);

        return Ok(response);
    }

    [HttpPost("/api/agents/reauthenticate")]
    [AllowAnonymous]
    public async Task<ActionResult<AgentAuthenticationResponseDto>> ReauthenticateAgentAsync(
        AgentReauthenticationDto reauthDto,
        [FromHeader(Name = RefreshTokenHeader)] string token)
    {
        if (!Crypto.VerifyTokenAuthorization(token, reauthDto.LoginId))
        {
            return Unauthorized();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest();
        }

        try
        {
            var agent = await agentsHandler.ReauthenticateAgentAsync(reauthDto);

            if (agent is null)
            {
                return NotFound();
            }
            else if (agent.IsDisabled)
            {
                return Forbid();
            }

            var response = CreateAuthorizedAgentResponse(agent);
            var signature = Crypto.SignWithPrivateKey<AgentAuthDto>(agent);

            // Add the signature to the response headers
            Response.Headers.Append("X-ResponseSignature", signature);

            return Ok(response);
        }
        catch (InvalidOperationException)
        {
            return Forbid();
        }
        catch (ArgumentException)
        {
            return NotFound();
        }
    }

    [HttpPatch("/api/agents/{agentId}/logout")]
    [AllowAnonymous]
    public async Task<ActionResult<AgentAuthenticationResponseDto>> LogOutAgentAsync(
        int agentId,
        AgentLogOutDto logOutDto,
        [FromHeader(Name = AccessTokenHeader)] string token)
    {
        if (!Crypto.VerifyTokenAuthorization(token, logOutDto.LoginId))
        {
            return Unauthorized();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest();
        }

        try
        {
            var agent = await agentsHandler.GetByAgentIdAsync(agentId);

            if (agent is null)
            {
                return NotFound();
            }

            if (agent.IsDisabled)
            {
                // SWAP - 901: Commented
                // return Forbid();
            }

            var res = await agentsHandler.LogOutAgentAsync(agentId);

            await notificationsHandler.OnEventReceivedFromApp(agent, NotificationType.AgentLogout, null);

            return Ok(res);
        }
        catch (ArgumentException e)
        {
            return NotFound(e.Message);
        }
    }

    [HttpPatch("/api/agents/{agentId}/preferences")]
    [AllowAnonymous]
    public async Task<ActionResult<bool>> UpdateAgentPreferences(
        int agentId,
        AgentPreferencesUpdateDto updateDto,
        [FromHeader(Name = AccessTokenHeader)] string token)
    {
        try
        {
            var res = false;

            // Verify authorization first
            if (!Crypto.VerifyTokenAuthorization(token, updateDto.LoginId))
            {
                // If the above failed, Likely called from the portal
                if (HttpContext.User.HasClaim(c => c.Value == Permissions.UpdateAppUser))
                {
                    res = await agentsHandler.UpdateAgentPreferencesAsync(agentId, updateDto);
                    return Ok(res);
                }

                return Unauthorized();
            }

            if (!ModelState.IsValid)
            {
                return BadRequest();
            }

            // Verification passed
            var agent = await agentsHandler.GetByAgentIdAsync(agentId);

            if (agent == null)
            {
                return NotFound();
            }

            if (agent.IsDisabled)
            {
                return Forbid();
            }

            res = await agentsHandler.UpdateAgentPreferencesAsync(agentId, updateDto);

            if (updateDto.PreferenceName == "IsSynthesisEnabled")
            {
                if (updateDto.NewValue is JsonElement jsonElement && jsonElement.ValueKind == JsonValueKind.False)
                {
                    await notificationsHandler.OnEventReceivedFromApp(agent, NotificationType.SynthesisDisabled, updateDto.Reason);
                }
            }

            return Ok(res);
        }
        catch (ArgumentException e)
        {
            return NotFound(e.Message);
        }
    }

    [HttpPatch("/api/agents/{agentId}/pwd")]
    [AllowAnonymous]
    public async Task<ActionResult> UpdatePasswordFromApp(
        int agentId,
        AgentChangePasswordDto changePasswordDto,
        [FromHeader(Name = AccessTokenHeader)] string token)
    {
        if (!Crypto.VerifyTokenAuthorization(token, changePasswordDto.LoginId))
        {
            return Unauthorized();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest();
        }

        try
        {
            await agentsHandler.UpdatePasswordFromAppAsync(agentId, changePasswordDto);
            return Ok();
        }
        catch (InvalidOperationException e)
        {
            return Forbid(e.Message);
        }
        catch (ArgumentException e)
        {
            return NotFound(e.Message);
        }
    }

    [HttpPatch("/api/agents/{agentId}/icp")]
    [AllowAnonymous]
    public async Task<ActionResult> UpdateICPResultAsync(int agentId, [FromBody] OverallCompatibilityReport icpResult, [FromHeader(Name = AccessTokenHeader)] string token)
    {
        var agent = await agentsHandler.GetByAgentIdAsync(agentId);

        if (agent is null || !Crypto.VerifyTokenAuthorization(token, agent.LoginId))
        {
            return Unauthorized();
        }

        try
        {
            await agentsHandler.UpdateICPAsync(agentId, icpResult);
            return Ok();
        }
        catch (Exception e)
        {
            logger.LogError("Failed to update ICP result. {Error}", e);
            return StatusCode(StatusCodes.Status500InternalServerError, "Failed to update the ICP result");
        }
    }

    private AgentAuthenticationResponseDto CreateAuthorizedAgentResponse(AgentAuthDto agent)
    {
        var now = DateTime.UtcNow;

        return new AgentAuthenticationResponseDto()
        {
            Success = true,
            Message = "Login successful",
            AccessToken = Crypto.GenerateTokenFromPrivateKey(agent, _accessTokenExpiry, now),
            RefreshToken = Crypto.GenerateTokenFromPrivateKey(agent, _refreshTokenExpiry, now),
            CreatedAt = now,
            AccessTokenExpiry = now.Add(_accessTokenExpiry),
            RefreshTokenExpiry = now.Add(_refreshTokenExpiry),
            AgentInfo = agent
        };
    }
}

﻿using System.Globalization;
using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sanas.Backend.API.Authorization;
using Sanas.Backend.BO.Dto;
using Sanas.Backend.BO.Dto.AppReleases;
using Sanas.Backend.BO.Dto.RequestDto;
using Sanas.Backend.BO.Dto.ResponseDto;
using Sanas.Backend.BO.Handlers;

namespace Sanas.Backend.API.Controllers;

[Route("api/workspaces/{workspaceId}/[controller]")]
[ApiController]
[ApiVersion("1.0")]
[ApiVersion("2.0")]
[ApiVersion("3.0")]
[ApiVersion("4.0")]
public class AppReleasesController(IAppReleasesHandler appReleasesHandler) : ControllerBase
{
    [HttpPost("~/api/[Controller]")]
    [AuthorizeApiKey]
    public async Task<ActionResult> CreateReleaseAsync([FromBody] AppReleaseCreateDto appRelease)
    {
        try
        {
            if (appRelease is null || !ModelState.IsValid)
            {
                return BadRequest("Invalid request");
            }

            await appReleasesHandler.CreateAppReleaseAsync(appRelease);
            return Ok("Successfully created release record in portal.");
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
        catch (Exception e)
        {
            return StatusCode(StatusCodes.Status500InternalServerError, $"Error in creating app release ({e.Message}).");
        }
    }

    [HttpGet]
    [Authorize]
    public async Task<ActionResult<PagedDto<AppReleaseDto>>> GetAppReleasesAsync([FromRoute] int workspaceId, [FromQuery] RequestOptions queryOptions)
    {
        try
        {
            var startDate = Request.Query["StartDate"].FirstOrDefault();
            var endDate = Request.Query["EndDate"].FirstOrDefault();
            var modelTypeFilter = Request.Query["ModelType"].FirstOrDefault();

            var filters = new AppReleasesQueryFilters
            {
                StartDate = string.IsNullOrWhiteSpace(startDate) ? null : DateTime.ParseExact(startDate, "yyyy-MM-dd", CultureInfo.InvariantCulture.DateTimeFormat, DateTimeStyles.AssumeUniversal),
                EndDate = string.IsNullOrWhiteSpace(startDate) ? null : DateTime.ParseExact(startDate, "yyyy-MM-dd", CultureInfo.InvariantCulture.DateTimeFormat, DateTimeStyles.AssumeUniversal),
                ModelTypeId = int.TryParse(modelTypeFilter, out int modelTypeId) ? modelTypeId : null,
                Versions = [.. Request.Query["Version"]],
            };

            var response = await appReleasesHandler.GetAppReleasesAsync(workspaceId, queryOptions, filters);

            return Ok(response);
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
        catch (Exception e)
        {
            return StatusCode(StatusCodes.Status500InternalServerError, $"Error in fetching app releases ({e.Message}).");
        }
    }

    [HttpPatch("{releaseId:int}/releasenotes")]
    [HttpPatch("~/api/[Controller]/{releaseId:int}/releasenotes")]
    public async Task<ActionResult> UpdateReleaseNotes([FromRoute] int releaseId, [FromBody] string releaseNotes)
    {
        try
        {
            if (string.IsNullOrWhiteSpace(releaseNotes))
            {
                return BadRequest("Release notes cannot be empty");
            }

            await appReleasesHandler.UpdateReleaseNotesAsync(releaseId, releaseNotes);
            return Ok("Release notes updated successfully");
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
        catch (Exception e)
        {
            return StatusCode(StatusCodes.Status500InternalServerError, $"Error in updating release notes ({e.Message}).");
        }
    }

    [HttpGet]
    [Route("{releaseId}/releaseNotes")]
    [Route("~/api/[Controller]/{releaseId}/releaseNotes")]
    public async Task<ActionResult<string>> GetReleaseNotes([FromRoute] int releaseId)
    {
        try
        {
            var release = await appReleasesHandler.GetByReleaseIdAsync(releaseId);
            if (release is null)
            {
                return BadRequest("No release found for given release id.");
            }

            return Ok(release.ReleaseNotes);
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
        catch (Exception e)
        {
            return StatusCode(StatusCodes.Status500InternalServerError, $"Error in getting release notes ({e.Message}).");
        }
    }
}

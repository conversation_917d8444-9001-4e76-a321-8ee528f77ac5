﻿using Asp.Versioning;
using Microsoft.AspNetCore.Mvc;
using Sanas.Backend.BO.Dto;
using Sanas.Backend.BO.Handlers;

namespace Sanas.Backend.API.Controllers;

[ApiController]
[Route("api/Auth0")]
[ApiVersion("1.0")]
[ApiVersion("2.0")]
[ApiVersion("3.0")]
[ApiVersion("4.0")]
public class Auth0Controller(IRBACHandler auth0Handler) : ControllerBase
{
    [HttpGet("roles")]
    public async Task<RolesResponseDto> GetRolesAsync()
    {
        var allRoles = await auth0Handler.GetRolesAsync();
        return allRoles;
    }
}
﻿using System.Text.Json;
using Microsoft.AspNetCore.Mvc;
using Sanas.Backend.BO.Services.Interfaces;

namespace Sanas.Backend.API.Controllers;

[ApiController]
[Route("api/[Controller]")]
public class BnsController(
    ILogger<BnsController> logger,
    IBnsService service
) : ControllerBase
{
    [HttpPost("{endpoint}")]
    public async Task<IActionResult> PostToBnsAsync(string endpoint, [FromBody] JsonElement request)
    {
        try
        {
            HttpResponseMessage response = await service.PostToBnsAsync(endpoint, request);
            string content = await response.Content.ReadAsStringAsync();

            string contentType = response.Content.Headers.ContentType?.ToString() ?? "application/json";

            return new ContentResult
            {
                Content = content,
                StatusCode = (int)response.StatusCode,
                ContentType = contentType
            };
        }
        catch (HttpRequestException e)
        {
            logger.LogError("Error :: {Error}", e);
            return StatusCode(500, e.Message);
        }
    }
}
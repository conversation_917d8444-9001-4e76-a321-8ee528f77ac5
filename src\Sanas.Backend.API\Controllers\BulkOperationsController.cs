﻿using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sanas.Auth0Client.Interfaces;
using Sanas.Backend.API.Authorization;
using Sanas.Backend.BO.Dto;
using Sanas.Backend.BO.Helpers;
using Sanas.Backend.BO.Services;
using Sanas.Backend.Data.Models;
using Sanas.Backend.Data.Repositories.Interfaces;

namespace Sanas.Backend.API.Controllers
{
    [ApiVersion("1.0")]
    [ApiVersion("2.0")]
    [ApiVersion("3.0")]
    [Route("api/[controller]")]
    [ApiController]
    [Authorize]
    public class BulkOperationsController(
    IPortalUserRepository portalUserRepo,
    ITeamsRepository teamsRepository,
    IAuth0UserManagementService userManager,
    IUserInviterService userInviterService) : ControllerBase
    {
        [HttpPost("teams")]
        [AuthorizeWorkspace(Permissions.CreateWorkspace)]
        public async Task<ActionResult<Dictionary<string, int>>> CreateTeamsAsync(int workspaceId, IEnumerable<string> teamNames)
        {
            try
            {
                var teams = await teamsRepository.CreateTeamsAsync(workspaceId, teamNames);
                return Ok(teams.ToDictionary(t => t.Name, t => t.TeamId));
            }
            catch (Exception ex)
            {
                return StatusCode(StatusCodes.Status500InternalServerError, ex);
            }
        }

        [HttpPost("userTeamAssignments")]
        [AuthorizeWorkspace(Permissions.CreateWorkspace)]
        public async Task<ActionResult> AssignTeamSupervisors(IEnumerable<BulkTeamUser> teamSupervisorMap)
        {
            try
            {
                var teamUsers = teamSupervisorMap.Select(map => new TeamUser
                {
                    PortalUserId = map.PortalUserId,
                    TeamId = map.TeamId,
                });

                await teamsRepository.AssignPortalUsersToTeamsAsync(teamUsers);
                return Ok("Successfully assigned the users to teams");
            }
            catch (Exception ex)
            {
                return StatusCode(StatusCodes.Status500InternalServerError, ex);
            }
        }

        [HttpPost("users")]
        [AuthorizeWorkspace(Permissions.CreateWorkspace)]
        public async Task<List<BulkUserResponse>> CreateUsersAsync(int workspaceId, IEnumerable<BulkUserRequest> userCreateRecords)
        {
            var users = new Dictionary<string, BulkUserResponse>();

            foreach (var record in userCreateRecords)
            {
                string error = null;

                if (!users.ContainsKey(record.Email))
                {
                    users.Add(record.Email, new BulkUserResponse() { Email = record.Email });
                }

                try
                {
                    error = $"Error in creating Auth0 user {record.Email}";

                    // First create a user record in Auth0
                    var (user, passwordResetUrl) = await userManager.CreateAuth0UserAsync(record.Name, null, record.Email);

                    var portalUserRecord = new PortalUser
                    {
                        Email = record.Email,
                        FirstName = record.Name,
                        IdpUserId = user.UserId,
                        IsSanasUser = false
                    };

                    error = $"Failed to create the user in Portal - {record.Email}";

                    var portalUser = await portalUserRepo.CreatePortalUserAsync(portalUserRecord, workspaceId, ["customer:supervisor"]);

                    users[record.Email].UserId = portalUser.PortalUserId;

                    error = $"Failed to send the invite to user {record.Email}";

                    if (!string.IsNullOrWhiteSpace(passwordResetUrl))
                    {
                        userInviterService.SendWorkspaceInvitationToNewUser(portalUserRecord.Email, passwordResetUrl);
                    }

                    await Task.Delay(100);
                }
                catch (Exception e)
                {
                    users[record.Email].Error = error + Environment.NewLine + e;
                }
            }

            return [.. users.Values];
        }
    }
}

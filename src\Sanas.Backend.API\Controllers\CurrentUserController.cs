﻿using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sanas.Backend.API.Extensions;
using Sanas.Backend.BO.Dto;
using Sanas.Backend.BO.Dto.RequestDto;
using Sanas.Backend.BO.Handlers;

namespace Sanas.Backend.API.Controllers;

[ApiController]
[Authorize]
[Route("api/[controller]")]
[ApiVersion("1.0")]
[ApiVersion("2.0")]
[ApiVersion("3.0")]
[ApiVersion("4.0")]
public class CurrentUserController(
    IUsersHandler usersHandler,
    IRBACHandler rbacHndler) : ControllerBase
{
    [HttpGet("Profile")]
    public async Task<ActionResult<PortalUserDto>> GetProfileAsync()
    {
        try
        {
            var user = await usersHandler.GetByUserIdAsync(HttpContext.GetUserId());

            if (user is not null)
            {
                return new PortalUserDto
                {
                    FirstName = user.FirstName,
                    LastName = user.LastName,
                    Email = user.Email,
                    PortalUserId = user.PortalUserId,
                    Status = user.Status,
                };
            }

            return null;
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
    }

    [HttpPost("Profile")]
    public async Task<ActionResult> UpdateProfileAsync([FromBody] UserProfileUpdateDto updateDto)
    {
        try
        {
            await usersHandler.UpdateUserProfileAsync(HttpContext.GetUserId(), updateDto.FirstName, updateDto.LastName);
            return Ok("Profile successfully updated!");
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
    }

    [HttpPost("change-password")]
    public async Task<ActionResult> UpdatePasswordAsync([FromBody] string newPassword)
    {
        try
        {
            await usersHandler.UpdateUserPasswordAsync(HttpContext.GetUserId(), newPassword?.Trim());
            return Ok("Profile successfully updated!");
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
    }

    [HttpGet("scopes")]
    public async Task<IEnumerable<UserScope>> GetCurrentUserScopes()
    {
        var userId = HttpContext.GetUserId();
        var user = await usersHandler.GetByUserIdAsync(userId);

        if (user is not null)
        {
            var scopes = await rbacHndler.GetUserWorkspaceScopesAsync(user.PortalUserId, user.IsSanasUser);

            foreach (var scope in scopes)
            {
                if (scope.WorkspaceId > 0)
                {
                    var result = await usersHandler.GetAccessibleTeamsAsync(scope.WorkspaceId, HttpContext.GetUserId());
                    scope.TeamIds = result.ToList();
                }
            }

            return scopes;
        }

        return [];
    }

    [HttpPatch("NotificationToken")]
    public async Task<ActionResult> UpdateNotificationTokenAsync([FromBody] string token)
    {
        try
        {
            await usersHandler.UpdateNotificationTokenAsync(HttpContext.GetUserId(), token);
            return Ok();
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
    }
}

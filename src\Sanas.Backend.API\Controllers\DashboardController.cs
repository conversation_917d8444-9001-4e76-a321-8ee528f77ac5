using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sanas.Backend.API.Authorization;
using Sanas.Backend.API.Helpers;
using Sanas.Backend.BO.Dto.Dashboards;
using Sanas.Backend.BO.Dto.Enums;
using Sanas.Backend.BO.Helpers;
using Sanas.Backend.BO.Services.Interfaces;

namespace Sanas.Backend.API.Controllers;

[Route("api/dashboard")]
[ApiController]
[Authorize]
[ApiVersion("1.0")]
[ApiVersion("2.0")]
[ApiVersion("3.0")]
public class DashboardController(
    ILogger<DashboardController> logger,
    INestService nestService,
    CurrentUser currentUser
) : ControllerBase
{
    private static string BuildEncodedUrl(
        int userId,
        int[] workspaceIds,
        DateTime? lastDateTime,
        string modelType,
        int timeZone = 0,
        string multi = "no",
        DurationTypes duration = DurationTypes.OneMonth)
    {
        var queryParams = new List<string> { $"userId={Uri.EscapeDataString(userId.ToString())}" };

        if (workspaceIds != null && workspaceIds.Length > 0)
        {
            queryParams.AddRange(workspaceIds.Select(id => $"workspaceIds={Uri.EscapeDataString(id.ToString())}"));
        }

        if (lastDateTime.HasValue)
        {
            queryParams.Add($"lastDateTime={Uri.EscapeDataString(lastDateTime.Value.ToString("o"))}"); // "o" is the round-trip date/time pattern (ISO 8601)
        }

        if (!string.IsNullOrWhiteSpace(modelType))
        {
            queryParams.Add($"modelType={modelType}");
        }

        queryParams.Add($"timeZone={Uri.EscapeDataString(timeZone.ToString())}");
        queryParams.Add($"duration={(int)duration}");
        queryParams.Add($"multi={multi}");
        return $"?{string.Join("&", queryParams)}";
    }

    [HttpGet("all")]
    public async Task<ActionResult> GetDashboardAll(
        [FromQuery] int[] workspaceIds,
        [FromQuery] DateTime? lastDateTime,
        [FromQuery] string modelType,
        [FromQuery] int timeZone = 0,
        [FromQuery] string multi = "no",
        [FromQuery] DurationTypes duration = DurationTypes.OneMonth)
    {
        try
        {
            var user = await currentUser.GetUserIdAsync();
            var endpoint = $"api/Workspaces/Agents/graph/all/{BuildEncodedUrl(user, workspaceIds, lastDateTime, modelType, timeZone, multi, duration)}";
            var response = await nestService.GetFromNestServerAsync(endpoint);
            string contentType = response.Content.Headers.ContentType?.ToString() ?? "application/json";
            string content = await response.Content.ReadAsStringAsync();
            return new ContentResult
            {
                Content = content,
                StatusCode = (int)response.StatusCode,
                ContentType = contentType
            };
        }
        catch (HttpRequestException e)
        {
            logger.LogError("Error :: {Error}", e);
            if (e.StatusCode != null)
            {
                return StatusCode((int)e.StatusCode, e.Message);
            }

            return StatusCode(StatusCodes.Status500InternalServerError, e.Message);
        }
        catch (Exception e)
        {
            logger.LogError("Error in fetching dashboard data. {Error}", e);
            return StatusCode(StatusCodes.Status500InternalServerError, $"Error in fetching agent engage dashboard");
        }
    }
}
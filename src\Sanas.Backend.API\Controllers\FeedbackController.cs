﻿using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sanas.Backend.API.Authorization;
using Sanas.Backend.BO.Dto;
using Sanas.Backend.BO.Dto.RequestDto;
using Sanas.Backend.BO.Dto.ResponseDto;
using Sanas.Backend.BO.Handlers;
using Sanas.Backend.BO.Handlers.Interfaces;
using Sanas.Backend.BO.Helpers;

namespace Sanas.Backend.API.Controllers;

[Route("api/[controller]")]
[ApiController]
[Authorize]
[ApiVersion("1.0")]
[ApiVersion("2.0")]
[ApiVersion("3.0")]
[ApiVersion("4.0")]
public class FeedbackController(
    ILogger<FeedbackController> logger,
    IFeedbackHandler feedbackHander,
    IAgentsHandler agentsHandler,
    IAppEventHandler appEventHandler) : ControllerBase
{
    private const string AccessTokenHeader = "X-SANAS-ACCESS-TOKEN";

    [HttpPost("/api/[controller]")]
    [AllowAnonymous]
    public async Task<ActionResult> CreateFeedbackAsync(
        FeedbackCreateDto createDto,
        [FromHeader(Name = AccessTokenHeader)] string token)
    {
        if (!Crypto.VerifyTokenAuthorization(token, createDto.LoginId))
        {
            logger.LogWarning("Unauthorized attempt to create feedback detected.");
            return Unauthorized();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest();
        }

        try
        {
            var agent = await agentsHandler.GetByAgentIdAsync(createDto.FeedbackDetail.AgentId);

            if (agent is null)
            {
                return NotFound();
            }

            if (agent.IsDisabled)
            {
                return Forbid();
            }

            var res = await feedbackHander.CreateFeedbackAsync(createDto.FeedbackDetail, createDto.LoginId);

            await appEventHandler.OnEventReceivedFromApp(agent, NotificationType.FeedbackSubmitted, createDto.FeedbackDetail.Description);

            return Ok(res);
        }
        catch (ArgumentException e)
        {
            return NotFound(e.Message);
        }
    }

    [HttpGet]
    [AuthorizeWorkspace(Permissions.ReadWorkspaces)]
    public async Task<ActionResult<PagedDto<FeedbackDto>>> GetFeedbacksAsync([FromRoute] int workspaceId, [FromQuery] RequestOptions queryOptions)
    {
        try
        {
            return await feedbackHander.GetFeedbacksByWorkspaceIdAsync(workspaceId, queryOptions);
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
    }
}
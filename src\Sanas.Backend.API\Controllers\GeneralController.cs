﻿using System.Text.Json;
using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sanas.Backend.API.Authorization;
using Sanas.Backend.API.Extensions;
using Sanas.Backend.BO.Dto;
using Sanas.Backend.BO.Handlers;
using Sanas.Backend.BO.Helpers;

namespace Sanas.Backend.API.Controllers;

[Route("api/[controller]")]
[AllowAnonymous]
[ApiController]
[ApiVersion("1.0")]
[ApiVersion("2.0")]
[ApiVersion("3.0")]
[ApiVersion("4.0")]
public class GeneralController : ControllerBase
{
    private readonly ILogger<GeneralController> _logger;
    private readonly IGenericHandler _genericHandler;

    public GeneralController(ILogger<GeneralController> logger, IGenericHandler genericHandler)
    {
        _logger = logger;
        _genericHandler = genericHandler;
    }

    [HttpGet("icp")]
    public async Task<ActionResult<CompatibilityChecklist>> GetIdealCustomerProfileAsync()
    {
        var icpChecklist = await _genericHandler.GetICPAsync();

        if (!icpChecklist.IsValidJson())
        {
            return null;
        }

        var result = JsonSerializer.Deserialize<CompatibilityChecklist>(icpChecklist);
        Response.Headers.Append("X-ResponseSignature", Crypto.SignWithPrivateKey(result));

        return Ok(result);
    }

    [HttpPost("icp")]
    [AuthorizeWorkspace(Permissions.CreateWorkspace)]
    public async Task<ActionResult> UpdateIdealCustomerProfileAsync([FromBody] CompatibilityChecklist icpChecklist)
    {
        try
        {
            await _genericHandler.UpdateICPAsync(icpChecklist);
            return Ok("Successfully updated the ICP checklist.");
        }
        catch (Exception e)
        {
            _logger.LogError("Failed to update ICP checklist. {Error}", e);
            return StatusCode(StatusCodes.Status500InternalServerError, "Failed to update ICP checklist");
        }
    }
}

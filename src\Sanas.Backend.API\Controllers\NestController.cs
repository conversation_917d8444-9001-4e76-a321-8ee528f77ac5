﻿using System.Text.Json;
using System.Web;
using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sanas.Backend.API.Authorization;
using Sanas.Backend.BO.Helpers;
using Sanas.Backend.BO.Services.Interfaces;

namespace Sanas.Backend.API.Controllers;

[ApiController]
[ApiVersion("1.0")]
[ApiVersion("2.0")]
[ApiVersion("3.0")]
[ApiVersion("4.0")]
[Authorize]
[Route("api/[Controller]")]
public class NestController(
    ILogger<NestController> logger,
    INestService service
) : ControllerBase
{
    private static async Task<(string Content, int StatusCode)> TransformHttpResponse(HttpResponseMessage response)
    {
        string content = await response.Content.ReadAsStringAsync();
        return (content, (int)response.StatusCode);
    }

    [HttpPost("{endpoint}")]
    public async Task<IActionResult> PostToNestServerAsync(string endpoint, [FromBody] JsonElement request)
    {
        try
        {
            var response = await service.PostToNestServerAsync(HttpUtility.UrlDecode(endpoint), request);
            string contentType = response.Content.Headers.ContentType?.ToString() ?? "application/json";
            var (content, statusCode) = await TransformHttpResponse(response);
            return new ContentResult
            {
                Content = content,
                StatusCode = statusCode,
                ContentType = contentType
            };
        }
        catch (HttpRequestException e)
        {
            logger.LogError("Error :: {Error}", e);
            return StatusCode(500, e.Message);
        }
    }

    [HttpGet("{endpoint}")]
    public async Task<IActionResult> GetFromNestServerAsync(string endpoint)
    {
        try
        {
            var response = await service.GetFromNestServerAsync(HttpUtility.UrlDecode(endpoint));

            var (content, statusCode) = await TransformHttpResponse(response);
            
            if (!response.IsSuccessStatusCode)
            {
                throw new HttpRequestException(content,null,response.StatusCode);
            }
            
            response.EnsureSuccessStatusCode();
            
            string contentType = response.Content.Headers.ContentType?.ToString() ?? "application/json";

            if ((contentType.StartsWith("text/") && contentType != "text/csv") || contentType == "application/json")
            {
                return new ContentResult
                {
                    Content = content,
                    StatusCode = statusCode,
                    ContentType = contentType
                };
            }

            string fileName = "downloadedFile";
            if (response.Content.Headers.ContentDisposition != null)
            {
                fileName = response.Content.Headers.ContentDisposition.FileName?.Trim('"') ?? fileName;
            }

            var fileBytes = await response.Content.ReadAsByteArrayAsync();
            return File(fileBytes, contentType, fileName);
        }
        catch (HttpRequestException e)
        {
            logger.LogError("Error :: {Error}", e.Message);
            return StatusCode((int)e.StatusCode, e.Message);
        }
    }
}
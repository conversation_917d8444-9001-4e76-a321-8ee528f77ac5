﻿using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sanas.Backend.API.Helpers;
using Sanas.Backend.BO.Dto;
using Sanas.Backend.BO.Dto.RequestDto;
using Sanas.Backend.BO.Dto.ResponseDto;
using Sanas.Backend.BO.Handlers;

namespace Sanas.Backend.API.Controllers;

[Authorize]
[ApiController]
[Route("api/Workspaces/{workspaceId}/[controller]")]
[ApiVersion("1.0")]
[ApiVersion("2.0")]
[ApiVersion("3.0")]
[ApiVersion("4.0")]
public class NotificationsController(CurrentUser currentUser, INotificationsHandler notificationsHandler) : ControllerBase
{
    [HttpGet]
    public async Task<ActionResult<PagedDto<NotificationDto>>> GetNotificationsAsync([FromRoute] int workspaceId, [FromQuery] RequestOptions queryOptions)
    {
        try
        {
            var filters = new NotificationQueryFilters
            {
                NotificationTypes = Request.Query["Type"],
                LoginIds = Request.Query["LoginId"]
            };

            return await notificationsHandler.GetByUserId(workspaceId, await currentUser.GetUserIdAsync(), queryOptions, filters);
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
    }

    [HttpPost("mark-all-read")]
    public async Task<ActionResult> MarkAllAsRead([FromRoute] int workspaceId)
    {
        try
        {
            var types = Request.Query["Type"];
            await notificationsHandler.MarkAllAsReadAsync(workspaceId, await currentUser.GetUserIdAsync(), types);
            return Ok();
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
    }

    [HttpPost("{notificationId}/mark-as-read")]
    public async Task<ActionResult> MarkAsRead(int notificationId)
    {
        try
        {
            await notificationsHandler.ToggleMarkAsRead(notificationId, await currentUser.GetUserIdAsync(), false);
            return Ok();
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
    }

    [HttpPost("{notificationId}/mark-as-unread")]
    public async Task<ActionResult> MarkAsUnRead(int notificationId)
    {
        try
        {
            await notificationsHandler.ToggleMarkAsRead(notificationId, await currentUser.GetUserIdAsync(), true);
            return Ok();
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
    }
}

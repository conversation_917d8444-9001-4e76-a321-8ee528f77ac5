﻿using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sanas.Backend.API.Authorization;
using Sanas.Backend.API.Helpers;
using Sanas.Backend.BO.Handlers;
using Sanas.Backend.BO.Helpers;

namespace Sanas.Backend.API.Controllers;

[ApiController]
[Authorize]
[Route("api/workspaces/{workspaceId}/[controller]")]
[ApiVersion("1.0")]
[ApiVersion("2.0")]
[ApiVersion("3.0")]
[ApiVersion("4.0")]
public class PortalUsersController(IUsersHandler userHandler, CurrentUser currentUser) : ControllerBase
{
    [HttpHead("check-email")]
    [AuthorizeWorkspace(Permissions.ReadPortalUsers)]
    public async Task<ActionResult> EmailExists([FromRoute] int workspaceId, string email)
    {
        if (await userHandler.EmailExists(email, workspaceId) == false)
        {
            return NotFound();
        }

        return StatusCode(StatusCodes.Status204NoContent);
    }

    [HttpPost("{portalUserId}/enable")]
    [AuthorizeWorkspace(Permissions.UpdatePortalUser)]
    public async Task<ActionResult> EnableUserAsync([FromRoute] int workspaceId, int portalUserId)
    {
        if (!await currentUser.HasPermissionsAsync(workspaceId, [Permissions.CreateWorkspace]))
        {
            var userWorkspaces = await userHandler.GetWorkspaceIdsForUserAsync(portalUserId, includeDisabled: true);

            // if (await currentUser.HasAccessToWorkspacesAsync(userWorkspaces))
            if (!userWorkspaces.Any(id => workspaceId == id))
            {
                return Forbid();
            }
        }

        await userHandler.EnableDisablePortalUserAsync(workspaceId, portalUserId, disable: false);
        return Ok($"Successfully enabled the user.");
    }

    [HttpPost("{portalUserId}/disable")]
    [AuthorizeWorkspace(Permissions.UpdatePortalUser)]
    public async Task<ActionResult> DisableUserAsync([FromRoute] int workspaceId, int portalUserId)
    {
        var currentUserId = await currentUser.GetUserIdAsync();

        if (currentUserId == portalUserId)
        {
            return BadRequest("You are not allowed to disable your own account.");
        }

        if (!await currentUser.HasPermissionsAsync(workspaceId, [Permissions.CreateWorkspace]))
        {
            var userWorkspaces = await userHandler.GetWorkspaceIdsForUserAsync(portalUserId, includeDisabled: true);

            // if (await currentUser.HasAccessToWorkspacesAsync(userWorkspaces))
            if (!userWorkspaces.Any(id => workspaceId == id))
            {
                return Forbid();
            }
        }

        await userHandler.EnableDisablePortalUserAsync(workspaceId, portalUserId, disable: true);
        return Ok($"Successfully disabled the user.");
    }

    [HttpPost("{portalUserId}/send-invite")]
    [AuthorizeWorkspace(Permissions.CreatePortalUser)]
    public async Task<ActionResult> SendVerificationEmailAsync([FromRoute] int workspaceId, int portalUserId)
    {
        try
        {
            await userHandler.ResendInviteAsync(workspaceId, portalUserId);
            return Ok("Successfully sent the invite.");
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
    }

    [HttpPost("{portalUserId}/cancel-invite")]
    [AuthorizeWorkspace(Permissions.CreatePortalUser)]
    public async Task<ActionResult> CancelUserInviteAsync([FromRoute] int workspaceId, int portalUserId)
    {
        try
        {
            await userHandler.CancelUserInviteAsync(workspaceId, portalUserId);
            return Ok("Successfully canceled the invite.");
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
    }
}
using System.Text;
using Asp.Versioning;
using Microsoft.AspNetCore.Mvc;
using Sanas.Backend.API.Extensions;
using Sanas.Backend.BO.Dto;
using Sanas.Backend.BO.Handlers;
using Sanas.Backend.BO.Helpers;
using Sanas.Backend.BO.Services.Interfaces;

namespace Sanas.Backend.API.Controllers;

[Route("api/workspaces/{workspaceId}/[Controller]")]
[ApiController]
[ApiVersion("1.0")]
[ApiVersion("2.0")]
[ApiVersion("3.0")]
[ApiVersion("4.0")]
public class ReportsController(
    ILogger<ReportsController> logger,
    IRBACHandler r<PERSON>,
    IUsersHandler usersHandler,
    IBnsService bnsService) : ControllerBase
{
    [HttpGet("agent-usage")]
    public async Task<IActionResult> Get(int workspaceId, [FromQuery] UsageReportsFiltersDto filters)
    {
        var workspaceIds = filters.WorkspaceIds ?? [workspaceId];
        workspaceIds.Add(workspaceId);
        var targetWorkspaceIds = workspaceIds.Distinct().Where(id => id != 0).ToList();

        if (!await CheckWorkspaceAuthorization(targetWorkspaceIds))
        {
            return Unauthorized("You do not have access to one or more workspace Ids specified in the request.");
        }

        try
        {
            var queryString = BuildQueryString(targetWorkspaceIds, filters);

            HttpResponseMessage response = await bnsService.GetAgentUsageReportAsync(workspaceId, queryString, download: false);
            string content = await response.Content.ReadAsStringAsync();

            string contentType = response.Content.Headers.ContentType?.ToString() ?? "application/json";

            return new ContentResult
            {
                Content = content,
                StatusCode = (int)response.StatusCode,
                ContentType = contentType
            };
        }
        catch (HttpRequestException e)
        {
            logger.LogError("Error :: {Error}", e);
            return StatusCode(500, e.Message);
        }
    }

    [HttpGet("agent-usage/download")]
    public async Task<IActionResult> Download(int workspaceId, [FromQuery] UsageReportsFiltersDto filters)
    {
        var workspaceIds = filters.WorkspaceIds ?? [workspaceId];
        workspaceIds.Add(workspaceId);
        var targetWorkspaceIds = workspaceIds.Distinct().Where(id => id != 0).ToList();

        if (!await CheckWorkspaceAuthorization(targetWorkspaceIds))
        {
            return Unauthorized("You do not have access to one or more workspace Ids specified in the request.");
        }

        try
        {
            var queryString = BuildQueryString(targetWorkspaceIds, filters);

            HttpResponseMessage response = await bnsService.GetAgentUsageReportAsync(workspaceId, queryString, download: true);
            string content = await response.Content.ReadAsStringAsync();

            string contentType = response.Content.Headers.ContentType?.ToString() ?? "application/json";

            return new ContentResult
            {
                Content = content,
                StatusCode = (int)response.StatusCode,
                ContentType = contentType
            };
        }
        catch (HttpRequestException e)
        {
            logger.LogError("Error :: {Error}", e);
            return StatusCode(500, e.Message);
        }
    }

    public static StringBuilder BuildQueryString(IEnumerable<int> targetWorkspaceIds, [FromQuery] UsageReportsFiltersDto filters)
    {
        var queryString = new StringBuilder(string.Join('&', targetWorkspaceIds.Select(id => $"workspaceIds={id}")));

        if (filters.AgentIds is not null)
        {
            var agentIdsParam = string.Join('&', filters.AgentIds.Where(id => id != 0).Distinct().Select(id => $"agentIds={id}"));
            queryString.Append($"&{agentIdsParam}");
        }

        if (filters.Page.HasValue)
        {
            queryString.Append($"&page={filters.Page.Value}");
        }

        if (filters.PerPage.HasValue)
        {
            queryString.Append($"&perPage={filters.PerPage.Value}");
        }

        if (!string.IsNullOrWhiteSpace(filters.SearchText))
        {
            queryString.Append($"&searchText={filters.SearchText}");
        }

        if (!string.IsNullOrWhiteSpace(filters.FromDate))
        {
            queryString.Append($"&fromDate={filters.FromDate}");
        }

        if (!string.IsNullOrWhiteSpace(filters.ToDate))
        {
            queryString.Append($"&toDate={filters.ToDate}");
        }

        return queryString;
    }

    private async Task<bool> CheckWorkspaceAuthorization(IEnumerable<int> workspaceIds)
    {
        var currentUser = await usersHandler.GetByUserIdAsync(HttpContext.GetUserId());
        if (currentUser is not null)
        {
            if (!currentUser.IsSanasUser)
            {
                var validWorkspaceIds = new List<int>();

                var userScopes = await rbacHandler.GetUserWorkspaceScopesAsync(currentUser.PortalUserId, isSanasUser: false);

                var userWorkspaces = userScopes
                    .Where(scope => scope.WorkspaceId != 0 && scope.Permissions.Contains(Permissions.ReadWorkspaceReports))
                    .Select(scope => scope.WorkspaceId);

                if (workspaceIds.Except(userWorkspaces).Any())
                {
                    return false;
                }
            }

            return true;
        }

        return false;
    }
}
﻿using System.Net.Mail;
using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sanas.Backend.API.Authorization;
using Sanas.Backend.BO.Dto;
using Sanas.Backend.BO.Dto.PortalUsers;
using Sanas.Backend.BO.Dto.RequestDto;
using Sanas.Backend.BO.Dto.ResponseDto;
using Sanas.Backend.BO.Exceptions;
using Sanas.Backend.BO.Handlers;
using Sanas.Backend.BO.Helpers;

namespace Sanas.Backend.API.Controllers;

[Route("api/[controller]")]
[ApiController]
[Authorize]
[ApiVersion("1.0")]
[ApiVersion("2.0")]
[ApiVersion("3.0")]
[ApiVersion("4.0")]
public class SanasUsersController(ISanasUserHandler sanasUserHandler, IRBACHandler rbacHandler) : ControllerBase
{
    [HttpGet]
    [AuthorizeWorkspace(Permissions.ReadPortalUsers, Permissions.ReadAllWorkspaces)]
    public async Task<PagedDto<PortalUserDto>> GetAllSanasUsersAsync([FromQuery] RequestOptions queryOptions)
    {
        return await sanasUserHandler.GetAllWithPagingAsync(queryOptions);
    }

    [HttpGet("{userId}")]
    [AuthorizeWorkspace(Permissions.ReadPortalUsers, Permissions.ReadAllWorkspaces)]
    public async Task<ActionResult<PortalUserGetDto>> GetSanasUserAsync(int userId)
    {
        var record = await sanasUserHandler.GetByUserIdAsync(userId);

        if (record == null)
        {
            return NotFound($"No portal user found for ID {userId}");
        }

        return record;
    }

    [HttpPost]
    [AuthorizeWorkspace(Permissions.CreatePortalUser, Permissions.CreateWorkspace)]
    public async Task<IActionResult> CreateSanasUserAsync(PortalUserCreateDto portalUser)
    {
        try
        {
            var mailAddress = new MailAddress(portalUser.Email?.Trim());
            if (!mailAddress.Host.Equals("sanas.ai", StringComparison.OrdinalIgnoreCase))
            {
                return BadRequest($"Invalid email: {mailAddress.Host} is not a valid domain for sanas user email.");
            }

            if (portalUser.Roles.Count == 0)
            {
                return BadRequest("No role specified for the new user.");
            }

            var sanasRoles = (await rbacHandler.GetRolesAsync()).SanasRoles;
            foreach (var invitedRole in portalUser.Roles)
            {
                if (invitedRole.RoleId > 0)
                {
                    var role = sanasRoles.FirstOrDefault(sanasRole => sanasRole.RoleId == invitedRole.RoleId);
                    if (role is not null)
                    {
                        invitedRole.Name = role.Name;
                        continue;
                    }
                }
                else if (!string.IsNullOrWhiteSpace(invitedRole.Name))
                {
                    var role = sanasRoles.FirstOrDefault(sanasRole => string.Equals(sanasRole.Name, invitedRole.Name.Trim(), StringComparison.OrdinalIgnoreCase));
                    if (role is not null)
                    {
                        invitedRole.RoleId = role.RoleId;
                        continue;
                    }
                }

                return BadRequest("One or more invites contain invalid role");
            }

            var record = await sanasUserHandler.CreateSanasUserAsync(portalUser);
            return CreatedAtAction(nameof(GetSanasUserAsync), new { UserId = record.PortalUserId }, record);
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
        catch (PortalException e)
        {
            if (e.ErrorCode == PortalErrorCodes.NonSanasUserFound)
            {
                return Conflict(e.Message);
            }

            throw;
        }
    }

    [HttpPost("{portalUserId}/enable")]
    [AuthorizeWorkspace(Permissions.CreatePortalUser, Permissions.CreateWorkspace)]
    public async Task<ActionResult> EnableAsync(int portalUserId)
    {
        try
        {
            await sanasUserHandler.EnableDisableSanasUserAsync(portalUserId, disable: false);
            return Ok();
        }
        catch (Exception e)
        {
            return BadRequest(e.Message);
        }
    }

    [HttpPost("{portalUserId}/disable")]
    [AuthorizeWorkspace(Permissions.CreatePortalUser, Permissions.CreateWorkspace)]
    public async Task<ActionResult> DisableAsync(int portalUserId)
    {
        try
        {
            await sanasUserHandler.EnableDisableSanasUserAsync(portalUserId, disable: true);
            return Ok();
        }
        catch (Exception e)
        {
            return BadRequest(e.Message);
        }
    }

    [HttpPost("update-role")]
    [AuthorizeWorkspace(Permissions.UpdatePortalUser)]
    public async Task<ActionResult> UpdateUserRoleAsync(UserRoleUpdateDto request)
    {
        try
        {
            if (request.PortalUserId <= 0 || string.IsNullOrWhiteSpace(request.RoleName))
            {
                return BadRequest("Request contains invalid inputs.");
            }

            await rbacHandler.UpdateSanasUserRoleAsync(request);

            return Ok("Successfully updated the Sanas user role.");
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
    }
}

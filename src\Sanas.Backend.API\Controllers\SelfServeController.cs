﻿using System.Text.Json;
using Asp.Versioning;
using Microsoft.AspNetCore.Mvc;
using Sanas.Auth0Client.Interfaces;
using Sanas.Backend.BO.Dto;
using Sanas.Backend.BO.Handlers;

namespace Sanas.Backend.API.Controllers
{
    [Route("api/[controller]")]
    [ApiController]
    [ApiVersion("1.0")]
    [ApiVersion("2.0")]
    [ApiVersion("3.0")]
    public class SelfServeController(
        IAuth0UserManagementService userService,
        ILogger<SelfServeController> logger,
        IWorkspaceHandler workspaceHandler,
        IUsersHandler usersHandler,
        IHttpClientFactory httpClientFactory,
        IConfiguration configuration,
        IAgentsHandler agentsHandler) : ControllerBase
    {
        [HttpPost("create-user")]
        public async Task<ActionResult> CreateUserAsync([FromBody] NCUser user)
        {
            try
            {
                if (await usersHandler.GetByUserEmailAsync(user.Email) is not null)
                {
                    return Conflict($"Another user exists with email {user.Email}");
                }

                var enterpriseName = await workspaceHandler.GenerateEnterpriseNameFromEmailAsync(user.Email);

                var newWorkspace = await workspaceHandler.CreateWorkspaceAsync(new BO.Dto.RequestDto.WorkspaceCreateDto
                {
                    Enterprise = enterpriseName,
                    BPO = "_NA",
                    Site = "Global",
                    CountryCode = "IND",
                    AllocatedSeats = user.AllocatedSeats,
                    ModelTypes = ["NC"],
                    UserInvites = []
                });

                var auth0User = await userService.CreateVerifiedAuth0UserAsync(user.FirstName, user.LastName, user.Email, user.Password);

                var portalNCUser = await usersHandler.CreateNCUserAsync(newWorkspace.WorkspaceId, auth0User.UserId, user);

                await agentsHandler.CreateManyAgentsAsync(newWorkspace.WorkspaceId, new BO.Dto.RequestDto.AgentsCreateDto
                {
                    TeamId = (await workspaceHandler.GetDefaultTeamAsync(newWorkspace.WorkspaceId))?.TeamId,
                    Agents = [new AgentCreds { LoginId = user.Email, Password = user.Password, Name = $"{user.FirstName} {user.LastName}" }]
                });

                return Created();
            }
            catch (ArgumentException e)
            {
                return BadRequest(e.Message);
            }
            catch (Exception e)
            {
                logger.LogError("Failed to automatically provision user and workspace: {Error}", e);
                return StatusCode(StatusCodes.Status500InternalServerError, "Failed to create user.");
            }
        }

        [HttpPost("authenticate")]
        public async Task<ActionResult<HttpResponseMessage>> AuthenticateUserAsync([FromBody] Credentials credentials)
        {
            var httpClient = httpClientFactory.CreateClient("auth0");

            var requestBody = new FormUrlEncodedContent(
            [
                new KeyValuePair<string, string>("grant_type", "password"),
                new KeyValuePair<string, string>("username", credentials.Email?.Trim()),
                new KeyValuePair<string, string>("password", credentials.Password?.Trim()),
                new KeyValuePair<string, string>("audience", configuration["Auth0:Audience"]),
                new KeyValuePair<string, string>("client_id", configuration["Swaggerclient:ClientId"]),
                new KeyValuePair<string, string>("client_secret", configuration["Swaggerclient:ClientSecret"]),
                new KeyValuePair<string, string>("scope", "openid offline_access")
            ]);

            try
            {
                var response = await httpClient.PostAsync("/oauth/token", requestBody);
                string content = await response.Content.ReadAsStringAsync();

                if (response.IsSuccessStatusCode)
                {
                    AuthResponse authResponse = JsonSerializer.Deserialize<AuthResponse>(content);
                    authResponse.Audience = configuration["Auth0:Audience"];
                    authResponse.ClientId = configuration["Swaggerclient:ClientId"];

                    content = JsonSerializer.Serialize(authResponse);
                }

                string contentType = response.Content.Headers.ContentType?.ToString() ?? "application/json";

                return new ContentResult
                {
                    Content = content,
                    StatusCode = (int)response.StatusCode,
                    ContentType = contentType
                };
            }
            catch (HttpRequestException e)
            {
                logger.LogError("Error :: {Error}", e);
                return StatusCode(500, e.Message);
            }
        }
    }
}

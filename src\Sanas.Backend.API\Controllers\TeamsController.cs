﻿using Asp.Versioning;
using Microsoft.AspNetCore.Mvc;
using Sanas.Backend.API.Authorization;
using Sanas.Backend.API.Helpers;
using Sanas.Backend.BO.Dto;
using Sanas.Backend.BO.Dto.Reports;
using Sanas.Backend.BO.Dto.RequestDto;
using Sanas.Backend.BO.Dto.ResponseDto;
using Sanas.Backend.BO.Dto.Team;
using Sanas.Backend.BO.Handlers;
using Sanas.Backend.BO.Helpers;

namespace Sanas.Backend.API.Controllers;

[Route("api/workspaces/{workspaceId}/[controller]")]
[ApiController]
[ApiVersion("1.0")]
[ApiVersion("2.0")]
[ApiVersion("3.0")]
[ApiVersion("4.0")]
public class TeamsController(
    ILogger<TeamsController> logger,
    ITeamsHandler teamsHandler,
    CurrentUser currentUser) : ControllerBase
{
    [HttpPost]
    [AuthorizeWorkspace(Permissions.CreateTeam)]
    public async Task<ActionResult> CreateTeamAsync([FromRoute] int workspaceId, TeamCreateDto team)
    {
        if (!TeamCreateDto.IsValid(workspaceId, team, out string errorMsg))
        {
            logger.LogInformation("Invalid request to create team.");
            return BadRequest(errorMsg);
        }

        try
        {
            if (team.Agents.Count > 0 && !await currentUser.HasPermissionsAsync(workspaceId, [Permissions.CreateAppUser]))
            {
                return Forbid();
            }

            var newRecord = await teamsHandler.CreateTeamAsync(workspaceId, team);

            return CreatedAtAction(nameof(GetTeamByIdAsync), new { newRecord.WorkspaceId, newRecord.TeamId }, newRecord);
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
    }

    [HttpGet("{teamId}")]
    [AuthorizeWorkspace(Permissions.ReadTeams)]
    public async Task<ActionResult<TeamDto>> GetTeamByIdAsync([FromRoute] int workspaceId, int teamId)
    {
        var record = await teamsHandler.GetByTeamIdAsync(workspaceId, teamId);
        if (record == null || record.IsDisabled)
        {
            return NotFound($"The team is either disabled or not found");
        }

        return record;
    }

    [HttpPut("{teamId}")]
    [AuthorizeWorkspace(Permissions.UpdateTeam)]
    public async Task<ActionResult> UpdateTeamAsync([FromRoute] int workspaceId, int teamId, [FromBody] TeamUpdateDto team)
    {
        if (!TeamUpdateDto.IsValid(workspaceId, team, out string errorMsg))
        {
            return BadRequest(errorMsg);
        }

        if (!await currentUser.HasAccessToTeamsAsync(workspaceId, [teamId]))
        {
            logger.LogWarning("The logged in user does not have permissions to modify the specified team.");

            return Forbid();
        }

        await teamsHandler.UpdateTeamAsync(workspaceId, teamId, team);

        return Accepted();
    }

    [HttpGet("{teamId}/agents")]
    [AuthorizeWorkspace(Permissions.ReadAppUsers)]
    public async Task<ActionResult<PagedDto<AgentDto>>> GetAgentsInTeamAsync([FromRoute] int workspaceId, int teamId, [FromQuery] RequestOptions queryOptions)
    {
        try
        {
            var appStatusFilters = Request.Query["AppStatus"].ToList();
            var licenseStatusFilters = Request.Query["License"].ToList();
            var voices = Request.Query["Voice"].ToList();
            var appVersions = Request.Query["AppVersion"].ToList();
            var icpStatuses = Request.Query["IcpStatus"].Select(s => int.TryParse(s, out int status) ? status : -1);

            var agentFilters = new AgentQueryFilters
            {
                AppStatuses = appStatusFilters,
                LicenseStatuses = licenseStatusFilters,
                AppVersions = appVersions,
                Voices = voices,
                IcpStatuses = icpStatuses
            };

            return await teamsHandler.GetAgentsInTeamWithPagingAsync(workspaceId, teamId, queryOptions, agentFilters);
        }
        catch(ArgumentException e)
        {
            return BadRequest(e.Message);
        }
    }

    [HttpPatch("disable/{teamId}")]
    [AuthorizeWorkspace(Permissions.UpdateTeam)]
    public async Task<ActionResult> DisableTeam([FromRoute] int workspaceId, int teamId)
    {
        try
        {
            await teamsHandler.EnableOrDisableTeamAsync(workspaceId, teamId, true);
            return Ok();
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
    }

    [HttpPatch("reactivate/{teamId}")]
    [AuthorizeWorkspace(Permissions.UpdateTeam)]
    public async Task<ActionResult> ReactivateTeam([FromRoute] int workspaceId, int teamId)
    {
        try
        {
            await teamsHandler.EnableOrDisableTeamAsync(workspaceId, teamId, false);
            return Ok();
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
    }

    [HttpPatch("set-as-default/{teamId}")]
    [AuthorizeWorkspace(Permissions.UpdateTeam)]
    public async Task<ActionResult> SetAsDefaultTeamAsync([FromRoute] int workspaceId, int teamId)
    {
        try
        {
            await teamsHandler.SetAsDefaultTeamAsync(workspaceId, teamId);
            return Ok();
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
    }

    [HttpGet("stats/{teamId}")]
    public async Task<ActionResult<TeamStats>> GetTeamStats(int teamId, int? durationInHours = 8)
    {
        try
        {
            if (durationInHours > 120)
            {
                return BadRequest("Requested duration exceeds the maximum allowed value of 120 hours.");
            }

            TeamStats stats = await teamsHandler.GetTeamStatsAsync(teamId, durationInHours.Value);
            return Ok(stats);
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
    }

    [HttpGet("{teamId}/agents/download")]
    [AuthorizeWorkspace(Permissions.ReadTeamReports)]
    public async Task<ActionResult> DownloadReportAsync([FromRoute] int workspaceId, int teamId, [FromQuery] TeamsReportDownloadOptions queryOptions)
    {
        var record = await teamsHandler.GetByTeamIdAsync(workspaceId, teamId);

        if (record == null)
        {
            return NotFound();
        }

        var appStatusFilters = Request.Query["AppStatus"].ToList();
        var licenseStatusFilters = Request.Query["License"].ToList();
        var voices = Request.Query["Voice"].ToList();
        var appVersions = Request.Query["AppVersion"].ToList();
        var icpStatuses = Request.Query["IcpStatus"].Select(s => int.TryParse(s, out int status) ? status : -1);

        var agentFilters = new AgentQueryFilters
        {
            AppStatuses = appStatusFilters,
            LicenseStatuses = licenseStatusFilters,
            AppVersions = appVersions,
            Voices = voices,
            IcpStatuses = icpStatuses,
        };

        try
        {
            var reportBytes = await teamsHandler.DownloadAgentsReportAsync(workspaceId, teamId, queryOptions, agentFilters);

            return File(reportBytes, "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", $"Team Report.xlsx");
        }
        catch (Exception e)
        {
            logger.LogError("Failed to generate report. {Error}", e);
            return StatusCode(StatusCodes.Status500InternalServerError, "Failed to generate report.");
        }
    }
}

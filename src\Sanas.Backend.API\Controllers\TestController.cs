/*
    Note:
    This is a test action method to check if the AWS environment variables and secrets are working.
    This will be removed and replaced with a Health check end point later.
*/

using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sanas.Backend.API.Authorization;
using Sanas.Backend.BO.Helpers;

namespace Sanas.Backend.API.Controllers;

[ApiController]
[Route("api/[Controller]")]
[ApiExplorerSettings(IgnoreApi = true)]
[Authorize]
[ApiVersion("1.0")]
[ApiVersion("2.0")]
public class TestController(IConfiguration configuration) : ControllerBase
{
    private readonly IConfiguration _configuration = configuration;

    [HttpGet("config")]
    [AuthorizeWorkspace(Permissions.QueryConfigurations)]
    public Dictionary<string, string> GetConfigurations()
    {
        var results = new Dictionary<string, string>();

        try
        {
            var section = Request.Query["section"].FirstOrDefault();
            var entries = Request.Query["entry"];

            foreach (var entry in entries)
            {
                if (section is null)
                {
                    results.Add($"{entry}", _configuration[entry]);
                }
                else
                {
                    results.Add($"{section}:{entry}", _configuration[$"{section}:{entry}"]);
                }
            }
        }
        catch (Exception e)
        {
            results.Add("WEBSITE_URL", $"ERROR: {e.Message}");
        }

        return results;
    }

    [HttpGet("check")]
    [AllowAnonymous]
    public string TestEndpoint()
    {
        return "I'm ok";
    }
}

﻿using System.Text;
using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using Sanas.Backend.API.Authorization;
using Sanas.Backend.API.Helpers;
using Sanas.Backend.BO.Dto;
using Sanas.Backend.BO.Dto.Bulk;
using Sanas.Backend.BO.Dto.RequestDto;
using Sanas.Backend.BO.Handlers;
using Sanas.Backend.BO.Helpers;

namespace Sanas.Backend.API.Controllers.V2;

[ApiController]
[Authorize]
[Route("api/Workspaces/{workspaceId}/[Controller]")]
[ApiVersion("2.0")]
public partial class AgentsController(
    ILogger<AgentsController> logger,
    IAgentsHandler agentsHandler,
    IWorkspaceHandler workspaceHandler,
    CurrentUser currentUser,
    AgentsCsvParser csvParser,
    AgentsXlsxParser xlsxParser,
    IAppEventHandler notificationsHandler) : ControllerBase
{
    [HttpPost("many")]
    [AuthorizeWorkspace(Permissions.CreateAppUser)]
    public async Task<ActionResult> CreateManyAgentsAsync([FromRoute] int workspaceId, AgentsCreateDto request)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest();
        }

        if (!request.TeamId.HasValue)
        {
            request.TeamId = (await workspaceHandler.GetDefaultTeamAsync(workspaceId)).TeamId;
        }

        if (!await currentUser.HasAccessToTeamsAsync(workspaceId, [request.TeamId.Value]))
        {
            logger.LogWarning("{ActionName}{Error}", "CreateManyAgents", "User doe not have access to the team.");
            return Forbid();
        }

        try
        {
            await agentsHandler.CreateManyAgentsAsync(workspaceId, request);
            return Created();
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
    }

    [HttpGet("{agentId}")]
    [AuthorizeWorkspace(Permissions.ReadAppUsers)]
    public async Task<ActionResult<AgentDto>> GetAgentByIdAsync(int agentId)
    {
        var record = await agentsHandler.GetByAgentIdAsync(agentId);

        if (record == null || record.IsDisabled)
        {
            return BadRequest("User not found or disabled");
        }

        return Ok(record);
    }

    [HttpGet("check-id")]
    [AuthorizeWorkspace(Permissions.ReadAppUsers)]
    public async Task<ActionResult<AgentCreds>> CheckLoginIdAndSuggest([FromRoute] int workspaceId, string loginId)
    {
        try
        {
            return await agentsHandler.CheckLoginIdAndSuggest(loginId, workspaceId);
        }
        catch (ArgumentException e)
        {
            return Conflict(e.Message);
        }
    }

    [HttpPost("disable")]
    [AuthorizeWorkspace(Permissions.DeleteAppUser)]
    public async Task<ActionResult> DisableAgentsAsync([FromRoute] int workspaceId, [FromBody] IEnumerable<int> agentIds)
    {
        try
        {
            await ValidateOperationAsync(workspaceId, agentIds);
            await agentsHandler.EnableDisableAgentsAsync(workspaceId, agentIds, true);
            return Ok("Successfully disabled users!");
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
        catch (InvalidOperationException)
        {
            return Forbid();
        }
    }

    [HttpPost("reactivate")]
    [AuthorizeWorkspace(Permissions.UpdateAppUser)]
    public async Task<ActionResult> ReactivateAgentsAsync([FromRoute] int workspaceId, [FromBody] IEnumerable<int> agentIds)
    {
        try
        {
            await ValidateOperationAsync(workspaceId, agentIds);
            await agentsHandler.EnableDisableAgentsAsync(workspaceId, agentIds, false);
            return Ok("Successfully enabled users!");
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
        catch (InvalidOperationException)
        {
            return Forbid();
        }
    }

    [HttpPost("move")]
    [AuthorizeWorkspace(Permissions.MoveAppUsers)]
    public async Task<ActionResult> MoveAgentsToTeamAsync([FromRoute] int workspaceId, [FromBody] MoveAgentsRequestDto request)
    {
        try
        {
            await ValidateOperationAsync(workspaceId, request.AgentIds);
            if (!await currentUser.HasAccessToTeamsAsync(workspaceId, [request.DestinationTeamId]))
            {
                logger.LogWarning("{ActionName}{Error}", "MoveAgents", "User doe not have access to destination team.");
                return Forbid();
            }

            await agentsHandler.MoveAgentsToTeamAsync(workspaceId, request.AgentIds, request.DestinationTeamId);
            return Ok("Successfully moved the users!");
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
        catch (InvalidOperationException)
        {
            return Forbid();
        }
    }

    [HttpPut("{agentId}")]
    [AuthorizeWorkspace(Permissions.UpdateAppUser)]
    public async Task<ActionResult<AgentDto>> UpdateAgentAsync([FromRoute] int workspaceId, int agentId, AgentUpdateDto agentUpdateDto)
    {
        try
        {
            var agent = await agentsHandler.GetByAgentIdAsync(agentId);
            if (agent == null || agent.IsDisabled)
            {
                return BadRequest("User not found");
            }

            if (!await currentUser.HasAccessToTeamsAsync(workspaceId, [agent.TeamId]))
            {
                logger.LogWarning("{ActionName}{Error}", "UpdateAgent", "User doe not have access to destination team.");
                return Forbid();
            }

            AgentDto updatedAgent = await agentsHandler.UpdateAgentAsync(agentId, agentUpdateDto);

            return Ok();
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
    }

    private static void CleanPreBulkUpload(string action, IFormFile file)
    {
        if (action != "create" && action != "disable")
        {
            throw new ArgumentException("Action not supported.");
        }

        if (file is null)
        {
            throw new ArgumentException("File is required!");
        }

        if (!(file.ContentType.Equals("text/csv", StringComparison.OrdinalIgnoreCase) || file.ContentType.Equals("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", StringComparison.OrdinalIgnoreCase) || file.ContentType.Equals("application/vnd.ms-excel", StringComparison.OrdinalIgnoreCase)))
        {
            throw new ArgumentException("File format not supported. Only .xlsx, .xls and .csv files are allowed!");
        }
    }

    private static void CleanPostBulkUpload(IList<AgentCsvDto> records, int teamId)
    {
        if (records is null || records.Count == 0)
        {
            throw new ArgumentException("File must contain at least one row to proceed.");
        }

        if (teamId > 0 && records.Any(record => record.TeamId != teamId))
        {
            throw new ArgumentException($"You are trying to add users outside of your team.");
        }
    }

    [HttpPost("bulk-upload")]
    [AuthorizeWorkspace(Permissions.CreateAppUser)]
    public async Task<ActionResult> BulkUploadAgentsCsvAsync(IFormFile file, [FromRoute] int workspaceId, [FromForm] int? teamId = 0, string action = null, bool isVerified = false)
    {
        try
        {
            CleanPreBulkUpload(action, file);
            List<AgentCsvDto> records;
            if (!file.ContentType.Equals("text/csv", StringComparison.OrdinalIgnoreCase))
            {
                records = await csvParser.ParseAgentsCsvAsync(file, workspaceId, teamId, action);
            }
            else
            {
                records = await xlsxParser.ParseAgentsXlsxAsync(file, workspaceId, teamId, action);
            }

            CleanPostBulkUpload(records, teamId ?? 0);
            var (isSuccess, result, requestSummary) = await agentsHandler.BulkUploadAsync(workspaceId, records, action, isVerified, teamId);

            var csvContent = new StringBuilder();
            foreach (var res in result)
            {
                csvContent.AppendLine(string.Join(',', res));
            }

            if (!isSuccess)
            {
                return new MixedMultipartResult(JsonConvert.SerializeObject(requestSummary), file.FileName, "text/csv", Encoding.UTF8.GetBytes(csvContent.ToString()), StatusCodes.Status200OK);
            }

            switch (action)
            {
                case "create":
                    if (isVerified)
                    {
                        return new MixedMultipartResult(JsonConvert.SerializeObject(requestSummary), file.FileName, "text/csv", Encoding.UTF8.GetBytes(csvContent.ToString()), StatusCodes.Status201Created);
                    }

                    return new OkObjectResult(requestSummary);
                case "disable":
                    return Ok(requestSummary);
                default:
                    return new BadRequestObjectResult(new { message = "Action not supported." });
            }
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
    }

    [HttpGet("icp-report")]
    public async Task<ActionResult<OverallCompatibilityReport>> GetICPCompatibilityReportAsync(int agentId)
    {
        try
        {
            var report = await agentsHandler.GetIcpIncompatibilityReportAsync(agentId);
            return report;
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
        catch (Exception e)
        {
            logger.LogError("Error fetching compatibility details. {Error}", e);
            return StatusCode(StatusCodes.Status500InternalServerError, "Error fetching compatibility details");
        }
    }

    private async Task<int[]> GetTeamIdsFromAgentIds(int workspaceId, IEnumerable<int> agentIds)
    {
        if (agentIds.Count() > 1)
        {
            agentIds = agentIds.ToHashSet();
        }

        var agents = await agentsHandler.GetAgentsByIdAsync(workspaceId, agentIds);
        if (agents == null)
        {
            throw new ArgumentException("No agents found for requested agent ids");
        }

        return agents.Select(agent => agent.TeamId).ToHashSet().ToArray();
    }

    private async Task ValidateOperationAsync(int workspaceId, IEnumerable<int> agentIds)
    {
        if (agentIds == null || !agentIds.Any())
        {
            throw new ArgumentException("No agent IDs specified.");
        }

        var teamIds = await GetTeamIdsFromAgentIds(workspaceId, agentIds);

        if (!await currentUser.HasAccessToTeamsAsync(workspaceId, teamIds))
        {
            logger.LogWarning("An Attempt to create a user in an unauthorized team detected.");
            throw new InvalidOperationException($"Logged in user does not have access to the team.");
        }
    }
}
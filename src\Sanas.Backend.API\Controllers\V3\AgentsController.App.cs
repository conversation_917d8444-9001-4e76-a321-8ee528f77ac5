﻿using System.Text.Json;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sanas.Backend.BO.Dto;
using Sanas.Backend.BO.Dto.Agent;
using Sanas.Backend.BO.Dto.RequestDto;
using Sanas.Backend.BO.Dto.ResponseDto;
using Sanas.Backend.BO.Helpers;

namespace Sanas.Backend.API.Controllers.V3;

public partial class AgentsController
{
    private const string AccessTokenHeader = "X-SANAS-ACCESS-TOKEN";
    private const string RefreshTokenHeader = "X-SANAS-REFRESH-TOKEN";

    private readonly TimeSpan _accessTokenExpiry = TimeSpan.FromHours(24);
    private readonly TimeSpan _refreshTokenExpiry = TimeSpan.FromDays(30);

    [HttpPost("/api/agents/{agentId}/autologin")]
    [AllowAnonymous]
    public async Task<ActionResult<AgentAutoLoginResponse>> AutoLoginAgent(int agentId, AgentAutoLoginDto agentAutoLoginDto, [FromHeader(Name = AccessTokenHeader)] string token)
    {
        if (!Crypto.VerifyTokenAuthorization(token, agentAutoLoginDto.LoginId))
        {
            return Unauthorized();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest();
        }

        var response = await agentsHandler.AgentAutoLoginV3Async(agentId, agentAutoLoginDto);

        if (response.AgentInfo == null)
        {
            return NotFound("User not found");
        }
        else if (response.AgentInfo.IsDisabled)
        {
            return Forbid();
        }

        Response.Headers.Append("X-ResponseSignature", Crypto.SignWithPrivateKey(response.AgentInfo));

        return Ok(response);
    }

    [HttpPost("/api/agents/{agentId}/heartbeat")]
    [AllowAnonymous]
    public async Task<ActionResult<AgentHeartBeatResponseDto>> AgentHeartbeatAsync(int agentId, AgentHeartbeatDto heartbeatDto, [FromHeader(Name = AccessTokenHeader)] string token)
    {
        if (!Crypto.VerifyTokenAuthorization(token, heartbeatDto.LoginId))
        {
            return Unauthorized();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest();
        }

        var agentRecord = await agentsHandler.GetByAgentIdAsync(agentId);

        if (agentRecord == null)
        {
            return NotFound();
        }

        bool isLogginExpired = !string.Equals(agentRecord.MachineName, heartbeatDto.MachineName, StringComparison.OrdinalIgnoreCase);

        if (!isLogginExpired)
        {
            await agentsHandler.UpdateAgentLastCallDetailsAsync(agentId, heartbeatDto);
        }

        var agent = new AgentHeartBeatResponseDto
        {
            AppConfig = agentRecord.AppConfig,
            IsDisabled = agentRecord.IsDisabled,
            Success = true,
            IsLoginExpired = isLogginExpired
        };

        Response.Headers.Append("X-ResponseSignature", Crypto.SignWithPrivateKey(agent));

        return Ok(agent);
    }

    [HttpPost("/api/agents/authenticate")]
    [AllowAnonymous]
    public async Task<ActionResult<AgentAuthResponse>> AuthenticateAgentAsync(AgentLoginV3Dto agentLoginDto)
    {
        if (!ModelState.IsValid)
        {
            return BadRequest();
        }

        try
        {
            var response = await agentsHandler.AuthenticateAgentV3Async(agentLoginDto);

            if (!response.IsLoggedIn)
            {
                var now = DateTime.UtcNow;

                response.Message = "Login successful";
                response.AccessToken = Crypto.GenerateTokenFromPrivateKey(response.AgentInfo, _accessTokenExpiry, now);
                response.RefreshToken = Crypto.GenerateTokenFromPrivateKey(response.AgentInfo, _refreshTokenExpiry, now);
                response.AccessTokenExpiry = now.Add(_accessTokenExpiry);
                response.RefreshTokenExpiry = now.Add(_refreshTokenExpiry);
                response.CreatedAt = now;
                response.Success = true;
            }

            var signature = Crypto.SignWithPrivateKey(response.AgentInfo);

            // Add the signature to the response headers
            Response.Headers.Append("X-ResponseSignature", signature);

            return Ok(response);
        }
        catch (UnauthorizedAccessException e)
        {
            logger.LogWarning("An unauthorized agent login detected. {Error}", e);
            return Unauthorized();
        }
        catch (Exception e)
        {
            logger.LogWarning("Error in authenticating a user. {Error}", e);
            return StatusCode(StatusCodes.Status500InternalServerError, e.Message);
        }
    }

    [HttpPost("/api/agents/reauthenticate")]
    [AllowAnonymous]
    public async Task<ActionResult<AgentAuthResponse>> ReauthenticateAgentAsync(AgentReauthenticationDto reauthDto, [FromHeader(Name = RefreshTokenHeader)] string token)
    {
        if (!Crypto.VerifyTokenAuthorization(token, reauthDto.LoginId))
        {
            return Unauthorized();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest();
        }

        try
        {
            var response = await agentsHandler.ReauthenticateAgentV3Async(reauthDto);
            if (response.AgentInfo is null)
            {
                return NotFound();
            }

            response.Success = true;
            response.CreatedAt = DateTime.UtcNow;
            response.Message = "Token refresh successfull";
            response.AccessToken = Crypto.GenerateTokenFromPrivateKey(response.AgentInfo, _accessTokenExpiry, response.CreatedAt);
            response.RefreshToken = Crypto.GenerateTokenFromPrivateKey(response.AgentInfo, _refreshTokenExpiry, response.CreatedAt);
            response.AccessTokenExpiry = response.CreatedAt.Add(_accessTokenExpiry);
            response.RefreshTokenExpiry = response.CreatedAt.Add(_refreshTokenExpiry);
            
            var signature = Crypto.SignWithPrivateKey(response.AgentInfo);

            // Add the signature to the response headers
            Response.Headers.Append("X-ResponseSignature", signature);

            return Ok(response);
        }
        catch (InvalidOperationException)
        {
            return Forbid();
        }
        catch (ArgumentException)
        {
            return NotFound();
        }
    }

    [HttpPatch("/api/agents/{agentId}/logout")]
    [AllowAnonymous]
    public async Task<ActionResult<AgentAuthenticationResponseDto>> LogOutAgentAsync(int agentId, AgentLogOutDto logOutDto, [FromHeader(Name = AccessTokenHeader)] string token)
    {
        if (!Crypto.VerifyTokenAuthorization(token, logOutDto.LoginId))
        {
            return Unauthorized();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest();
        }

        try
        {
            var agent = await agentsHandler.GetByAgentIdAsync(agentId);

            if (agent is null)
            {
                return NotFound();
            }

            if (agent.IsDisabled)
            {
                // SWAP - 901: Commented
                // return Forbid();
            }

            if (string.Equals(logOutDto.MachineName, agent.MachineName, StringComparison.OrdinalIgnoreCase))
            {
                await agentsHandler.LogOutAgentAsync(agentId);
                await notificationsHandler.OnEventReceivedFromApp(agent, NotificationType.AgentLogout, null);
            }

            return Ok(true);
        }
        catch (ArgumentException e)
        {
            return NotFound(e.Message);
        }
    }

    [HttpPatch("/api/agents/{agentId}/preferences")]
    [AllowAnonymous]
    public async Task<ActionResult<bool>> UpdateAgentPreferences(int agentId, AgentPreferencesUpdateDto updateDto, [FromHeader(Name = AccessTokenHeader)] string token)
    {
        try
        {
            var res = false;

            // Verify authorization first
            if (!Crypto.VerifyTokenAuthorization(token, updateDto.LoginId))
            {
                // If the above failed, Likely called from the portal
                if (HttpContext.User.HasClaim(c => c.Value == Permissions.UpdateAppUser))
                {
                    res = await agentsHandler.UpdateAgentPreferencesAsync(agentId, updateDto);
                    return Ok(res);
                }

                return Unauthorized();
            }

            if (!ModelState.IsValid)
            {
                return BadRequest();
            }

            // Verification passed
            var agent = await agentsHandler.GetByAgentIdAsync(agentId);

            if (agent == null)
            {
                return NotFound();
            }

            if (agent.IsDisabled)
            {
                return Forbid();
            }

            res = await agentsHandler.UpdateAgentPreferencesAsync(agentId, updateDto);

            if (updateDto.PreferenceName == "IsSynthesisEnabled")
            {
                if (updateDto.NewValue is JsonElement jsonElement && jsonElement.ValueKind == JsonValueKind.False)
                {
                    await notificationsHandler.OnEventReceivedFromApp(agent, NotificationType.SynthesisDisabled, updateDto.Reason);
                }
            }

            return Ok(res);
        }
        catch (ArgumentException e)
        {
            return NotFound(e.Message);
        }
    }

    [HttpPatch("/api/agents/{agentId}/pwd")]
    [AllowAnonymous]
    public async Task<ActionResult> UpdatePasswordFromApp(int agentId, AgentChangePasswordDto changePasswordDto, [FromHeader(Name = AccessTokenHeader)] string token)
    {
        if (!Crypto.VerifyTokenAuthorization(token, changePasswordDto.LoginId))
        {
            return Unauthorized();
        }

        if (!ModelState.IsValid)
        {
            return BadRequest();
        }

        try
        {
            await agentsHandler.UpdatePasswordFromAppAsync(agentId, changePasswordDto);
            return Ok();
        }
        catch (InvalidOperationException e)
        {
            return Forbid(e.Message);
        }
        catch (ArgumentException e)
        {
            return NotFound(e.Message);
        }
    }

    [HttpPatch("/api/agents/{agentId}/icp")]
    [AllowAnonymous]
    public async Task<ActionResult> UpdateICPAsync(int agentId, [FromHeader(Name = AccessTokenHeader)] string token, [FromBody] OverallCompatibilityReport icpReport)
    {
        var agent = await agentsHandler.GetByAgentIdAsync(agentId);

        if (agent is null || !Crypto.VerifyTokenAuthorization(token, agent.LoginId))
        {
            return Unauthorized();
        }

        try
        {
            await agentsHandler.UpdateICPAsync(agentId, icpReport);
            return Ok();
        }
        catch (Exception e)
        {
            logger.LogError("Failed to update ICP result. {Error}", e);
            return StatusCode(StatusCodes.Status500InternalServerError, "Failed to update the ICP result");
        }
    }

    [HttpPost("/api/agents/isloggedin")]
    [AllowAnonymous]
    public async Task<ActionResult<AgentLoginValidationDto>> ValidateLoginForMachineAsync(AgentLoginCheckDto request)
    {
        var agent = await agentsHandler.GetByLoginIdAsync(request.WorkspaceKey, request.LoginId);

        if (agent is null)
        {
            return NotFound(null);
        }

        try
        {
            var result = new AgentLoginValidationDto
            {
                LastMachine = agent.MachineName,
                IsDisabled = agent.IsDisabled,
                IsLoggedIn = agent.IsDisabled ? false : HasLoggedIn(agent, request.MachineName),
                Success = true
            };

            Response.Headers.Append("X-ResponseSignature", Crypto.SignWithPrivateKey(result));

            return Ok(result);
        }
        catch (Exception e)
        {
            logger.LogError("Failed to get last login details of the agent. {Error}", e);
            return StatusCode(StatusCodes.Status500InternalServerError, "Failed to get last login details of the agent.");
        }
    }

    private AgentAuthResponse CreateAuthorizedAgentResponse(AgentAuthExDto agent)
    {
        var now = DateTime.UtcNow;

        return new AgentAuthResponse()
        {
            Success = true,
            Message = "Login successful",
            AccessToken = Crypto.GenerateTokenFromPrivateKey(agent, _accessTokenExpiry, now),
            RefreshToken = Crypto.GenerateTokenFromPrivateKey(agent, _refreshTokenExpiry, now),
            CreatedAt = now,
            AccessTokenExpiry = now.Add(_accessTokenExpiry),
            RefreshTokenExpiry = now.Add(_refreshTokenExpiry),
            AgentInfo = agent
        };
    }

    private static bool HasLoggedIn(AgentDto agent, string machineName)
    {
        // If the incoming machine name is same then he is allowed to login.
        if (!string.Equals(agent.MachineName, machineName, StringComparison.OrdinalIgnoreCase))
        {
            if (agent.LastLoginUtc.HasValue)
            {
                if (agent.LastSeenUtc.HasValue)
                {
                    if (agent.LastSeenUtc > agent.LastLoginUtc)
                    {
                        // has logged out.
                        return false;
                    }
                }

                // still logged in
                return true;
            }
        }

        // Probably never logged in
        return false;
    }
}

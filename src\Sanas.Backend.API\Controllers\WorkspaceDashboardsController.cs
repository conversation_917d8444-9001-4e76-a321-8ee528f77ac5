﻿using Asp.Versioning;
using Microsoft.AspNetCore.Mvc;
using Sanas.Backend.API.Authorization;
using Sanas.Backend.BO.Dto.Dashboards;
using Sanas.Backend.BO.Dto.Enums;
using Sanas.Backend.BO.Handlers;
using Sanas.Backend.BO.Helpers;

namespace Sanas.Backend.API.Controllers;

[Route("api/workspaces/{workspaceId}/dashboards")]
[ApiController]
[ApiVersion("1.0")]
[ApiVersion("2.0")]
[ApiVersion("3.0")]
[ApiVersion("4.0")]
public class WorkspaceDashboardsController(
    ILogger<WorkspaceDashboardsController> logger,
    IWorkspaceHandler workspaceHandler) : ControllerBase
{
    [HttpGet("engage-report")]
    [AuthorizeWorkspace(Permissions.ReadWorkspaceReports)]
    public async Task<ActionResult<AgentActivityReport>> GetAgentActivityReportAsync([FromRoute] int workspaceId, [FromQuery] DurationTypes durationType = DurationTypes.TwelveHours)
    {
        try
        {
            var report = await workspaceHandler.GetAgentActityReportAsync(workspaceId, durationType);
            return report;
        }
        catch (Exception e)
        {
            logger.LogError("Error in fetching agent engage dashboard. {Error}", e);
            return StatusCode(StatusCodes.Status500InternalServerError, $"Error in fetching agent engage dashboard");
        }
    }
}

﻿using System.Text.Json;
using Asp.Versioning;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Sanas.Backend.API.Authorization;
using Sanas.Backend.API.Helpers;
using Sanas.Backend.BO.Dto;
using Sanas.Backend.BO.Dto.PortalUsers;
using Sanas.Backend.BO.Dto.RequestDto;
using Sanas.Backend.BO.Dto.ResponseDto;
using Sanas.Backend.BO.Handlers;
using Sanas.Backend.BO.Helpers;

namespace Sanas.Backend.API.Controllers;

[ApiController]
[Authorize]
[Route("api/[Controller]")]
[ApiVersion("1.0")]
[ApiVersion("2.0")]
[ApiVersion("3.0")]
[ApiVersion("4.0")]
public class WorkspacesController(
    ILogger<WorkspacesController> logger,
    IWorkspaceHandler workspaceHandler,
    IRBACHandler rbacHandler,
    CurrentUser currentUser) : ControllerBase
{
    private readonly ILogger _logger = logger;

    [HttpGet]
    public async Task<ActionResult<PagedDto<WorkspaceDto>>> GetAllAsync([FromQuery] RequestOptions queryOptions, bool skipAgents)
    {
        _logger.LogInformation("Getting all workspaces");

        if (await currentUser.HasPermissionsAsync(0, [Permissions.ReadAllWorkspaces]))
        {
            return await workspaceHandler.GetAllWithPagingAsync(queryOptions, skipAgents);
        }

        return await workspaceHandler.GetByUserIdWithPagingAsync(await currentUser.GetUserIdAsync(), queryOptions);
    }

    [HttpPost]
    [AuthorizeWorkspace(Permissions.CreateWorkspace)]
    public async Task<IActionResult> CreateWorkspaceAsync(WorkspaceCreateDto workspace)
    {
        try
        {
            if (!WorkspaceCreateDto.IsValid(workspace, out string errorMessage))
            {
                return BadRequest(errorMessage);
            }

            if (workspace.UserInvites is not null && workspace.UserInvites.Length > 0)
            {
                var roleNamesInInvite = workspace.UserInvites.Select(invite => invite.RoleName?.Trim().ToLower());
                var invitedRoles = (await rbacHandler.GetRolesAsync()).CustomerRoles.Where(role => roleNamesInInvite.Contains(role.Name.ToLower()));

                if (!invitedRoles.Any() || invitedRoles.Any(role => role.IsSanasRole))
                {
                    return BadRequest("One or more invites contain invalid role");
                }
            }

            var newRecord = await workspaceHandler.CreateWorkspaceAsync(workspace);
            return CreatedAtAction(nameof(GetWorkspaceByIdAsync), new { workspaceId = newRecord.WorkspaceId }, newRecord);
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
    }

    [HttpGet("{workspaceId}")]
    [AuthorizeWorkspace(Permissions.ReadWorkspaces)]
    public async Task<ActionResult<WorkspaceDto>> GetWorkspaceByIdAsync(int workspaceId)
    {
        var record = await workspaceHandler.GetByIdAsync(workspaceId);

        if (record is null || record.IsDisabled)
        {
            return BadRequest("Workspace not found or disabled");
        }

        return record;
    }

    [HttpPut("{workspaceId}")]
    [AuthorizeWorkspace(Permissions.UpdateWorkspace)]
    public async Task<ActionResult> UpdateWorkspaceAsync(int workspaceId, [FromBody] WorkspaceUpdateDto workspace)
    {
        try
        {
            if (!WorkspaceUpdateDto.IsValid(workspace, out string errorMessage))
            {
                throw new ArgumentException(errorMessage);
            }

            await workspaceHandler.UpdateWorkspaceAsync(workspaceId, workspace);

            return Ok("Workspace updated successfully.");
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
    }

    [HttpDelete("{workspaceId}")]
    [AuthorizeWorkspace(Permissions.DeleteWorkspace)]
    public async Task<ActionResult> DeleteWorkspaceAsync(int workspaceId)
    {
        try
        {
            await workspaceHandler.DeleteWorkspaceAsync(workspaceId);
            return Accepted();
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
    }

    [HttpGet("{workspaceId}/portalusers")]
    [AuthorizeWorkspace(Permissions.ReadPortalUsers)]
    public async Task<ActionResult<PagedDto<PortalUserDto>>> GetPortalUsersInWorkspaceAsync(int workspaceId, [FromQuery] RequestOptions req, string role)
    {
        return await workspaceHandler.GetUsersInWorkspaceWithPagingAsync(workspaceId, req, role);
    }

    [HttpGet("{workspaceId}/teams")]
    [AuthorizeWorkspace(Permissions.ReadTeams)]
    public async Task<ActionResult<PagedDto<TeamDto>>> GetTeamsInWorkspaceAsync(int workspaceId, [FromQuery] RequestOptions req)
    {
        return await workspaceHandler.GetTeamsInWorkspaceWithPagingAsync(workspaceId, req);
    }

    [HttpGet("{workspaceId}/agents")]
    [AuthorizeWorkspace(Permissions.ReadAppUsers)]
    public async Task<ActionResult<PagedDto<AgentDto>>> GetAgentsInWorkspaceAsync(int workspaceId, [FromQuery] RequestOptions req)
    {
        return await workspaceHandler.GetAgentsInWorkspaceWithPagingAsync(workspaceId, req);
    }

    [HttpPatch("{workspaceId}/reactivate")]
    [AuthorizeWorkspace(Permissions.UpdateWorkspace)]
    public async Task<ActionResult> ReactivateWorkspaceAsync(int workspaceId)
    {
        try
        {
            await workspaceHandler.ReactivateWorkspace(workspaceId);
            return Ok();
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
    }

    [HttpPost("{workspaceId}/invite")]
    [AuthorizeWorkspace(Permissions.CreatePortalUser)]
    public async Task<ActionResult> InviteUsersToWorkspaceAsync([FromRoute] int workspaceId, [FromBody] UserInvite[] invites)
    {
        try
        {
            if (invites is null || invites.Length == 0)
            {
                return BadRequest("Invitation list is empty.");
            }

            var roleNamesInInvite = invites.Select(invite => invite.RoleName?.ToLower());
            var invitedRoles = (await rbacHandler.GetRolesAsync()).CustomerRoles.Where(role => roleNamesInInvite.Contains(role.Name.ToLower()));

            if (!invitedRoles.Any() || invitedRoles.Any(role => role.IsSanasRole))
            {
                return BadRequest("One or more invites contain invalid role");
            }

            await workspaceHandler.InviteUsersToWorkspaceAsync(workspaceId, invites);
            return Ok();
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
    }

    [HttpPost("{workspaceId}/update-role")]
    [AuthorizeWorkspace(Permissions.UpdatePortalUser)]
    public async Task<ActionResult> UpdateUserRoleAsync([FromRoute] int workspaceId, UserRoleUpdateDto request)
    {
        try
        {
            if (request.PortalUserId <= 0 || string.IsNullOrWhiteSpace(request.RoleName))
            {
                return BadRequest("Request contains invalid inputs.");
            }

            await rbacHandler.UpdateUserRoleAtWorkspaceAsync(workspaceId, request);

            return Ok("Successfully updated tge user role.");
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
    }

    [HttpGet("Bpos")]
    [AuthorizeWorkspace(Permissions.CreateWorkspace)]
    public async Task<ActionResult<PagedDto<string>>> GetBPOsAsync([FromQuery] RequestOptions queryOptions)
    {
        try
        {
            return await workspaceHandler.GetBPOsAsync(queryOptions);
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
    }

    [HttpGet("Sites")]
    [AuthorizeWorkspace(Permissions.CreateWorkspace)]
    public async Task<ActionResult<PagedDto<string>>> GetSitesAsync([FromQuery] RequestOptions queryOptions)
    {
        try
        {
            return await workspaceHandler.GetSitesAsync(queryOptions);
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
    }

    [HttpPatch("{workspaceId}/appConfig")]
    [AuthorizeWorkspace(Permissions.CreateWorkspace)]
    public async Task<ActionResult> UpdateAutoAppConfigurationAsync(int workspaceId, [FromBody] JsonElement appConfig)
    {
        try
        {
            await workspaceHandler.UpdateAutoAppConfigurationAsync(workspaceId, appConfig);
            return Ok();
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
    }

    [HttpPost("nc/{workspaceId}")]
    [AuthorizeWorkspace(Permissions.ReadWorkspaces)]
    public async Task<ActionResult<NCWorkspace>> GetNCWorkspaceAsync(int workspaceId, bool forLogin)
    {
        try
        {
            var workspace = await workspaceHandler.GetNCWorkspaceAsync(workspaceId, await currentUser.GetUserIdAsync(), forLogin);
            return Ok(workspace);
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
    }

    [HttpGet("/api/installers/{installerId:Guid}/verify")]
    [AllowAnonymous]
    [AuthorizeApiKey]
    public async Task<ActionResult<InstallerCheckDto>> CheckInstallerAsync(Guid installerId)
    {
        try
        {
            var result = await workspaceHandler.ValidateInstallerIdAsync(installerId);
            return Ok(result);
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
    }

    [HttpPost("/api/installers/{installerId:Guid}/post-install-status")]
    [AllowAnonymous]
    [AuthorizeApiKey]
    public async Task<ActionResult<bool>> OnInstallCompleted([FromRoute] Guid installerId, [FromBody] InstallStatusDto installResult)
    {
        try
        {
            var result = await workspaceHandler.ValidateInstallerIdAsync(installerId);
            return Ok(result.IsInstallerIdValid && installResult.IsInstallationSuccessful);
        }
        catch (ArgumentException e)
        {
            return BadRequest(e.Message);
        }
    }
}
#See https://aka.ms/customizecontainer to learn how to customize your debug container and how Visual Studio uses this Dockerfile to build your images for faster debugging.
FROM mcr.microsoft.com/dotnet/aspnet:8.0 AS base
#USER app
WORKDIR /app
EXPOSE 8080
EXPOSE 8081

FROM mcr.microsoft.com/dotnet/sdk:8.0 AS build
ARG BUILD_CONFIGURATION=Release
WORKDIR /src
COPY ["Sanas.Backend.API/Sanas.Backend.API.csproj", "Sanas.Backend.API/"]
COPY ["Sanas.Auth0Client/Sanas.Auth0Client.csproj", "Sanas.Auth0Client/"]
COPY ["Sanas.Backend.Data/Sanas.Backend.Data.csproj", "Sanas.Backend.Data/"]
RUN dotnet restore "./Sanas.Backend.API/./Sanas.Backend.API.csproj"
COPY . .
WORKDIR "/src/Sanas.Backend.API"
RUN dotnet build "./Sanas.Backend.API.csproj" -c $BUILD_CONFIGURATION -o /app/build

FROM build AS publish
ARG BUILD_CONFIGURATION=Release
RUN dotnet publish "./Sanas.Backend.API.csproj" -c $BUILD_CONFIGURATION -o /app/publish /p:UseAppHost=false

FROM base AS final
WORKDIR /app
COPY --from=publish /app/publish .

ENTRYPOINT ["dotnet", "Sanas.Backend.API.dll"]
﻿using System.Security.Claims;

namespace Sanas.Backend.API.Extensions;

public static class HttpContextExtensions
{
    public static string GetUserId(this HttpContext context)
    {
        if (context.User.Identity.IsAuthenticated)
        {
            var auth0UserIDClaim = context.User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier);

            return auth0UserIDClaim.Value;
        }

        return null;
    }
}
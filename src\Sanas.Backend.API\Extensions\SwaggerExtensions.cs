﻿using System.Reflection;
using Microsoft.OpenApi.Models;

namespace Sanas.Backend.API.Extensions;

public static class SwaggerExtensions
{
    public static void AddSwaggerWithAuth(this IServiceCollection services, IConfiguration configuration)
    {
        services.AddEndpointsApiExplorer();

        services.AddSwaggerGen(options =>
        {
            options.SwaggerDoc("v2", new OpenApiInfo
            {
                Title = "API Definitions",
                Version = "v2.0"
            });

            options.AddSecurityDefinition("oauth2", new OpenApiSecurityScheme
            {
                Type = SecuritySchemeType.OAuth2,
                BearerFormat = "JWT",
                Flows = new OpenApiOAuthFlows
                {
                    Implicit = new OpenApiOAuthFlow
                    {
                        TokenUrl = new Uri(configuration["Swaggerclient:TokenUrl"]),
                        AuthorizationUrl = new Uri(configuration["Swaggerclient:AuthUrl"]),
                        Scopes = new Dictionary<string, string>
                        {
                            { "openid", "OpenId" },
                            { "email", "user email" }
                            // { "profile", "user profile" }
                        }
                    }
                }
            });

            options.AddSecurityRequirement(new OpenApiSecurityRequirement
            {
                {
                  new OpenApiSecurityScheme
                  {
                      Reference = new OpenApiReference { Type = ReferenceType.SecurityScheme, Id = "oauth2" }
                  },
                  ["openid"]
                }
            });

            var xmlFile = $"{Assembly.GetExecutingAssembly().GetName().Name}.xml";
            var xmlPath = Path.Combine(AppContext.BaseDirectory, xmlFile);

            if (Path.Exists(xmlPath))
            {
                options.IncludeXmlComments(xmlPath);
            }
        });
    }

    public static void UseSwaggerwithAuth(this WebApplication app, IConfiguration configuration)
    {
        app.UseSwagger();
        app.UseSwaggerUI(c =>
        {
            c.SwaggerEndpoint("/swagger/v2/swagger.json", "Sanas Portal API");
            c.OAuthClientId(configuration["Swaggerclient:ClientId"]);
            c.OAuthClientSecret(configuration["Swaggerclient:ClientSecret"]);
            c.OAuthScopeSeparator(" ");
            c.EnableTryItOutByDefault();
            c.OAuthUsePkce();
            c.OAuthAdditionalQueryStringParams(new Dictionary<string, string>
            {
                { "audience", configuration["Auth0:Audience"] }
            });
        });
    }
}

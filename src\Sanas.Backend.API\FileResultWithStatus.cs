﻿using System.Text;
using Microsoft.AspNetCore.Mvc;

namespace Sanas.Backend.BO.Dto.Bulk;

public class FileResultWithStatus : FileContentResult
{
    private readonly string _content;
    private readonly int _statusCode;

    public FileResultWithStatus(string content, int statusCode)
        : base(Encoding.UTF8.GetBytes(content), "text/csv")
    {
        FileDownloadName = "data.csv";
        _content = content;
        _statusCode = statusCode;
    }

    public override async Task ExecuteResultAsync(ActionContext context)
    {
        var array = Encoding.UTF8.GetBytes(_content);
        await Task.CompletedTask;
        context.HttpContext.Response.StatusCode = _statusCode;
        context.HttpContext.Response.ContentType = "text/csv";
        context.HttpContext.Response.ContentLength = array.Length;
        await context.HttpContext.Response.Body.WriteAsync(array);
    }
}

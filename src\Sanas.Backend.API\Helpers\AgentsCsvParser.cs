﻿using Sanas.Backend.BO.Dto;
using Sanas.Backend.BO.Handlers;

namespace Sanas.Backend.API.Helpers;

public class AgentsCsvParser(IWorkspaceHandler workspaceHandler, ITeamsHandler teamsHandler)
{
    public async Task<List<AgentCsvDto>> ParseAgentsCsvAsync(IFormFile file, int workspaceId, int? teamId = 0, string action = null)
    {
        var workspaceTeams = await workspaceHandler.GetAllTeamNamesWithIdsAsync(workspaceId);
        var workspaceDefaultTeam = await workspaceHandler.GetDefaultTeamAsync(workspaceId);
        var selectedTeam = teamId > 0 ? await teamsHandler.GetByTeamIdAsync(workspaceId, teamId ?? 0) : default;
        var headerRowProcessed = false;
        var records = new List<AgentCsvDto>();

        using var reader = new StreamReader(file.OpenReadStream());

        while (!reader.EndOfStream)
        {
            var rowData = reader.ReadLine();

            if (string.IsNullOrWhiteSpace(rowData))
            {
                continue;
            }

            if (!headerRowProcessed)
            {
                ValidateAgentsBulkUploadCsvColumnHeaders(rowData, action);
                headerRowProcessed = true;
            }
            else
            {
                var values = ParseCSVLine(rowData); // rowData.Split(",");

                if (values.All(string.IsNullOrWhiteSpace))
                {
                    continue;
                }

                var record = BuildRecordFromCsvRowAsync(selectedTeam, workspaceDefaultTeam, workspaceTeams, values);
                records.Add(record);
            }
        }

        return records;
    }

    private static AgentCsvDto BuildRecordFromCsvRowAsync(TeamDto selectedTeam, TeamDto worksapceDefaultTeam, Dictionary<string, int> workspaceTeams, string[] values)
    {
        var loginId = values.Length > 0 ? values[0].Trim() : string.Empty;
        var agentName = values.Length > 1 ? values[1].Trim() : string.Empty;
        var teamName = values.Length > 2 ? values[2].Trim() : string.Empty;
        var teamId = workspaceTeams.GetValueOrDefault(teamName.ToLower());

        if (string.IsNullOrWhiteSpace(teamName))
        {
            teamName = selectedTeam?.Name ?? worksapceDefaultTeam.Name;
            teamId = selectedTeam?.TeamId ?? worksapceDefaultTeam.TeamId;
        }

        var record = new AgentCsvDto
        {
            LoginId = loginId,
            Name = agentName,
            TeamName = teamName,
            TeamId = teamId
        };

        return record;
    }

    private static void ValidateAgentsBulkUploadCsvColumnHeaders(string rowData, string action)
    {
        var columns = rowData.Split(',', StringSplitOptions.TrimEntries);
        var columnsByAction = Constants.AgentsBulkUploadCSVColumnsByAction;
        var validColumns = columnsByAction[action];
        if (!columns.Take(validColumns.Length).Select(c => c.ToLower()).SequenceEqual(validColumns))
        {
            throw new ArgumentException("Columns in the file do not match the requirements. Please download the csv template.");
        }
    }

    public static string[] ParseCSVLine(string line)
    {
        if (string.IsNullOrWhiteSpace(line))
        {
            return [];
        }

        var values = new List<string>();
        var inQuotes = false;
        var value = new System.Text.StringBuilder();

        for (int i = 0; i < line.Length; i++)
        {
            char c = line[i];

            if (inQuotes)
            {
                if (c == '\"')
                {
                    if (i + 1 < line.Length && line[i + 1] == '\"')
                    {
                        // This is an escaped double quote
                        value.Append('\"');
                        i++; // Skip the next quote
                    }
                    else
                    {
                        inQuotes = false; // Closing quote
                    }
                }
                else
                {
                    value.Append(c);
                }
            }
            else
            {
                if (c == '\"')
                {
                    inQuotes = true; // Starting quote
                }
                else if (c == ',')
                {
                    values.Add(value.ToString());
                    value.Clear();
                }
                else
                {
                    value.Append(c);
                }
            }
        }

        // Add the last value
        values.Add(value.ToString());

        return [.. values];
    }
}
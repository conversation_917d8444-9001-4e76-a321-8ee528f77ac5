using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;
using Sanas.Backend.BO.Dto;
using Sanas.Backend.BO.Handlers;

namespace Sanas.Backend.API.Helpers;

public class AgentsXlsxParser(IWorkspaceHandler workspaceHandler, ITeamsHandler teamsHandler)
{
    public async Task<List<AgentCsvDto>> ParseAgentsXlsxAsync(IFormFile file, int workspaceId, int? teamId = 0, string action = null)
    {
        using var document = SpreadsheetDocument.Open(file.OpenReadStream(), false);
        var defaultTeam = await workspaceHandler.GetDefaultTeamAsync(workspaceId);
        var selectedTeam = teamId > 0 ? await teamsHandler.GetByTeamIdAsync(workspaceId, (int)teamId) : default;
        var records = new List<AgentCsvDto>();
        var workbookPart = document.WorkbookPart;
        if (workbookPart is null)
        {
            return records;
        }

        var sheet = workbookPart.Workbook.Sheets?.GetFirstChild<Sheet>();
        if (sheet?.Id is null)
        {
            return records;
        }

        var worksheetPart = (WorksheetPart)workbookPart.GetPartById(sheet.Id);
        var data = worksheetPart.Worksheet.GetFirstChild<SheetData>();
        if (data is null)
        {
            return records;
        }

        var rows = data.Elements<Row>().ToList();
        for (int i = 0; i < rows.Count; i++)
        {
            if (i == 0)
            {
                ValidateUploadedSheet(document, rows[i], action);
                continue;
            }

            var agent = await BuildFromRow(document, rows[i], workspaceId, teamsHandler, selectedTeam ?? defaultTeam);

            if (string.IsNullOrWhiteSpace(agent.LoginId) && string.IsNullOrWhiteSpace(agent.Name) && string.IsNullOrWhiteSpace(agent.TeamName))
            {
                // It's a blank row. Ignore it
                continue;
            }

            records.Add(agent);
        }

        return records;
    }

    public static string GetCellValue(SpreadsheetDocument document, Row row, string columnName)
    {
        Cell cell = row.Elements<Cell>().FirstOrDefault(c => c.CellReference.Value == $"{columnName}{row.RowIndex}");

        if (cell is null)
        {
            return null;
        }

        string value = cell.InnerText;

        if (cell.DataType != null && cell.DataType.Value == CellValues.SharedString)
        {
            var stringTable = document.WorkbookPart.GetPartsOfType<SharedStringTablePart>().FirstOrDefault();
            if (stringTable != null)
            {
                value = stringTable.SharedStringTable.ElementAt(int.Parse(value)).InnerText;
            }
        }

        return value?.Trim();
    }

    private static bool ValidateUploadedSheet(SpreadsheetDocument doc, Row headerRow, string action)
    {
        string[] validColumn = Constants.AgentsBulkUploadCSVColumnsByAction[action];
        string[] sheetColumns =  new string[]
            {
                GetCellValue(doc, headerRow, "A"),
                GetCellValue(doc, headerRow, "B"),
                GetCellValue(doc, headerRow, "C")
            }
            .Where(item => item != null)
            .ToArray();

        if (!sheetColumns.Select(column => column?.ToLower()).SequenceEqual(validColumn))
        {
            throw new ArgumentException("Columns in the file do not match the requirements. Please download the csv template.");
        }

        return true;
    }

    private static async Task<AgentCsvDto> BuildFromRow(SpreadsheetDocument doc, Row row, int workspaceId, ITeamsHandler teamsHandler, TeamDto fallbackTeam)
    {
        var loginId = GetCellValue(doc, row, "A") ?? string.Empty;
        var agentName = GetCellValue(doc, row, "B") ?? string.Empty;
        var teamName = GetCellValue(doc, row, "C") ?? string.Empty;

        var team = new TeamDto { TeamId = -1 };

        if (!string.IsNullOrWhiteSpace(teamName))
        {
            if (await teamsHandler.GetByTeamNameAsync(workspaceId, teamName.ToLower()) is TeamDto teamDto)
            {
                team = teamDto;
            }
        }
        else
        {
            if (!string.IsNullOrWhiteSpace(loginId) || !string.IsNullOrWhiteSpace(agentName))
            {
                team = fallbackTeam;
            }
        }

        return new AgentCsvDto
        {
            LoginId = loginId,
            Name = agentName,
            TeamName = team?.Name ?? string.Empty,
            TeamId = team.TeamId
        };
    }
}
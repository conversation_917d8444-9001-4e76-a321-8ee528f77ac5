﻿using System.Security.Claims;
using Sanas.Backend.API.Extensions;
using Sanas.Backend.BO.Handlers;
using Sanas.Backend.BO.Helpers;

namespace Sanas.Backend.API.Helpers;

public class CurrentUser(
    IHttpContextAccessor httpContextAccessor,
    IUsersHandler usersHandler,
    IRBACHandler userService)
{
    private readonly HttpContext _httpContext = httpContextAccessor.HttpContext;

    public async Task<bool> HasAccessToTeamsAsync(int workspaceId, int[] teamIds)
    {
        if (teamIds!.Length == 0)
        {
            return true;
        }

        if (_httpContext.User.Identity.IsAuthenticated)
        {
            // Check if user is an admin
            if (await HasPermissionsAsync(workspaceId, [Permissions.CreateTeam]))
            {
                return true;
            }

            var assignedTeamItds = await usersHandler.GetAccessibleTeamsAsync(workspaceId, _httpContext.GetUserId());

            return !teamIds.Except(assignedTeamItds.Select(teamId => teamId)).Any();
        }

        return false;
    }

    public async Task<int> GetUserIdAsync()
    {
        if (_httpContext.User.Identity.IsAuthenticated)
        {
            var auth0UserIDClaim = _httpContext.User.Claims.FirstOrDefault(c => c.Type == ClaimTypes.NameIdentifier);
            var user = await usersHandler.GetByUserIdAsync(auth0UserIDClaim.Value);

            if (user is not null)
            {
                return user.PortalUserId;
            }
        }

        return 0;
    }

    public async Task<bool> HasPermissionsAsync(int workspaceId, IEnumerable<string> permissions)
    {
        var userId = _httpContext.GetUserId();
        var user = await usersHandler.GetByUserIdAsync(userId);
        if (user is not null)
        {
            var grantedPermissions = await userService.GetUserPermissionsAsync(workspaceId, user.PortalUserId, user.IsSanasUser);
            return !permissions.Except(grantedPermissions).Any();
        }

        return false;
    }

    public async Task<bool> HasAccessToWorkspacesAsync(IEnumerable<int> workspaceIds)
    {
        var userWorkspaceIds = await usersHandler.GetWorkspaceIdsForUserAsync(_httpContext.GetUserId());
        return workspaceIds.Except(userWorkspaceIds).Any();
    }
}

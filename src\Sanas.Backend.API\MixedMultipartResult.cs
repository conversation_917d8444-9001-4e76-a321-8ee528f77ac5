using System.Text;
using Microsoft.AspNetCore.Mvc;

namespace Sanas.Backend.BO.Dto.Bulk;

public class MixedMultipartResult : ActionResult
{
    private readonly string _text;
    private readonly byte[] _fileContent;
    private readonly string _contentType;
    private readonly string _fileName;
    private readonly int _statusCode;

    public MixedMultipartResult(string text, string fileName, string contentType, byte[] fileContent, int statusCode)
    {
        _text = text;
        _fileName = fileName;
        _fileContent = fileContent;
        _statusCode = statusCode;
        _contentType = contentType;
    }

    public override async Task ExecuteResultAsync(ActionContext context)
    {
        var boundary = "multipart_boundary";
        var multipartContent = new MultipartContent("mixed", boundary);

        // Text part
        var textPart = new StringContent(_text, Encoding.UTF8, "application/json");
        multipartContent.Add(textPart);

        // File part
        var fileContent = new StreamContent(new MemoryStream(_fileContent));
        fileContent.Headers.ContentType = new System.Net.Http.Headers.MediaTypeHeaderValue(_contentType);
        fileContent.Headers.ContentDisposition = new System.Net.Http.Headers.ContentDispositionHeaderValue("attachment")
        {
            FileName = _fileName
        };
        multipartContent.Add(fileContent);

        context.HttpContext.Response.StatusCode = _statusCode;

        context.HttpContext.Response.Headers.ContentType = $"multipart/mixed; boundary={boundary}";
        await multipartContent.CopyToAsync(context.HttpContext.Response.Body);
    }
}
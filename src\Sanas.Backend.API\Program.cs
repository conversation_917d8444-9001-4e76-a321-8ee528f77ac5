using AWS.Logger.SeriLog;
using Serilog;
using Serilog.Formatting.Compact;

namespace Sanas.Backend.API;

public static class Program
{
    public static void Main(string[] args)
    {
        var builder = WebApplication.CreateBuilder(args);
        builder.Host.UseSerilog((context, logConfig) =>
        {
            logConfig
            .ReadFrom.Configuration(context.Configuration)
            .WriteTo.Console()
            .WriteTo.AWSSeriLog(configuration: context.Configuration, textFormatter: new RenderedCompactJsonFormatter());
        });
        
        var startup = new Startup(builder.Configuration);
        startup.ConfigureServices(builder.Services);
        var app = builder.Build();

        startup.Configure(app, builder.Environment);
        app.Run();
    }
}
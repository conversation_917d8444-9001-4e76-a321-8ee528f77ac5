﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <UserSecretsId>dbe4edf8-d0ba-4118-8742-5f57c53374aa</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <TreatWarningsAsErrors>True</TreatWarningsAsErrors>
    <CodeAnalysisRuleSet>..\Sanas.Portal.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Authorization\RbacHandler.cs" />
    <Compile Remove="Authorization\RbacRequirement.cs" />
  </ItemGroup>

  <ItemGroup>
    <Content Include="Authorization\RbacHandler.cs" />
    <Content Include="Authorization\RbacRequirement.cs" />
  </ItemGroup>

  <ItemGroup>
    <PackageReference Include="AWS.Logger.SeriLog" Version="3.4.1" />
    <PackageReference Include="Microsoft.AspNetCore.Authentication.JwtBearer" Version="8.0.4" />
    <PackageReference Include="Microsoft.AspNetCore.OpenApi" Version="8.0.4" />
    <PackageReference Include="Microsoft.Extensions.Http.Polly" Version="8.0.4" />
    <PackageReference Include="Microsoft.Extensions.Http.Resilience" Version="8.4.0" />
    <PackageReference Include="Microsoft.VisualStudio.Azure.Containers.Tools.Targets" Version="1.20.1" />
    <PackageReference Include="Npgsql" Version="8.0.3" />
    <PackageReference Include="Polly" Version="8.3.1" />
    <PackageReference Include="Serilog" Version="3.1.1" />
    <PackageReference Include="Serilog.AspNetCore" Version="8.0.1" />
    <PackageReference Include="Serilog.Settings.Configuration" Version="8.0.0" />
    <PackageReference Include="Swashbuckle.AspNetCore" Version="6.5.0" />
    <PackageReference Include="Npgsql.EntityFrameworkCore.PostgreSQL" Version="8.0.4" />
    <PackageReference Include="StyleCop.Analyzers.Unstable" Version="1.2.0.556">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
    <PackageReference Include="Asp.Versioning.Http" Version="8.1.0" />
    <PackageReference Include="Asp.Versioning.Mvc" Version="8.1.0" />
    <PackageReference Include="Asp.Versioning.Mvc.ApiExplorer" Version="8.1.0" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\Sanas.Backend.BO\Sanas.Backend.BO.csproj" />
  </ItemGroup>

</Project>

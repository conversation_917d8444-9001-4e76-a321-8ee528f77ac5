﻿using System.Security.Claims;
using Asp.Versioning;
using Microsoft.AspNetCore.Authentication.JwtBearer;
using Microsoft.EntityFrameworkCore;
using Microsoft.IdentityModel.Tokens;
using Polly;
using Polly.Extensions.Http;
using Polly.Retry;
using Sanas.Auth0Client;
using Sanas.Auth0Client.Interfaces;
using Sanas.Backend.API.Extensions;
using Sanas.Backend.API.Helpers;
using Sanas.Backend.API.Middlewares;
using Sanas.Backend.BO.Controllers;
using Sanas.Backend.BO.Handlers;
using Sanas.Backend.BO.Handlers.Interfaces;
using Sanas.Backend.BO.Handlers.Interfaces.V4;
using Sanas.Backend.BO.Handlers.V4;
using Sanas.Backend.BO.Helpers;
using Sanas.Backend.BO.Services;
using Sanas.Backend.BO.Services.Interfaces;
using Sanas.Backend.Data;
using Sanas.Backend.Data.Repositories;
using Sanas.Backend.Data.Repositories.Interfaces;

namespace Sanas.Backend.API;

public class Startup(IConfiguration configuration)
{
    public IConfiguration Configuration { get; } = configuration;

    public void ConfigureServices(IServiceCollection services)
    {
        var environment = Configuration["Settings:MaxRecordsPerPage"];

        // Remove this once SecretManager.cs is configured
        Crypto.Initialize(Convert.FromBase64String(Configuration["ResponseSettings:PrivateKey"]));

        services.AddApiVersioning(options =>
        {
            options.AssumeDefaultVersionWhenUnspecified = true;
            options.DefaultApiVersion = new ApiVersion(1);
            options.ReportApiVersions = true;
            options.ApiVersionReader = new QueryStringApiVersionReader("api-version");
        }).AddApiExplorer(options =>
        {
            options.GroupNameFormat = "'v'V";
            options.SubstituteApiVersionInUrl = true;
        });

        services.AddControllers(options => { options.SuppressAsyncSuffixInActionNames = false; });

        services.AddHttpClient<IAuth0ManagementApiService, Auth0ManagementApiService>()
            .AddPolicyHandler(GetRetryPolicy())
            .AddPolicyHandler(GetCircuitBreakerPolicy());

        services.AddHttpClient("auth0", httpClient => { httpClient.BaseAddress = new Uri(Configuration["Auth0:Authority"]); });
        services.AddHttpClient<IEmailService, MandrilEmailService>().AddPolicyHandler(GetRetryPolicy()).AddPolicyHandler(GetCircuitBreakerPolicy());
        services.AddHttpClient<IAppDownloadService, AppDownloadService>().AddPolicyHandler(GetRetryPolicy()).AddPolicyHandler(GetCircuitBreakerPolicy());
        services.AddHttpClient<IPushNotificationService, FcmPushNotificationService>().AddPolicyHandler(GetRetryPolicy()).AddPolicyHandler(GetCircuitBreakerPolicy());
        services.AddHttpClient<IBnsService, BnsService>().AddPolicyHandler(GetRetryPolicy()).AddPolicyHandler(GetCircuitBreakerPolicy());
        services.AddHttpClient<IFreshdeskService, FreshdeskService>().AddPolicyHandler(GetRetryPolicy()).AddPolicyHandler(GetCircuitBreakerPolicy());
        services.AddHttpClient<INestService, NestService>().AddPolicyHandler(GetRetryPolicy()).AddPolicyHandler(GetCircuitBreakerPolicy());

        services.AddDbContext<PortalDbContext>(options =>
        {
            options.UseNpgsql(Configuration.GetConnectionString("SanasPortalDB"))
                .LogTo(s => System.Diagnostics.Debug.WriteLine(s))
                .EnableDetailedErrors(true);
        });

        //services.AddSingleton<IEmailService, GmailService>();
        services.AddSingleton<IUserInviterService, UserInviterService>();
        services.AddSingleton<IBlobService, AmazonS3Service>();

        services.AddSingleton<IHttpContextAccessor, HttpContextAccessor>();
        services.AddSingleton<IAuth0UserManagementService, Auth0UserManagementService>();
        services.AddSingleton<ITelemetryReader, AzureLogAnalyticsReader>();

        services.AddScoped<IRBACRepository, RBACRepository>();
        services.AddScoped<IPortalUserRepository, PortalUsersRepository>();
        services.AddScoped<IWorkspaceRepository, WorkspacesRepository>();
        services.AddScoped<ITeamsRepository, TeamsRepository>();
        services.AddScoped<IAgentsRepository, AgentsRepository>();
        services.AddScoped<IFeedbackRepository, FeedbackRepository>();
        services.AddScoped<INotificationsRepository, NotificationsRepository>();
        services.AddScoped<ExceptionHandlerMiddleware>();
        services.AddScoped<IBpoSitesRepository, BpoSitesRepository>();
        services.AddScoped<IGenericRepository, GenericRepository>();
        services.AddScoped<IAppReleasesRepository, AppReleasesRepository>();
        services.AddScoped<IRBACHandler, RBACHandler>();
        services.AddScoped<IWorkspaceHandler, WorkspaceHandler>();
        services.AddScoped<IUsersHandler, UsersHandler>();
        services.AddScoped<ISanasUserHandler, SanasUserHandler>();
        services.AddScoped<ITeamsHandler, TeamsHandler>();
        services.AddScoped<IAgentsHandler, AgentsHandler>();
        services.AddScoped<IAgentsV4Handler, AgentsV4Handler>();
        services.AddScoped<IFeedbackHandler, FeedbackHandler>();
        services.AddScoped<INotificationsHandler, NotificationsHandler>();
        services.AddScoped<IAppEventHandler, AppEventHandler>();
        services.AddScoped<IGenericHandler, GenericHandler>();
        services.AddScoped<IAppReleasesHandler, AppReleasesHandler>();

        services.AddScoped<CurrentUser>();
        services.AddScoped<AgentsCsvParser>();
        services.AddScoped<AgentsXlsxParser>();
        services.AddScoped<NotifierService>();

        services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = JwtBearerDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = JwtBearerDefaults.AuthenticationScheme;
            })
            .AddJwtBearer(options =>
            {
                options.Authority = Configuration["Auth0:Authority"];
                options.Audience = Configuration["Auth0:Audience"];
                options.TokenValidationParameters = new TokenValidationParameters
                {
                    NameClaimType = "Roles",
                    RoleClaimType = "https://schemas.quickstarts.com/roles"
                };
            });

        services.AddSwaggerWithAuth(Configuration);
        services.AddHealthChecks();
        services.AddAuthorization();

        services.AddCors(options =>
        {
            options.AddPolicy(
                name: "portalfrontend",
                policy =>
                {
                    policy.AllowAnyOrigin();
                    // policy.AllowCredentials();
                    policy.AllowAnyHeader();
                    policy.AllowAnyMethod();
                });
        });
    }

    public void Configure(WebApplication app, IWebHostEnvironment env)
    {
        if (env.IsDevelopment())
        {
            app.UseDeveloperExceptionPage();
        }
        else
        {
            app.UseHsts();
        }

        app.UseSwaggerwithAuth(Configuration);
        app.UseHttpsRedirection();
        app.UseAuthentication();
        app.UseCors("portalfrontend");

        app.Use(async (context, next) =>
        {
            if (context.User != null && context.User.Identity.IsAuthenticated)
            {
                // add claims here 
                _ = context.User.Claims.Append(new Claim("type-x", "value-x"));
            }

            await next();
        });

        app.UseMiddleware<ExceptionHandlerMiddleware>();

        using (var serviceScope = app.Services.GetService<IServiceScopeFactory>().CreateScope())
        {
            var context = serviceScope.ServiceProvider.GetRequiredService<PortalDbContext>();
            context.Database.EnsureCreated();
        }

        app.UseAuthorization();
        app.MapControllers();
        app.MapHealthChecks("/health-check");
    }

    private static AsyncRetryPolicy<HttpResponseMessage> GetRetryPolicy()
    {
        return HttpPolicyExtensions
            .HandleTransientHttpError()
            .OrResult(msg => msg.StatusCode == System.Net.HttpStatusCode.NotFound)
            .WaitAndRetryAsync(5, retryAttempt => TimeSpan.FromSeconds(Math.Pow(2, retryAttempt)));
    }

    private static Polly.CircuitBreaker.AsyncCircuitBreakerPolicy<HttpResponseMessage> GetCircuitBreakerPolicy()
    {
        return HttpPolicyExtensions
            .HandleTransientHttpError()
            .CircuitBreakerAsync(5, TimeSpan.FromSeconds(30));
    }
}
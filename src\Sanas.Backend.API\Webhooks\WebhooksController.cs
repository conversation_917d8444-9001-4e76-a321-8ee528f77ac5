﻿using System.Text.Json.Nodes;
using Microsoft.AspNetCore.Mvc;
using Sanas.Backend.Data.Repositories.Interfaces;

namespace Sanas.Backend.API.Webhooks
{
    [ApiController]
    [Route("[Controller]")]
    [ApiExplorerSettings(IgnoreApi = true)]
    public class WebhooksController(
        ILogger<WebhooksController> logger,
        IConfiguration configuration,
        IPortalUserRepository portalUserRepo) : ControllerBase
    {
        private readonly string _webhookApiKey = configuration["Auth0ManagementClient:WebhookApiKey"];

        [HttpPost("OnFirstLogin")]
        public async Task<ActionResult> OnFirstLoginAsync(JsonNode userInfo, [FromHeader(Name = "X-SANAS-AUTH-KEY")] string authKey)
        {
            logger.LogInformation("Webhook: User's first time login detected.");

            try
            {
                if (authKey != _webhookApiKey)
                {
                    logger.LogWarning("Webhook: Invalid auth key specified.");
                    return Unauthorized("Webhook: Invalid auth key specified.");
                }

                var email = userInfo["email"].GetValue<string>();

                await portalUserRepo.SetAccountVerifiedAsync(email);

                return Ok(true);
            }
            catch (Exception e)
            {
                logger.LogError("Webhook: An error occurred while setting account verified upon first time login for user. {Error}", e);
                return StatusCode(500, e.Message);
            }
        }
    }
}

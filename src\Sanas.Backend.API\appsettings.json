{"Logging": {"LogLevel": {"Default": "Information", "Microsoft.AspNetCore": "Warning"}}, "Serilog": {"LogGroup": "/ecs/portal-v2-api-dev-api", "MinimumLevel": {"Default": "Information", "Override": {"Microsoft": "Warning", "System": "Warning"}}, "Region": "us-west-2", "Using": ["AWS.Logger.SeriLog"], "WriteTo": [{"Name": "AWSSeriLog"}]}, "AllowedHosts": "*", "AzLogAnalytics": {"ApiEndpoint": "https://api.loganalytics.io", "WorkspaceId": "db2bec96-ffc2-4255-ba2b-fbca3635b03c", "TenantId": "d21c464b-5b68-4756-b62d-b05b5cfbd449", "ClientId": "70074a30-db21-438e-990a-119357b14513", "ClientSecret": "****************************************"}}
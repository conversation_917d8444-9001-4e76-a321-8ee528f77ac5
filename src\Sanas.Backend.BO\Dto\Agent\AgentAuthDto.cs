﻿namespace Sanas.Backend.BO.Dto;

public class AgentAuthDto
{
    public int WorkspaceId { get; set; }

    public int? TeamId { get; set; }

    public int AgentId { get; set; }

    public string AgentName { get; set; }

    public string LoginId { get; set; }

    public bool IsDisabled { get; set; }

    public DateTime? CreatedAtUtc { get; set; }

    public DateTime? LastSeenUtc { get; set; }

    public bool IsPasswordTemporary { get; set; }

    public AgentPreferencesDto AgentPreferences { get; set; }

    public static AgentAuthDto FromDataModel(Data.Models.Agent agent)
    {
        if (agent == null)
        {
            return null;
        }

        return new AgentAuthDto
        {
            WorkspaceId = agent.WorkspaceId,
            TeamId = agent.TeamId,
            AgentId = agent.AgentId,
            AgentName = agent.Name,
            LoginId = agent.LoginId,
            IsDisabled = agent.IsDisabled,
            CreatedAtUtc = agent.CreatedUtc,
            LastSeenUtc = agent.LastSeenUtc,
            IsPasswordTemporary = agent.IsPasswordTemporary,
            AgentPreferences = AgentPreferencesDto.FromDataModel(agent.AgentPreferences)
        };
    }
}
﻿using Sanas.Backend.BO.Extensions;

namespace Sanas.Backend.BO.Dto.Agent
{
    // This DTO is for version 2+ of agent authentication / auto login and reauthentication.
    public class AgentAuthExDto : AgentAuthDto
    {
        public Dictionary<string, dynamic> AppConfig { get; set; }

        public static new AgentAuthExDto FromDataModel(Data.Models.Agent agent)
        {
            if (agent == null)
            {
                return null;
            }

            return new AgentAuthExDto
            {
                WorkspaceId = agent.WorkspaceId,
                TeamId = agent.TeamId,
                AgentId = agent.AgentId,
                AgentName = agent.Name,
                LoginId = agent.LoginId,
                IsDisabled = agent.IsDisabled,
                CreatedAtUtc = agent.CreatedUtc,
                LastSeenUtc = agent.LastSeenUtc,
                IsPasswordTemporary = agent.IsPasswordTemporary,
                AgentPreferences = AgentPreferencesDto.FromDataModel(agent.AgentPreferences),
                AppConfig = agent.Workspace.AppConfig.JsonToDictionary()
            };
        }
    }
}

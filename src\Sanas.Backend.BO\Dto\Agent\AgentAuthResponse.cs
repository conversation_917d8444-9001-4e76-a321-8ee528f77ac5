﻿using Sanas.Backend.Data.Models;

namespace Sanas.Backend.BO.Dto.Agent;

public class AgentAuthResponse
{
    public bool Success { get; set; } = false;

    public string Message { get; set; } = string.Empty;

    public string AccessToken { get; set; } = string.Empty;

    public string RefreshToken { get; set; } = string.Empty;

    public bool IsLoggedIn { get; set; }

    public bool IsSessionCheck { get; set; }

    public string LastMachine { get; set; }

    public string ModelType { get; set; }

    public AgentAuthExDto AgentInfo { get; set; }

    public DateTime AccessTokenExpiry { get; set; }

    public DateTime RefreshTokenExpiry { get; set; }
    
    public DateTime CreatedAt { get; set; }
}

﻿namespace Sanas.Backend.BO.Dto.ResponseDto;

public class AgentAuthenticationResponseDto
{
    public bool Success { get; set; } = false;

    public string Message { get; set; } = string.Empty;

    public string AccessToken { get; set; } = string.Empty;

    public string RefreshToken { get; set; } = string.Empty;

    public AgentAuthDto AgentInfo { get; set; }

    public DateTime AccessTokenExpiry { get; set; }

    public DateTime RefreshTokenExpiry { get; set; }

    public DateTime CreatedAt { get; set; }
}
﻿using System.ComponentModel.DataAnnotations;

namespace Sanas.Backend.BO.Dto;

public class AgentCreds
{
    [Required]
    [MinLength(3, ErrorMessage = "User ID should be contain minimum 3 characters.")]
    [MaxLength(32, ErrorMessage = "User ID cannot be more than 32 characters long.")]
    [RegularExpression(@"^[a-zA-Z0-9@_\-\.]+$", ErrorMessage = "User ID is in invalid format")]

    public string LoginId { get; set; }

    [MaxLength(200)]
    public string Name { get; set; }

    [Required]
    [Length(3, 200)]
    public string Password { get; set; }

    public AgentCreds()
    {
    }

    public AgentCreds(string loginId, string name, string password)
    {
        LoginId = loginId;
        Name = name;
        Password = password;
    }
}

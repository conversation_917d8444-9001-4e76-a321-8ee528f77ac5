﻿using Sanas.Backend.BO.Extensions;

namespace Sanas.Backend.BO.Dto;

public class AgentDto : AgentLastCallDto
{
    public int WorkspaceId { get; set; }

    public int TeamId { get; set; }

    public int AgentId { get; set; }

    public string AgentName { get; set; }

    public string LoginId { get; set; }

    public bool IsDisabled { get; set; }

    public DateTime? CreatedAtUtc { get; set; }

    public DateTime? LastSeenUtc { get; set; }

    public DateTime? LastLoginUtc { get; set; }

    public bool IsPasswordTemporary { get; set; }

    public IcpCheckStatus IcpCheckStatus { get;set; }

    public AgentPreferencesDto AgentPreferences { get; set; }

    [System.Text.Json.Serialization.JsonIgnore]
    public Dictionary<string, dynamic> AppConfig { get; set; }

    public static AgentDto FromDataModel(Data.Models.Agent agent)
    {
        if (agent == null)
        {
            return null;
        }

        var agentDto = new AgentDto
        {
            WorkspaceId = agent.WorkspaceId,
            TeamId = agent.TeamId,
            AgentId = agent.AgentId,
            AgentName = agent.Name,
            LoginId = agent.LoginId,
            IsDisabled = agent.IsDisabled,
            CreatedAtUtc = agent.CreatedUtc,
            LastSeenUtc = agent.LastSeenUtc,
            LastLoginUtc = agent.LastLoginUtc,
            MachineName = agent.LastMachine,
            AppVersion = agent.LastAppVersion,
            IcpCheckStatus= (IcpCheckStatus)agent.IcpStatus,
            IsPasswordTemporary = agent.IsPasswordTemporary,
            Voice = GetVoiceGender(agent.AgentPreferences),
            SanasStatus = GetAppStatus(agent.AppStatus),
            AgentPreferences = AgentPreferencesDto.FromDataModel(agent.AgentPreferences),
            AppConfig = agent.Workspace?.AppConfig?.JsonToDictionary()
        };

        if (agent.Workspace is not null)
        {
            agentDto.IsDisabled |= agent.Workspace.IsDisabled;
        }

        if (agent.Team is not null)
        {
            agentDto.IsDisabled |= agent.Team.IsDisabled;
        }

        return agentDto;
    }

    private static string GetVoiceGender(Data.Models.AgentPreferences preferences)
    {
        string voice = "Masculine";

        if (preferences is not null && preferences.AgentVoice == 0)
        {
            voice = "Feminine";
        }

        return voice;
    }

    private static string GetAppStatus(int value)
    {
        switch (value)
        {
            case 1: return "Enabled";
            case 2: return "Disabled";
            default: return "Logged Out";
        }
    }
}
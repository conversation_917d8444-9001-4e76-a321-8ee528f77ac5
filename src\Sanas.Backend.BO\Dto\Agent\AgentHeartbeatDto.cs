﻿using System.ComponentModel.DataAnnotations;

namespace Sanas.Backend.BO.Dto.Agent;

public class AgentHeartbeatDto
{
    [Required]
    public string LoginId { get; set; }

    [Required]
    public string WorkspaceKey { get; set; }

    [Required]
    public bool IsOnCall { get; set; }

    [Required]
    public string MachineName { get; set; }

    [Required]
    public string AppVersion { get; set; }

    public string OnCallProcessName { get; set; } = string.Empty;
}
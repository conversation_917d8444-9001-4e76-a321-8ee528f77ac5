﻿namespace Sanas.Backend.BO.Dto;

public class AgentLastLoginDto
{
    public string LoginId { get; set; }

    public string WorkspaceKey { get; set; }

    public DateTime? LastLoginUtc { get; set; }

    public DateTime? LastLogoutUtc { get; set; }

    public string LastMachine { get; set; }

    public string LastAppVersion { get; set; }

    public bool IsLoggedIn
    {
        get
        {
            if (LastLoginUtc.HasValue)
            {
                if (LastLogoutUtc.HasValue)
                {
                    if (LastLogoutUtc > LastLoginUtc)
                    {
                        // has logged out.
                        return false;
                    }
                }

                // still logged in
                return true;
            }

            // Probably never logged in
            return false;
        }
    }
}

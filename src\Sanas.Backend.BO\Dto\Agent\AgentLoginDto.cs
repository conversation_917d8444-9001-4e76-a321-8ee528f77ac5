﻿using System.ComponentModel.DataAnnotations;

namespace Sanas.Backend.BO.Dto.RequestDto;

public class AgentLoginDto
{
    [Required]
    public string WorkspaceKey { get; set; }

    [Required]
    public string LoginId { get; set; }

    [Required]
    public string Password { get; set; }

    [Required]
    public string MachineName { get; set; }

    [Required]
    public string AppVersion { get; set; }

    public bool SkipLastLoginCheck { get; set; }

    // public OverallCompatibilityReport IcpResult { get; set; }
}
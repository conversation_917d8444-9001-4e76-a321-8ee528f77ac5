﻿namespace Sanas.Backend.BO.Dto;

public class AgentPreferencesDto
{
    public int AgentPreferenceId { get; set; }

    public bool IsSynthesisEnabled { get; set; }

    public bool IsFeedbackSubmissionEnabled { get; set; }

    public bool IsAlcEnabled { get; set; }

    public bool IsAutoLoginEnabled { get; set; }

    public int AgentVoice { get; set; }

    public int AgentAccent { get; set; }

    public int AgentVad { get; set; }

    public static AgentPreferencesDto FromDataModel(Data.Models.AgentPreferences preferences)
    {
        if (preferences == null)
        {
            return null;
        }

        return new AgentPreferencesDto
        {
            AgentPreferenceId = preferences.AgentPreferencesId,
            IsSynthesisEnabled = preferences.IsSynthesisEnabled,
            IsFeedbackSubmissionEnabled = preferences.IsFeedbackSubmissionEnabled,
            IsAlcEnabled = preferences.IsAlcEnabled,
            IsAutoLoginEnabled = preferences.IsAutologinEnabled,
            AgentAccent = preferences.AgentAccent,
            AgentVad = preferences.AgentVad,
            AgentVoice = preferences.AgentVoice,
        };
    }
}
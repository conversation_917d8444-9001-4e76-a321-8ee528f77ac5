using System.ComponentModel.DataAnnotations;
using Sanas.Backend.Data.Models;

namespace Sanas.Backend.BO.Dto.Agent
{
    public class AgentSystemInfoDto
    {
        [Required]
        public string SysUserProfileType { get; set; }

        [Required]
        public string SysUserName { get; set; }

        [Required]
        public string SysDomainName { get; set; }
        
        public bool HasAgentSystemInfoChanged(AgentSystemInfoDto other)
        {
            if (other == null)
            {
                return true;
            }

            return SysUserProfileType != other.SysUserProfileType ||
                   !string.Equals(SysUserName, other.SysUserName, StringComparison.OrdinalIgnoreCase) ||
                   !string.Equals(SysDomainName, other.SysDomainName, StringComparison.OrdinalIgnoreCase);
        }
    }
}
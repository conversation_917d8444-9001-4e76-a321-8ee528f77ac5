﻿namespace Sanas.Backend.BO.Dto.RequestDto;

public class AgentUpdateDto
{
    public string Name { get; set; }

    public int? TeamId { get; set; }

    public string Password { get; set; }

    public string LoginId { get; set; }

    [System.Text.Json.Serialization.JsonIgnore]
    public string IcpResult { get; set; }

    [System.Text.Json.Serialization.JsonIgnore]
    public bool? IsDisabled { get; set; }

    [System.Text.Json.Serialization.JsonIgnore]
    public string MachineName { get; set; }

    [System.Text.Json.Serialization.JsonIgnore]
    public string AppVersion { get; set; }

    [System.Text.Json.Serialization.JsonIgnore]
    public DateTime? LastEngaged { get; set; }
}
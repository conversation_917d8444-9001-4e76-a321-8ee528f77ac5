﻿using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Sanas.Backend.BO.Dto.RequestDto;

public class AgentLoginV3Dto
{
    [Required]
    [RegularExpression("^[a-zA-Z0-9]+-.*$")]
    public string LicenseKey { get; set; }

    [Required]
    public string MachineName { get; set; }

    [Required]
    public string AppVersion { get; set; }

    public bool SkipLastLoginCheck { get; set; }

    [JsonIgnore]
    public string WorkspaceKey
    {
        get
        {
            string workspaceKey = null;
            if (!string.IsNullOrWhiteSpace(LicenseKey) && LicenseKey.Contains('-', StringComparison.OrdinalIgnoreCase))
            {
                workspaceKey = LicenseKey[0..LicenseKey.IndexOf('-', StringComparison.OrdinalIgnoreCase)];
            }

            return workspaceKey;
        }
    }

    [JsonIgnore]
    public string LoginId
    {
        get
        {
            string loginId = null;
            if (!string.IsNullOrWhiteSpace(LicenseKey) && LicenseKey.Contains('-', StringComparison.OrdinalIgnoreCase))
            {
                loginId = LicenseKey[(LicenseKey.IndexOf('-', StringComparison.OrdinalIgnoreCase) + 1)..];
            }

            return loginId;
        }
    }
}
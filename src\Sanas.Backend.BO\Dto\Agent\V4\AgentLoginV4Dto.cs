﻿using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;
using Sanas.Backend.BO.Dto.Agent;
using Sanas.Backend.Data.Models;

namespace Sanas.Backend.BO.Dto.RequestDto.V4;

public class AgentLoginV4Dto: AgentSystemInfoDto
{
    [Required]
    [RegularExpression("^[a-zA-Z0-9]+-.*$")]
    public string LicenseKey { get; set; }

    [Required]
    public string MachineName { get; set; }

    [Required]
    public string AppVersion { get; set; }
    
    [Required]
    public Guid InstallerId { get; set; }
    
    [Required]
    public ActivationMechanismName ActivationMechanism { get; set; }

    public bool SkipLastLoginCheck { get; set; }

    [JsonIgnore]
    public string WorkspaceKey
    {
        get
        {
            string workspaceKey = null;
            if (!string.IsNullOrWhiteSpace(LicenseKey) && LicenseKey.Contains('-', StringComparison.OrdinalIgnoreCase))
            {
                workspaceKey = LicenseKey[0..LicenseKey.IndexOf('-', StringComparison.OrdinalIgnoreCase)];
            }

            return workspaceKey;
        }
    }

    [JsonIgnore]
    public string LoginId
    {
        get
        {
            string loginId = null;
            if (!string.IsNullOrWhiteSpace(LicenseKey) && LicenseKey.Contains('-', StringComparison.OrdinalIgnoreCase))
            {
                loginId = LicenseKey[(LicenseKey.IndexOf('-', StringComparison.OrdinalIgnoreCase) + 1)..];
            }

            return loginId;
        }
    }
}
﻿namespace Sanas.Backend.Data.Models;

public partial class AppDownloadDto
{
    public int DownloadId { get; set; }

    public int ReleaseId { get; set; }

    public string CountryCode { get; set; }

    public int ModelTypeId { get; set; }

    public string ModelType { get; set; }

    public string ExeDownloadUrl { get; set; }

    public string MsiDownloadUrl { get; set; }

    public long MsiFileSize { get; set; }

    public long ExeFileSize { get; set; }

    public static AppDownloadDto FromModel(AppDownload model)
    {
        if (model == null)
        {
            return null;
        }

        return new AppDownloadDto
        {
            DownloadId = model.DownloadId,
            ReleaseId = model.ReleaseId,
            CountryCode = model.CountryCode,
            ModelTypeId = model.ModelTypeId,
            ExeDownloadUrl = model.ExeDownloadUrl,
            MsiDownloadUrl = model.MsiDownloadUrl,
            ModelType = model.ModelType.Name
        };
    }
}

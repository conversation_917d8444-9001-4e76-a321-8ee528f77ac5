﻿using System.ComponentModel.DataAnnotations;
using Sanas.Backend.Data.Models;

namespace Sanas.Backend.BO.Dto
{
    public class AppReleaseCreateDto
    {
        public class DownloadDto
        {
            [Required]
            public int ModelTypeId { get; set; }

            [Required]
            public string CountryCode { get; set; }

            [Required]
            public string ExeDownloadUrl { get; set; }

            [Required]
            public string MsiDownloadUrl { get; set; }

            public static AppDownload ToDataModel(DownloadDto appDownload)
            {
                return new AppDownload
                {
                    CountryCode = appDownload.CountryCode,
                    ModelTypeId = appDownload.ModelTypeId,
                    ExeDownloadUrl = appDownload.ExeDownloadUrl,
                    MsiDownloadUrl = appDownload.MsiDownloadUrl,
                };
            }
        }

        [Required]
        public string Version { get; set; }

        [Required]
        public DateTimeOffset ReleaseDate { get; set; }

        public string ReleaseNotes { get; set; }

        [Required]
        [MinLength(1)]
        public DownloadDto[] Downloads { get; set; }

        public static AppRelease ToDataModel(AppReleaseCreateDto releaseDto)
        {
            return new AppRelease
            {
                ReleaseDate = releaseDto.ReleaseDate,
                ReleaseNotes = releaseDto.ReleaseNotes,
                Version = releaseDto.Version,
                CreatedUtc = DateTime.UtcNow,
                UpdatedUtc = DateTime.UtcNow,
                AppDownloads = releaseDto.Downloads.Select(DownloadDto.ToDataModel).ToList()
            };
        }
    }
}

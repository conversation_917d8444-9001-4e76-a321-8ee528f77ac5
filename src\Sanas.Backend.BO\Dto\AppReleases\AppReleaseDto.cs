﻿using System.Collections.ObjectModel;
using Sanas.Backend.Data.Models;

namespace Sanas.Backend.BO.Dto.AppReleases;

public class AppReleaseDto
{
    public int ReleaseId { get; set; }

    public DateTimeOffset ReleaseDate { get; set; }

    public string Version { get; set; }

    public string ReleaseNotes { get; set; }

    public Collection<AppDownloadDto> AppDownloads { get; set; }

    public static AppReleaseDto FromModel(AppRelease model)
    {
        if (model == null)
        {
            return null;
        }

        return new AppReleaseDto
        {
            ReleaseId = model.ReleaseId,
            Version = model.Version,
            ReleaseNotes = model.ReleaseNotes,
            ReleaseDate = model.ReleaseDate,
            AppDownloads = new Collection<AppDownloadDto>(model.AppDownloads.Select(AppDownloadDto.FromModel).ToList())
        };
    }
}

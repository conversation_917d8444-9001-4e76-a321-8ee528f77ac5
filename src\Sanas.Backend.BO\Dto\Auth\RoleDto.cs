﻿using Sanas.Backend.Data.Models;

namespace Sanas.Backend.BO.Dto;

public class RoleDto
{
    public int RoleId { get; set; }

    public string Name { get; set; }

    public string Description { get; set; }

    public bool IsSanasRole { get; set; }

    public static RoleDto FromModel(Role role)
    {
        return new RoleDto
        {
            RoleId = role.RoleId,
            Name = role.Name,
            Description = role.Description,
            IsSanasRole = role.IsSanasRole
        };
    }
}

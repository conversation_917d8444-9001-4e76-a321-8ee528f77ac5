﻿namespace Sanas.Backend.BO.Dto;

public class UserScope
{
    public int WorkspaceId { get; set; }

    public IList<int> TeamIds { get; set; }

    public IList<RoleDto> Roles { get; set; }

    public IList<string> Permissions { get; set; }

    public UserScope(int workspaceId, IList<RoleDto> roles = null, IList<int> teamIds = null)
    {
        WorkspaceId = workspaceId;
        Roles = roles ?? [];
        TeamIds = teamIds ?? [];
    }

    public UserScope()
    {
        Roles = [];
        TeamIds = [];
    }
}

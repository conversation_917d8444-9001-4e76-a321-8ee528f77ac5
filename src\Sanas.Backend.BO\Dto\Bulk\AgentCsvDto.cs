﻿using System.Text.RegularExpressions;

namespace Sanas.Backend.BO.Dto;

public record AgentCsvDto
{
    public string LoginId { get; set; }

    public string TeamName { get; set; }

    public string Password { get; set; }
    
    public string Name { get; set; }

    public int TeamId { get; set; }

    public string Error { get; set; }

    public bool IsValid(out string error)
    {
        error = null;
        string pattern = @"^[a-zA-Z0-9@_\-\.]+$";
        Regex regex = new Regex(pattern);

        if (string.IsNullOrWhiteSpace(LoginId))
        {
            error = "User ID is empty. This field is mandatory.";
        }
        else if (LoginId.Length < 3)
        {
            error = "User ID should contain minimum 3 characters.";
        }
        else if (LoginId.Length > 32)
        {
            error = "User ID cannot be more than 32 characters long.";
        }
        else if (!regex.IsMatch(LoginId))
        {
            error = "Invalid User ID format. Only letters, digits, special characters[.-_@] are allowed.";
        }

        return string.IsNullOrWhiteSpace(error);
    }
}

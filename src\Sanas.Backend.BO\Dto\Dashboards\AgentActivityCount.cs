﻿namespace Sanas.Backend.BO.Dto.Dashboards;

public sealed record AgentActivityCount
{
    public DateTime DateTimeUtc { get; set; }

    public long ActiveUsersCount { get; set; }

    public long EngagedUsersCount { get; set; }

    public AgentActivityCount()
    {
    }

    public AgentActivityCount(DateTime dateTimeUtc, long activeUsersCount, long engagedUsersCount)
    {
        DateTimeUtc = dateTimeUtc;
        ActiveUsersCount = activeUsersCount;
        EngagedUsersCount = engagedUsersCount;
    }
}

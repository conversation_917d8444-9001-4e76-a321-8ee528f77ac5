﻿namespace Sanas.Backend.BO.Dto.Dashboards;

public sealed record AgentActivityReport
{
    public int TotalLicenseCount { get; set; }

    public int UsedLicenseCount { get; set; }

    public int LoggedInUsersCount { get; set; }

    public long CurrentPeriodEngageCount { get; set; }
    
    public long PreviousPeriodEngageCount { get; set; }

    public IList<AgentActivityCount> AgentsEngageRates { get; set; } = [];
}

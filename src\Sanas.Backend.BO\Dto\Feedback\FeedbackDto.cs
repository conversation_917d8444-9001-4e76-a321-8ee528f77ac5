﻿namespace Sanas.Backend.BO.Dto;

public class FeedbackDto
{
    public int FeedbackId { get; set; }

    public int WorkspaceId { get; set; }

    public int AgentId { get; set; }

    public string LoginId { get; set; }

    public string AppVersion { get; set; }

    public string Description { get; set; }

    public short SentimentType { get; set; }

    public string AttachmentName { get; set; }

    public string TrackingNumber { get; set; }

    public string SanasUniqueId { get; set; }

    public DateTime CreatedUtc { get; set; }

    public DateTime UpdatedUtc { get; set; }

    public byte[] Attachment { get; set; } = null;

    public static FeedbackDto FromDataModel(Data.Models.Feedback feedback)
    {
        if (feedback == null)
        {
            return null;
        }

        return new FeedbackDto
        {
            FeedbackId = feedback.FeedbackId,
            WorkspaceId = feedback.WorkspaceId,
            AgentId = feedback.AgentId,
            LoginId = feedback.LoginId,
            AppVersion = feedback.AppVersion,
            Description = feedback.Description,
            SentimentType = feedback.SentimentType,
            AttachmentName = feedback.AttachmentNumber,
            TrackingNumber = feedback.TrackingNumber,
            SanasUniqueId = feedback.SanasUniqueId,
            CreatedUtc = feedback.CreatedUtc,
            UpdatedUtc = feedback.UpdatedUtc,
        };
    }
}
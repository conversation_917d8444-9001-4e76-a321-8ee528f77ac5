﻿namespace Sanas.Backend.Data;

public class FreshdeskTicketDto
{
    public string ReporterEmail { get; set; } = "<EMAIL>";
    
    public string Subject { get; set; } = string.Empty;

    public string Description { get; set; } = string.Empty;

    public string Status { get; set; } = "2";

    public string Priority { get; set; } = "2";

    public string IssueType { get; set; } = "Application Issue";

    public string AttachmentName { get; set; } = string.Empty;

    public byte[] Attachment { get; set; } = null;

    public string CompanyName { get; set; } = string.Empty;

    public string LineOfBusiness { get; set; } = "Sanas Internal";

    public string ProductType { get; set; } = "Accent Translation";

    public string WorkspaceKey { get; set; } = string.Empty;

    public string RequestorName { get; set; } = string.Empty;
    
    public string ApplicationVersion { get; set; } = string.Empty;
    
    public string SanasUniqueId { get; set; } = string.Empty;
    
    public string SentimentType { get; set; } = string.Empty;
}
using Newtonsoft.Json;

namespace Sanas.Backend.BO.Dto.Feedback.SystemSummary;

public partial class ComputerSystem
{
    /* 
        "Name" is as a short identifier for "DNSHostName"
        ref: https://learn.microsoft.com/en-us/windows/win32/cimwin32prov/win32-computersystem

        [JsonProperty("Name")]
        public string Name { get; set; }
    */

    [JsonProperty("DNSHostName")]
    public string MachineName { get; set; } = string.Empty;

    [JsonProperty("HypervisorPresent")]
    public bool IsHypervisorPresent { get; set; } = false;

    [JsonProperty("Manufacturer")]
    public string ComputerManufacturer { get; set; } = string.Empty;

    [JsonProperty("Model")]
    public string ComputerModel { get; set; } = string.Empty;

    [JsonProperty("NumberOfLogicalProcessors")]
    public long? NumberOfLogicalProcessors { get; set; } = null;

    [JsonProperty("NumberOfProcessors")]
    public long? NumberOfProcessors { get; set; } = null;

    [JsonProperty("PowerState")]
    public long? PowerState { get; set; } = null;

    [JsonProperty("TotalPhysicalMemory")]
    public long? TotalPhysicalMemory { get; set; } = null;

    [JsonProperty("UserName")]
    public string UserName { get; set; } = string.Empty;
}
using Newtonsoft.Json;

namespace Sanas.Backend.BO.Dto.Feedback.SystemSummary;

public partial class OS
{
    [JsonProperty("Caption")]
    public string OSName { get; set; } = string.Empty;

    [JsonProperty("LastBootUpTime")]
    public string OSLastBootUpTime { get; set; } = string.Empty;

    [<PERSON>sonProperty("Manufacturer")]
    public string OSManufacturer { get; set; } = string.Empty;

    [<PERSON>sonProperty("OSArchitecture")]
    public string OSArchitecture { get; set; } = string.Empty;

    [JsonProperty("Version")]
    public string OSVersion { get; set; } = string.Empty;
}
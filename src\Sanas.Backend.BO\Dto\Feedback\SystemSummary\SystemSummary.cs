using Newtonsoft.Json;

namespace Sanas.Backend.BO.Dto.Feedback.SystemSummary;

// <summary>
// Select attributes from SystemSummary.json when feedback is submitted 
// </summary>
public partial class SystemSummary
{
    [JsonProperty(nameof(ComputerSystem))]
    public ComputerSystem[] ComputerSystem { get; set; } = null;

    [JsonProperty(nameof(OS))]
    public OS[] OS { get; set; } = null;

    [JsonProperty(nameof(CPU))]
    public CPU[] CPU { get; set; } = null;
}
namespace Sanas.Backend.BO.Dto;

public class ApplicationBenchmark
{
    public IList<string> CompatibleDailers { get; set; } = [];

    public IList<string> NonCriticallyIncompatibleDialers { get; set; } = new List<string>();

    public ApplicationBenchmark()
    {
    }

    public ApplicationBenchmark(IList<string> compatibleDailers, IList<string> nonCriticallyIncompatibleDialers)
    {
        CompatibleDailers = compatibleDailers;
        NonCriticallyIncompatibleDialers = nonCriticallyIncompatibleDialers;
    }
}
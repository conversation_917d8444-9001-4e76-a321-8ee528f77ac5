﻿using System.Text.Json.Serialization;

namespace Sanas.Backend.BO.Dto;

public class CompatibilityChecklist
{
    public Version Version { get; set; }

    [JsonPropertyName("HeadsetBenchmark")]
    public HeadsetBenchmark HeadsetBenchmark { get; set; }

    [JsonPropertyName("OperatingSystemBenchmark")]
    public OperatingSystemBenchmark OperatingSystemBenchmark { get; set; }

    [JsonPropertyName("CpuBenchmarks")]
    public IList<CpuBenchmark> CpuBenchmarks { get; set; }

    [JsonPropertyName("SystemMemoryBenchmark")]
    public SystemMemoryBenchmark SystemMemoryBenchmark { get; set; }

    [JsonPropertyName("ApplicationBenchmarks")]
    public ApplicationBenchmark ApplicationBenchmarks { get; set; }

    [JsonPropertyName("UnsupportedAudioProcessingApps")]
    public IList<string> UnsupportedAudioProcessingApps { get; set; }
}

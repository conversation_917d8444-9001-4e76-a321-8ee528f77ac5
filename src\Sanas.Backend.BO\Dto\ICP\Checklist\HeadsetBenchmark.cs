using System.Text.Json.Serialization;

namespace Sanas.Backend.BO.Dto;

public class HeadsetBenchmark
{
    // Headsets neither compatible nor critically incompatible falls under Non critically incompatible
    // Critically incompatible will be:-
    //     1. Any Bluetooth headset
    //     2. Any Webcam headset
    //     3. Any Aux based
    //     4. Any Microphone Array
    public IList<string> CompatibleHeadsets { get; set; } = new List<string>();

    public IList<string> CriticallyIncompatibleHeadsets { get; set; } = new List<string>();

    [JsonConstructor]
    public HeadsetBenchmark()
    {
    }

    public HeadsetBenchmark(IList<string> compatibleHeadsets, IList<string> criticallyIncompatibleHeadsets)
    {
        CompatibleHeadsets = compatibleHeadsets;
        CriticallyIncompatibleHeadsets = criticallyIncompatibleHeadsets;
    }
}
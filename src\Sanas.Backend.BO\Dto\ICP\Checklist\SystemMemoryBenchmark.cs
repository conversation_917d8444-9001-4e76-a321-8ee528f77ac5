using System.Text.Json.Serialization;

namespace Sanas.Backend.BO.Dto;

public class SystemMemoryBenchmark
{
    public float IncompatibleThresholdInGb { get; set; } = 0;

    public float CriticalIncompatibleThresholdInGb { get; set; } = 0;

    [JsonConstructor]
    public SystemMemoryBenchmark()
    {
    }

    public SystemMemoryBenchmark(float incompatibleThreshold, float criticalThreshold)
    {
        IncompatibleThresholdInGb = incompatibleThreshold;
        CriticalIncompatibleThresholdInGb = criticalThreshold;
    }
}
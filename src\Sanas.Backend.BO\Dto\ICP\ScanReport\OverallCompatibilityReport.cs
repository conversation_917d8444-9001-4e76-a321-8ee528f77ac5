namespace Sanas.Backend.BO.Dto;

public class OverallCompatibilityReport
{
    public CompatibilityState OverallCompatibilityState { get; set; } = CompatibilityState.None;
    public string MachineName { get; set; } = string.Empty;
    public bool? IsAutomatedCheck { get; set; }
    public ApplicationReport ApplicationReport { get; set; }
    public AudioProcessingAppReport AudioProcessingAppReport { get; set; }
    public CpuClockSpeedReport CpuClockSpeedReport { get; set; }
    public CpuCoresReport CpuCoresReport { get; set; }
    public CpuGenerationReport CpuGenerationReport { get; set; }
    public CpuManufacturerAndModelReport CpuManufacturerAndModelReport { get; set; }
    public DefaultMicReport DefaultMicReport { get; set; }
    public HeadsetReport HeadsetReport { get; set; }
    public OperatingSystemReport OperatingSystemReport { get; set; }
    public SystemMemoryReport SystemMemoryReport { get; set; }
}

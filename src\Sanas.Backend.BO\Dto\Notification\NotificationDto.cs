﻿namespace Sanas.Backend.BO.Dto;

public class NotificationDto
{
    public int NotificationId { get; set; }

    public NotificationType NotificationType { get; set; }

    public string Content { get; set; }

    public bool IsRead { get; set; }

    public int WorkspaceId { get; set; }

    public int TeamId { get; set; }

    public string TeamName { get; set; }

    public int AgentId { get; set; }

    public string AgentName { get; set; }

    public string LoginId { get; set; }

    public DateTime CreatedAtUtc { get; set; }

    internal IEnumerable<int> ReadByUserIds { get; set; }

    public static NotificationDto FromModel(Data.Models.Notification record)
    {
        var newInstance = new NotificationDto
        {
            NotificationId = record.NotificationId,
            Content = record.Content,
            NotificationType = (NotificationType)record.NotificationType,
            ReadByUserIds = record.ReadbyUserIds,
            WorkspaceId = record.WorkspaceId,
            TeamId = record.TeamId,
            CreatedAtUtc = record.CreatedUtc,
            TeamName = record.Team?.Name,
            AgentId = record.AgentId,
            AgentName = record.Agent?.Name,
            LoginId = record.Agent?.LoginId
        };

        return newInstance;
    }
}

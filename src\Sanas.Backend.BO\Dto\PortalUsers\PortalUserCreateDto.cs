﻿using System.Collections.ObjectModel;
using System.ComponentModel.DataAnnotations;

namespace Sanas.Backend.BO.Dto.RequestDto;

public class PortalUserCreateDto
{
    [Required]
    [MaxLength(200)]
    public string FirstName { get; set; }

    [Required]
    [MaxLength(200)]
    public string LastName { get; set; }

    [Required]
    [MaxLength(200)]
    [EmailAddress]
    public string Email { get; set; }

    public Collection<RoleDto> Roles { get; set; } = [];
}

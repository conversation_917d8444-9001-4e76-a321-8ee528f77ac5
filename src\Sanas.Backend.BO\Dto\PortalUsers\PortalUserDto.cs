﻿namespace Sanas.Backend.BO.Dto;

public class PortalUserDto
{
    public int PortalUserId { get; set; }
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public string Email { get; set; }
    public string Role { get; internal set; }
    internal string NotificationToken { get; set; }
    public UserStatus Status { get; set; }
    public bool IsSanasUser { get; set; }

    public static PortalUserDto FromDataModel(Data.Models.PortalUser portalUser)
    {
        if (portalUser == null)
        {
            return null;
        }

        return new PortalUserDto
        {
            PortalUserId = portalUser.PortalUserId,
            FirstName = portalUser.FirstName,
            LastName = portalUser.LastName,
            Email = portalUser.Email,
            NotificationToken = portalUser.NotificationToken,
            IsSanasUser = portalUser.IsSanasUser
        };
    }

    public static PortalUserDto FromPortalUserLite(Data.Models.PortalUserLite portalUser)
    {
        if (portalUser == null)
        {
            return null;
        }

        return new PortalUserDto
        {
            PortalUserId = portalUser.PortalUserId,
            FirstName = portalUser.FirstName,
            LastName = portalUser.LastName,
            Email = portalUser.Email,
            NotificationToken = portalUser.NotificationToken,
            IsSanasUser = portalUser.IsSanasUser,
            Role = portalUser.RoleName
        };
    }
}
﻿namespace Sanas.Backend.BO.Dto;

public class PortalUserMinDto
{
    public int PortalUserId { get; set; }
    public string FirstName { get; set; }
    public string LastName { get; set; }
    public string Email { get; set; }
    
    public bool IsDisabled { get; set; }

    public static PortalUserMinDto FromDataModel(Data.Models.PortalUser portalUser)
    {
        if (portalUser == null)
        {
            return null;
        }

        return new PortalUserMinDto
        {
            PortalUserId = portalUser.PortalUserId,
            FirstName = portalUser.FirstName,
            LastName = portalUser.LastName,
            Email = portalUser.Email,
            IsDisabled = portalUser.IsDisabled,
        };
    }
}
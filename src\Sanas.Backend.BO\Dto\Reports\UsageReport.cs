﻿namespace Sanas.Backend.BO.Dto;

public class UsageReport
{
    public class UsageItem
    {
        public string UserID { get; set; }
        public DateTimeOffset FirstSeen { get; set; }
        public DateTimeOffset LastSeen { get; set; }
        public string SessionDuration { get; set; }
        public string AppEnabledDuration { get; set; }
        public string TeamName { get; set; }
        public string UserName { get; set; }
    }

    public int Page { get; set; }

    public int PerPage { get; set; }

    public int Total { get; set; }

    public UsageItem[] Items { get; set; }
}
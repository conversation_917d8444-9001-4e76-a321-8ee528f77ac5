﻿using Sanas.Backend.Data;

namespace Sanas.Backend.BO.Dto.RequestDto;

public record RequestOptions : QueryOptions
{
    private int _pageSize = 50;

    private int _pageNumber = 0;

    public int PageNumber
    {
        get => _pageNumber;
        set
        {
            _pageNumber = value;
            Skip = PageNumber <= 0 ? 0 : (PageNumber - 1) * Fetch;
        }
    }

    public int PageSize
    {
        get => _pageSize;
        set
        {
            _pageSize = value;
            Fetch = value == 0 ? 50 : value;
            Skip = PageNumber <= 0 ? 0 : (PageNumber - 1) * Fetch;
        }
    }
}

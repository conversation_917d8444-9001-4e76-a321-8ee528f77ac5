using System.ComponentModel.DataAnnotations;

namespace Sanas.Backend.BO.Dto;

public class NCUser
{
    [Required]
    public string FirstName { get; set; }

    [Required]
    public string LastName { get; set; }

    [Required]
    [EmailAddress]
    public string Email { get; set; }

    [Required]
    public string Password { get; set; }

    [Required]
    [Range(1, 100000)]
    public int AllocatedSeats { get; set; }
}

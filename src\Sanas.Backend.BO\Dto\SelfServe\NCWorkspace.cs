﻿namespace Sanas.Backend.BO.Dto;

public class NCWorkspace
{
    public WorkspaceDto Workspace { get; set; }
    public AgentDto Agent { get; set; }
    public NCApp AppDetails { get; set; }
    public int LoginCount { get; set; }
    public string[] StepsCompleted { get; set; }

    public static NCWorkspace FromModel(Data.Models.NCWorkspace workspace)
    {
        if (workspace == null)
        {
            return null;
        }

        return new NCWorkspace
        {
            Workspace = WorkspaceDto.FromDataModel(workspace.Workspace),
            LoginCount = workspace.LoginCount,
            StepsCompleted = workspace.StepsCompleted,
            Agent = AgentDto.FromDataModel(workspace.Agent),
            AppDetails = new NCApp
            {
                Version = workspace.AppVersion,
                ExeUrl = workspace.ExeDownloadLink,
                MsiUrl = workspace.MsiDownloadLink,
            }
        };
    }
}
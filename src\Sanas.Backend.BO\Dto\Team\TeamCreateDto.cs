﻿using System.ComponentModel.DataAnnotations;

namespace Sanas.Backend.BO.Dto.RequestDto;

public class TeamCreateDto
{
    public class TeamAgent
    {
        [Required]
        [MinLength(3, ErrorMessage = "User ID should be contain minimum 3 characters.")]
        [MaxLength(32, ErrorMessage = "User ID cannot be more than 32 characters long.")]
        [RegularExpression(@"^[a-zA-Z0-9@_\-\.]+$", ErrorMessage = "User ID is in invalid format")]
        public string LoginId { get; set; }

        [Required]
        public string Password { get; set; }
    }

    [Required]
    [MaxLength(32)]
    public string Name { get; set; }

    public IList<TeamAgent> Agents { get; set; } = [];

    public IList<int> PortalUsersIds { get; set; } = [];

    public static bool IsValid(int workspaceId, TeamCreateDto team, out string errorMessage)
    {
        errorMessage = null;

        if (team == null)
        {
            errorMessage = "Request object is NULL";
        }
        else if(workspaceId <= 0)
        {
            errorMessage = "workspace id is invalid";
        }
        else if (string.IsNullOrWhiteSpace(team.Name))
        {
            errorMessage = "Team name is empty";
        }
        else if (team.Agents.Count > 0)
        {
            if (team.Agents.Any(a => string.IsNullOrWhiteSpace(a.LoginId) || string.IsNullOrWhiteSpace(a.Password)))
            {
                errorMessage = "One or more agents data is invalid.";
            }
        }

        return string.IsNullOrWhiteSpace(errorMessage);
    }
}

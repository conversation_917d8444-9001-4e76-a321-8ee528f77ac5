﻿using System.Collections.ObjectModel;
using Sanas.Backend.Data.Models;

namespace Sanas.Backend.BO.Dto
{
    public class TeamDto
    {
        public int TeamId { get; set; }

        public int WorkspaceId { get; set; }

        public string Name { get; set; }

        public int Agents { get; set; }

        public bool IsDisabled { get; set; }

        public bool IsDefaultTeam { get; set; }

        public Collection<PortalUserMinDto> Supervisors { get; set; }

        public static TeamDto UpdateSupervisors(TeamDto dto,IEnumerable<PortalUser> items)
        {
            Collection<PortalUserMinDto> users = [];
            foreach (var item in items)
            {
                users.Add(PortalUserMinDto.FromDataModel(item));
            }
            
            dto.Supervisors = users;
            return dto;
        }
        
        public static TeamDto FromDataModel(Data.Models.Team team)
        {
            if (team == null)
            {
                return null;
            }
            
            return new TeamDto
            {
                WorkspaceId = team.WorkspaceId,
                Name = team.Name,
                TeamId = team.TeamId,
                IsDisabled = team.IsDisabled,
                IsDefaultTeam = team.IsDefaultTeam,
            };
        }
    }
}

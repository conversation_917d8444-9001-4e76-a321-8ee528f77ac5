﻿using System.ComponentModel.DataAnnotations;

namespace Sanas.Backend.BO.Dto.RequestDto;

public class TeamUpdateDto
{
    [MaxLength(200)]
    public string Name { get; set; }

    public IEnumerable<int> PortalUserIds { get; set; }

    public static bool IsValid(int workspaceId, TeamUpdateDto team, out string errorMessage)
    {
        bool isValid = false;

        if (team == null)
        {
            errorMessage = "Request object is NULL";
        }
        else if (workspaceId <= 0)
        {
            errorMessage = "Workspace ID is not valid";
        }
        else if (string.IsNullOrWhiteSpace(team.Name))
        {
            errorMessage = "Team name is empty";
        }
        else
        {
            errorMessage = null;
            isValid = true;
        }

        return isValid;
    }
}

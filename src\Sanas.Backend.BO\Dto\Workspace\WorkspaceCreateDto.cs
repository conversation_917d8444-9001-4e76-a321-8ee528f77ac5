﻿using System.ComponentModel.DataAnnotations;
using System.Text.Json.Serialization;

namespace Sanas.Backend.BO.Dto.RequestDto
{
    public class WorkspaceCreateDto
    {
        [MaxLength(100)]
        public string Enterprise { get; set; }

        [MaxLength(100)]
        public string BPO { get; set; }

        [MaxLength(100)]
        public string Site { get; set; }

        [Range(1, 100000)]
        public int AllocatedSeats { get; set; }

        [MinLength(0)]
        [MaxLength(10)]
        public UserInvite[] UserInvites { get; set; }

        [Required]
        public string CountryCode { get; set; }

        [Required]
        public string[] ModelTypes { get; set; }

        [JsonIgnore]
        public string ValidationError
        {
            get
            {
                string errorMsg = null;

                if (string.IsNullOrWhiteSpace(Enterprise))
                {
                    errorMsg = "The value of 'Enterprise' is empty";
                }
                else if (string.IsNullOrWhiteSpace(BPO))
                {
                    errorMsg = "The value of 'Process' is empty";
                }
                else if (!string.IsNullOrWhiteSpace(Site))
                {
                    errorMsg = "The value of 'Location' is empty";
                }
                else if (AllocatedSeats <= 0)
                {
                    errorMsg = "Invalid AllocatedSeats value.";
                }
                else if (!string.IsNullOrWhiteSpace(CountryCode))
                {
                    if (!(CountryCode is "IND" or "PHL"))
                    {
                        errorMsg = "Country code is invalid. Accepted values are IND and PHL";
                    }
                }

                return errorMsg;
            }
        }

        public static bool IsValid(WorkspaceCreateDto workspace, out string errorMsg)
        {
            bool isValid = false;

            if (workspace == null)
            {
                errorMsg = "Request object is NULL";
            }
            else if (string.IsNullOrWhiteSpace(workspace.Enterprise))
            {
                errorMsg = "The value for 'Enterprise' is empty";
            }
            else if (string.IsNullOrWhiteSpace(workspace.BPO))
            {
                errorMsg = "The value for 'Process' is empty";
            }
            else if (string.IsNullOrWhiteSpace(workspace.Site))
            {
                errorMsg = "The value for 'Location' is empty";
            }
            else if (workspace.AllocatedSeats <= 0)
            {
                errorMsg = "Invalid AllocatedSeats value.";
            }
            else if (!(workspace.CountryCode is "IND" or "PHL"))
            {
                errorMsg = "Country code is invalid. Accepted values are IND and PHL";
            }
            else
            {
                errorMsg = null;
                isValid = true;
            }

            return isValid;
        }
    }
}

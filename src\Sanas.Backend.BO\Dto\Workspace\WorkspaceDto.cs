﻿using System.Text.Json.Serialization;
using Sanas.Backend.BO.Helpers;
using Sanas.Backend.Data.Models;

namespace Sanas.Backend.BO.Dto;

public class WorkspaceDto
{
    public int WorkspaceId { get; set; }

    public string WorkspaceKey { get; set; }

    public string Enterprise { get; set; }

    public string BPO { get; set; }

    public string Site { get; set; }

    public int AllocatedSeats { get; set; }

    public int LicensedSeats { get; set; }

    public bool IsDisabled { get; set; }

    public string CountryCode { get; set; }

    public string[] ModelTypes { get; set; }

    public ActivationMode ActivationMode { get; set; }
    
    public Guid InstallerId { get; set; }

    public static WorkspaceDto FromDataModel(Workspace workspace)
    {
        if (workspace == null)
        {
            return null;
        }

        return new WorkspaceDto
        {
            WorkspaceId = workspace.WorkspaceId,
            WorkspaceKey = workspace.WorkspaceKey,
            Enterprise = workspace.Enterprise,
            BPO = workspace.BpoSite.BPO,
            Site = workspace.BpoSite.Site,
            AllocatedSeats = workspace.AllocatedSeats,
            IsDisabled = workspace.IsDisabled,
            ModelTypes = workspace.ModelTypes,
            CountryCode = workspace.CountryCode,
            LicensedSeats = workspace is WorkspaceEx ? ((WorkspaceEx)workspace).AgentCount : 0,
            ActivationMode = workspace.ActivationMode,
            InstallerId = workspace.InstallerId,
        };
    }
}

﻿namespace Sanas.Backend.BO.Exceptions;

public class PortalException : Exception
{
    public int ErrorCode { get; private set; }

    public PortalException()
    {
    }

    public PortalException(string message)
        : base(message)
    {
    }

    public PortalException(string message, Exception innerException)
        : base(message, innerException)
    {
    }

    public PortalException(int errorCode, string message)
        : base(message)
    {
        ErrorCode = errorCode;
    }
}

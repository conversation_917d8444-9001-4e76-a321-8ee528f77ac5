﻿using Auth0.ManagementApi.Models;
using Sanas.Backend.BO.Dto;

namespace Sanas.Backend.BO.Extensions;

public static class Auth0UserExtensions
{
    public static UserStatus GetStatus(this User user)
    {
        UserStatus status = UserStatus.Unknown;

        var inviteDate = user.CreatedAt ?? DateTime.Now;        

        if (user.AppMetadata.InviteSentOnUtc is not null)
        {
            try
            {
                inviteDate = DateTime.Parse(user.AppMetadata.InviteSentOnUtc.ToString("O"));
            }
            catch
            {
            }
        }

        if (user == null)
        {
            return status;
        }

        if (user.Blocked ?? false)
        {
            status = UserStatus.Inactive;
        }
        else if (user.EmailVerified ?? false)
        {
            status = UserStatus.Active;
        }
        else
        {
            if (user.CreatedAt.HasValue)
            {
                if (DateTime.UtcNow.Subtract(inviteDate).TotalSeconds > 604800)
                {
                    status = UserStatus.InviteExpired;
                }
                else
                {
                    status = UserStatus.Invited;
                }
            }
        }

        return status;
    }
}


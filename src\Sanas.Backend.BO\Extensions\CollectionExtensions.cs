﻿namespace Sanas.Backend.BO.Extensions;

public static class CollectionExtensions
{
    public static void AddRange<T>(this ICollection<T> list, IEnumerable<T> items)
    {
        if (list == null || items == null)
        {
            return;
        }

        foreach (var item in items)
        {
            list.Add(item);
        }
    }

    public static void ForEach<T>(this IEnumerable<T> collection, Action<T> action)
    {
        foreach (var item in collection)
        {
            action?.Invoke(item);
        }
    }
}

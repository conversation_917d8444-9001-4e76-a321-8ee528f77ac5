﻿using Sanas.Backend.BO.Dto;
using Sanas.Backend.Data.Models;

namespace Sanas.Backend.BO.Extensions;

public static class PortalUserExtensions
{
    public static UserStatus GetStatus(this PortalUser user, int workspaceId = 0)
    {
        if (user == null)
        {
            return UserStatus.Unknown;
        }

        var inviteDate = user.InvitedUtc ?? user.CreatedUtc;

        if (user.IsDisabled || (!user.IsSanasUser && user.WorkspaceUsers.Any(wu => wu.WorkspaceId == workspaceId && wu.IsUserDisabled)))
        {
            return UserStatus.Inactive;
        }
        else if (user.IsAccountVerified)
        {
            return UserStatus.Active;
        }
        else
        {
            if (DateTime.UtcNow.Subtract(inviteDate).TotalSeconds > 604800)
            {
                return UserStatus.InviteExpired;
            }
            else
            {
                return UserStatus.Invited;
            }
        }
    }
}

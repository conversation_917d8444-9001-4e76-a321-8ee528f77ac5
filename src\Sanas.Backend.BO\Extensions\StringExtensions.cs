﻿using System.Text.Json;

namespace Sanas.Backend.BO.Extensions;

public static class StringExtensions
{
    public static Dictionary<string, dynamic> JsonToDictionary(this string json)
    {
        if (json == null)
        {
            return null;
        }

        try
        {
            var appConfig = new Dictionary<string, dynamic>();

            var result = JsonSerializer.Deserialize<Dictionary<string, JsonElement>>(json, new JsonSerializerOptions
            {
                PropertyNamingPolicy = JsonNamingPolicy.CamelCase
            });

            foreach (var item in result)
            {
                dynamic value = item.Value.ValueKind switch
                {
                    JsonValueKind.Array => FromJsonElementArray(item.Value.EnumerateArray()),
                    JsonValueKind.Object => JsonToDictionary(item.Value.ToString()),
                    _ => GetPrimitiveValue(item.Value),
                };

                appConfig.Add(item.Key, value);
            }

            return appConfig;
        }
        catch
        {
            return null;
        }
    }

    private static dynamic[] FromJsonElementArray(JsonElement.ArrayEnumerator arrayEnumerator)
    {
        var values = new List<dynamic>();

        foreach (var item in arrayEnumerator)
        {
            dynamic value = item.ValueKind switch
            {
                JsonValueKind.Array => FromJsonElementArray(item.EnumerateArray()),
                JsonValueKind.Object => JsonToDictionary(item.ToString()),
                _ => GetPrimitiveValue(item),
            };

            values.Add(value);
        }

        return [.. values];
    }

    private static dynamic GetPrimitiveValue(JsonElement element)
    {
        return element.ValueKind switch
        {
            JsonValueKind.String => element.GetString(),
            JsonValueKind.False => false,
            JsonValueKind.True => true,
            JsonValueKind.Number => element.TryGetInt64(out long longVal) ? longVal : element.TryGetDouble(out double dblVal) ? dblVal : 0,
            _ or JsonValueKind.Undefined or JsonValueKind.Null => null,
        };
    }
}

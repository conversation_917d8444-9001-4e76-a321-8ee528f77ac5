﻿using System.Text.Json;
using Microsoft.Extensions.Logging;
using Sanas.Backend.BO.Dto;
using Sanas.Backend.BO.Dto.Agent;
using Sanas.Backend.BO.Dto.RequestDto;
using Sanas.Backend.BO.Dto.RequestDto.V4;
using Sanas.Backend.BO.Helpers;
using Sanas.Backend.BO.Services.Interfaces;
using Sanas.Backend.Data.Models;
using Sanas.Backend.Data.Repositories.Interfaces;
using JsonSerializer = System.Text.Json.JsonSerializer;

namespace Sanas.Backend.BO.Handlers;

public class AgentsHandler(
    ILogger<AgentsHandler> logger,
    IAgentsRepository agentsRepo,
    INestService nestService,
    IWorkspaceRepository workspaceRepo) : IAgentsHandler
{
    public async Task<AgentAuthExDto> AgentAutoLoginAsync(int agentId, AgentAutoLoginDto autoLoginDto)
    {
        var agent = await agentsRepo.GetAgentByIdAsync(agentId);

        if (agent is not null)
        {
            if (!agent.IsDisabled)
            {
                agent.LastMachine = autoLoginDto.MachineName;
                agent.LastAppVersion = autoLoginDto.AppVersion;
                agent.LastLoginUtc = DateTime.UtcNow;

                try
                {
                    await agentsRepo.UpdateAgentAsync(agentId, agent);
                }
                catch (Exception e)
                {
                    logger.LogError("An error occurred while updating agent login details. {Error}", e);
                }
            }
        }

        return AgentAuthExDto.FromDataModel(agent);
    }

    public async Task<AgentAutoLoginResponse> AgentAutoLoginV3Async(int agentId, AgentAutoLoginDto autoLoginDto)
    {
        var agent = await agentsRepo.GetAgentByIdAsync(agentId);

        if (agent is not null)
        {
            if (!agent.IsDisabled)
            {
                agent.LastMachine = autoLoginDto.MachineName;
                agent.LastAppVersion = autoLoginDto.AppVersion;
                agent.LastLoginUtc = DateTime.UtcNow;

                try
                {
                    await agentsRepo.UpdateAgentAsync(agentId, agent);
                }
                catch (Exception e)
                {
                    logger.LogError("An error occurred while updating agent login details. {Error}", e);
                }
            }
        }

        return new AgentAutoLoginResponse
        {
            AgentInfo = AgentAuthExDto.FromDataModel(agent),
            ModelType = agent.Workspace?.ModelTypes?.FirstOrDefault(),
            Success = true
        };
    }

    public async Task<AgentAuthExDto> AuthenticateAgentAsync(AgentLoginDto agentLoginDto)
    {
        var agent = await agentsRepo.GetAgentByLoginAsync(agentLoginDto.LoginId, agentLoginDto.WorkspaceKey);

        if (agent is not null)
        {
            var passwordHash = Crypto.GetHash(agentLoginDto.Password, agent.LoginId);

            if (passwordHash.Equals(agent.Password, StringComparison.Ordinal))
            {
                agent.LastMachine = agentLoginDto.MachineName;
                agent.LastAppVersion = agentLoginDto.AppVersion;
                agent.LastLoginUtc = DateTime.UtcNow;

                try
                {
                    if (!agent.IsDisabled)
                    {
                        await agentsRepo.UpdateAgentAsync(agent.AgentId, agent);
                    }
                }
                catch (Exception e)
                {
                    logger.LogError("An error occurred while updating agent login details. {Error}", e);
                }
            }
            else
            {
                agent = null;
            }
        }

        return AgentAuthExDto.FromDataModel(agent);
    }

    public async Task<(AgentAuthExDto Agent, bool LastLoginVerified, string LastMachine)> AuthenticateAgentV2Async(AgentLoginDto agentLoginDto)
    {
        var agent = await agentsRepo.GetAgentByLoginAsync(agentLoginDto.LoginId, agentLoginDto.WorkspaceKey);

        if (agent is null)
        {
            throw new UnauthorizedAccessException();
        }

        var passwordHash = Crypto.GetHash(agentLoginDto.Password, agent.LoginId);

        if (!passwordHash.Equals(agent.Password, StringComparison.Ordinal))
        {
            throw new UnauthorizedAccessException();
        }

        if (!agentLoginDto.SkipLastLoginCheck)
        {
            if (HasLoggedIn(agent, agentLoginDto.MachineName))
            {
                return (AgentAuthExDto.FromDataModel(agent), false, agent.LastMachine);
            }
        }

        try
        {
            if (!agent.IsDisabled)
            {
                agent.LastMachine = agentLoginDto.MachineName;
                agent.LastAppVersion = agentLoginDto.AppVersion;
                agent.LastLoginUtc = DateTime.UtcNow;

                await agentsRepo.UpdateAgentAsync(agent.AgentId, agent);
            }

            return (AgentAuthExDto.FromDataModel(agent), true, agent.LastMachine);
        }
        catch (Exception e)
        {
            logger.LogError("An error occurred while updating agent login details. {Error}", e);
            throw;
        }
    }

    public async Task<AgentAuthResponse> AuthenticateAgentV3Async(AgentLoginV3Dto agentLoginDto)
    {
        var agent = await agentsRepo.GetAgentByLoginAsync(agentLoginDto.LoginId, agentLoginDto.WorkspaceKey);

        if (agent is null)
        {
            throw new UnauthorizedAccessException();
        }

        var modelType = agent.Workspace?.ModelTypes?.FirstOrDefault();

        if (!agentLoginDto.SkipLastLoginCheck)
        {
            if (HasLoggedIn(agent, agentLoginDto.MachineName))
            {
                return new AgentAuthResponse
                {
                    AgentInfo = AgentAuthExDto.FromDataModel(agent),
                    IsLoggedIn = true,
                    IsSessionCheck = !agentLoginDto.SkipLastLoginCheck,
                    LastMachine = agent.LastMachine,
                    ModelType = modelType
                };
            }
        }

        try
        {
            if (!agent.IsDisabled)
            {
                agent.LastMachine = agentLoginDto.MachineName;
                agent.LastAppVersion = agentLoginDto.AppVersion;
                agent.LastLoginUtc = DateTime.UtcNow;

                await agentsRepo.UpdateAgentAsync(agent.AgentId, agent);
            }

            return new AgentAuthResponse
            {
                AgentInfo = AgentAuthExDto.FromDataModel(agent),
                IsLoggedIn = false,
                IsSessionCheck = !agentLoginDto.SkipLastLoginCheck,
                LastMachine = agent.LastMachine,
                ModelType = modelType
            };
        }
        catch (Exception e)
        {
            logger.LogError("An error occurred while updating agent login details. {Error}", e);
            throw;
        }
    }

    public async Task<AgentAuthExDto> ReauthenticateAgentAsync(AgentReauthenticationDto reauthDto)
    {
        var agent = await agentsRepo.GetAgentByLoginAsync(reauthDto.LoginId, reauthDto.WorkspaceKey);

        if (agent is not null && !agent.IsDisabled)
        {
            var reauthenticationTime = DateTime.UtcNow;

            if (await agentsRepo.UpdateAgentLastLoginTime(agent.AgentId, reauthenticationTime))
            {
                return AgentAuthExDto.FromDataModel(agent);
            }
        }

        return AgentAuthExDto.FromDataModel(agent);
    }

    public async Task<AgentAuthResponse> ReauthenticateAgentV3Async(AgentReauthenticationDto reauthDto)
    {
        var response = new AgentAuthResponse();

        var agent = await agentsRepo.GetAgentByLoginAsync(reauthDto.LoginId, reauthDto.WorkspaceKey);

        var modelType = agent?.Workspace?.ModelTypes?.FirstOrDefault();

        if (agent is not null && !agent.IsDisabled)
        {
            var reauthenticationTime = DateTime.UtcNow;

            if (await agentsRepo.UpdateAgentLastLoginTime(agent.AgentId, reauthenticationTime))
            {
                response.AgentInfo = AgentAuthExDto.FromDataModel(agent);
            }
        }

        response.ModelType = modelType;
        response.AgentInfo = AgentAuthExDto.FromDataModel(agent);
        return response;
    }

    public async Task<bool> LogOutAgentAsync(int agentId)
    {
        var logOutTime = DateTime.UtcNow;
        return await agentsRepo.UpdateAgentLastSeenTime(agentId, logOutTime);
    }

    public async Task CreateManyAgentsAsync(int workspaceId, AgentsCreateDto request)
    {
        if (!request.TeamId.HasValue || request.TeamId.Value == 0)
        {
            request.TeamId = (await workspaceRepo.GetDefaultTeamAsync(workspaceId)).TeamId;
        }
        else
        {
            if (!await workspaceRepo.ContainsTeam(workspaceId, request.TeamId.Value))
            {
                throw new ArgumentException($"The team id '{request.TeamId.Value}' does not belong to the given workspace.");
            }
        }

        if (request.Agents.Select(a => a.LoginId?.Trim().ToLower()).ToHashSet().Count < request.Agents.Count())
        {
            throw new ArgumentException("The request contains records with duplicate user IDs.");
        }

        var duplicateIds = new List<string>();

        var existingLoginIds = (await agentsRepo.GetLoginIdsInWorkspaceAsync(workspaceId)).ToHashSet();

        foreach (var agent in request.Agents)
        {
            if (existingLoginIds.Contains(agent.LoginId?.Trim().ToLower()))
            {
                duplicateIds.Add(agent.LoginId);
            }
        }

        if (duplicateIds.Count > 0)
        {
            throw new ArgumentException($"One or more user IDs conflict with existing user IDs in the workspace ({string.Join(",", duplicateIds)})");
        }

        var now = DateTime.UtcNow;

        var agents = request.Agents.Select(agent => new Agent
        {
            LoginId = agent.LoginId.Trim(),
            Name = (agent.Name ?? string.Empty).Trim(),
            Password = Crypto.GetHash(agent.Password, agent.LoginId),
            TeamId = request.TeamId.Value,
            WorkspaceId = workspaceId,
            IsPasswordTemporary = true,
            CreatedUtc = now,
            AgentPreferences = new()
        });

        await agentsRepo.CreateManyAsync(agents);
    }

    public async Task EnableDisableAgentsAsync(int workspaceId, IEnumerable<int> agentIds, bool disable)
    {
        await agentsRepo.EnableDisableManyByIdAsync(workspaceId, agentIds, disable);
        var activity = disable ? "disabled" : "enabled";
        string agentIdsJson = $"[{string.Join(", ", agentIds)}]";
        string jsonString = $"{{\"activity\": \"{activity}\", \"agentIds\": {agentIdsJson}}}";
        await nestService.PostToNestServerAsync("/api/Workspaces/Agents/activities", JsonDocument.Parse(jsonString).RootElement);
    }

    public async Task<AgentDto> GetByAgentIdAsync(int agentId)
    {
        var record = await agentsRepo.GetAgentByIdAsync(agentId);
        return AgentDto.FromDataModel(record);
    }

    public async Task<AgentDto> GetByLoginIdAsync(string workspaceKey, string loginId)
    {
        var record = await agentsRepo.GetAgentByLoginAsync(loginId, workspaceKey);
        return AgentDto.FromDataModel(record);
    }

    public async Task UpdatePasswordFromAppAsync(int agentId, AgentChangePasswordDto changePasswordDto)
    {
        var agentRecord = await agentsRepo.GetAgentByIdAsync(agentId);

        if (agentRecord is not null)
        {
            if (agentRecord.IsDisabled)
            {
                throw new InvalidOperationException("Disabled");
            }

            if (!string.IsNullOrWhiteSpace(changePasswordDto.Password))
            {
                agentRecord.Password = Crypto.GetHash(changePasswordDto.Password, agentRecord.LoginId);
                agentRecord.IsPasswordTemporary = false;
            }

            await agentsRepo.UpdateAgentAsync(agentId, agentRecord);
        }
        else
        {
            throw new ArgumentException("User not found");
        }
    }

    public async Task<bool> UpdateAgentPreferencesAsync(int agentId, AgentPreferencesUpdateDto updateDto)
    {
        object preferenceValue = null;

        if (updateDto.NewValue is JsonElement jsonElement)
        {
            if (jsonElement.ValueKind == JsonValueKind.Number && jsonElement.TryGetInt32(out int value))
            {
                preferenceValue = value;
            }
            else if (jsonElement.ValueKind == JsonValueKind.True)
            {
                preferenceValue = true;
            }
            else if (jsonElement.ValueKind == JsonValueKind.False)
            {
                preferenceValue = false;
            }
        }

        return await agentsRepo.UpdateAgentPreferencesAsync(agentId, updateDto.PreferenceName, preferenceValue);
    }

    public async Task<AgentDto> UpdateAgentAsync(int agentId, AgentUpdateDto agentUpdateDto)
    {
        var agentRecord = await agentsRepo.GetAgentByIdAsync(agentId);

        if (agentRecord is not null)
        {
            if (agentUpdateDto.Name is not null)
            {
                agentRecord.Name = agentUpdateDto.Name;
            }

            /*if (!string.IsNullOrWhiteSpace(agentUpdateDto.LoginId))
            {
                // agentRecord.LoginId = agentUpdateDto.LoginId;
            }*/

            if (!string.IsNullOrWhiteSpace(agentUpdateDto.Password))
            {
                agentRecord.Password = Crypto.GetHash(agentUpdateDto.Password, agentRecord.LoginId);
                agentRecord.IsPasswordTemporary = true;
            }

            if (agentUpdateDto.IsDisabled.HasValue)
            {
                agentRecord.IsDisabled = agentUpdateDto.IsDisabled.Value;
            }

            if (agentUpdateDto.TeamId.HasValue && agentRecord.TeamId != agentUpdateDto.TeamId.Value)
            {
                if (!await workspaceRepo.ContainsTeam(agentRecord.WorkspaceId, agentUpdateDto.TeamId.Value))
                {
                    throw new ArgumentException($"The team id '{agentUpdateDto.TeamId.Value}' does not belong to given workspace.");
                }

                agentRecord.TeamId = agentUpdateDto.TeamId.Value;
            }

            var updatedrecord = await agentsRepo.UpdateAgentAsync(agentId, agentRecord);
            return AgentDto.FromDataModel(updatedrecord);
        }
        else
        {
            throw new ArgumentException("User not found");
        }
    }

    public async Task UpdateAgentLastCallDetailsAsync(int agentId, AgentHeartbeatDto heartbeatDto)
    {
        try
        {
            var agentRecord = await agentsRepo.GetAgentByIdAsync(agentId);

            if (agentRecord is not null)
            {
                agentRecord.LastMachine = heartbeatDto.MachineName;
                agentRecord.LastAppVersion = heartbeatDto.AppVersion;
                agentRecord.LastEngagedUtc = heartbeatDto.IsOnCall ? DateTime.UtcNow : agentRecord.LastEngagedUtc;
                await agentsRepo.UpdateAgentAsync(agentId, agentRecord);
            }
        }
        catch (Exception e)
        {
            logger.LogError("Failed to update last call details. {Error}", e);
        }
    }

    public async Task<AgentCreds> CheckLoginIdAndSuggest(string loginId, int workspaceId)
    {
        if (string.IsNullOrWhiteSpace(loginId))
        {
            throw new ArgumentException("The user ID cannot be empty.");
        }

        if (!await agentsRepo.AgentExistsAsync(workspaceId, loginId?.Trim()))
        {
            return new AgentCreds(loginId, loginId, Random.Shared.Next(100000, 999999).ToString());
        }

        throw new ArgumentException("The user ID already exists.");
    }

    public async Task<IEnumerable<Agent>> GetAgentsByIdAsync(int workspaceId, IEnumerable<int> agentIds)
    {
        return await agentsRepo.GetAgentsByIdAsync(workspaceId, agentIds);
    }

    public async Task MoveAgentsToTeamAsync(int workspaceId, IEnumerable<int> agentIds, int destinationTeamId)
    {
        await agentsRepo.MoveAgentsToTeamAsync(workspaceId, agentIds, destinationTeamId);
    }

    public async Task<(bool IsSuccess, List<string[]> Result, AgentBulkUploadRequestSummaryDto RequestSummary)> HandleCreateBulkUploadAsync(int workspaceId, IList<AgentCsvDto> uploadData, bool isVerified, int? teamId)
    {
        var workspace = await workspaceRepo.GetByIdAsync(workspaceId);
        var workspaceTeams = await workspaceRepo.GetAllTeamNamesWithIdsAsync(workspaceId);
        var existingLoginIds = (await agentsRepo.GetLoginIdsInWorkspaceAsync(workspaceId)).ToHashSet();
        var uniqueLoginIds = new HashSet<string>();
        var errorSummary = new Dictionary<string, int>();

        for (int i = 0; i < uploadData.Count; i++)
        {
            var record = uploadData[i];

            // record.Name = (record.FirstName + " " + record.LastName).Trim();
            record.Password = Random.Shared.Next(100000, 999999).ToString();
            record.Error = string.Empty;

            if (uniqueLoginIds.Contains(record.LoginId.ToLower().Trim()))
            {
                record.Error = "Duplicate User ID found in the file.";
                string errorSummaryMessage = "Duplicate User ID";
                errorSummary[errorSummaryMessage] = errorSummary.ContainsKey(errorSummaryMessage) ? errorSummary[errorSummaryMessage] + 1 : 1;
                continue;
            }

            if (!workspaceTeams.ContainsValue(record.TeamId))
            {
                record.Error = "Incorrect Team Name. This team does not exist.";
                string errorSummaryMessage = "Team name does not exist";
                errorSummary[errorSummaryMessage] = errorSummary.ContainsKey(errorSummaryMessage) ? errorSummary[errorSummaryMessage] + 1 : 1;
                continue;
            }

            if (existingLoginIds.Contains(record.LoginId.ToLower().Trim()))
            {
                record.Error = "User ID entered is not unique. A user with the same user ID already exists.";
                string errorSummaryMessage = "User ID already exists";
                errorSummary[errorSummaryMessage] = errorSummary.ContainsKey(errorSummaryMessage) ? errorSummary[errorSummaryMessage] + 1 : 1;
                continue;
            }

            if (!record.IsValid(out string error))
            {
                record.Error = error;
                string errorSummaryMessage = error.Split('.')[0];
                errorSummary[errorSummaryMessage] = errorSummary.ContainsKey(errorSummaryMessage) ? errorSummary[errorSummaryMessage] + 1 : 1;
                continue;
            }

            uniqueLoginIds.Add(record.LoginId.ToLower());
        }

        var csv = new List<string[]>();
        var requestSummary = new AgentBulkUploadRequestSummaryDto();
        if (uploadData.Any(record => !string.IsNullOrWhiteSpace(record.Error)))
        {
            csv.Add(["User ID", "Name", "Team", "Error Message"]);

            foreach (var item in uploadData)
            {
                var loginId = item.LoginId.Contains(',', StringComparison.Ordinal) ? $"\"{item.LoginId}\"" : item.LoginId;
                var agentName = item.Name.Contains(',', StringComparison.Ordinal) ? $"\"{item.Name}\"" : item.Name;
                var teamName = item.TeamName.Contains(',', StringComparison.Ordinal) ? $"\"{item.TeamName}\"" : item.TeamName;
                var error = item.Error.Contains(',', StringComparison.Ordinal) ? $"\"{item.Error}\"" : item.Error;
                csv.Add([loginId, agentName, teamName, error]);
            }

            requestSummary.Status = AgentBulkUploadStatus.Error;
            requestSummary.Message = $"We encountered {uploadData.Count(record => !string.IsNullOrWhiteSpace(record.Error))} {(uploadData.Count() == 1 ? "error" : "errors")} in your file and couldn't add any users at this time.";
            requestSummary.Summary = errorSummary.Select(item => $"Error: '{item.Key}' : {item.Value} {(item.Value == 1 ? "record" : "records")} found.").ToArray();

            return (false, csv, requestSummary);
        }

        var utcNow = DateTime.UtcNow;

        var agents = uploadData.Select(record => new Agent
        {
            LoginId = record.LoginId.Trim(),
            Name = record.Name.Trim(),
            Password = Crypto.GetHash(record.Password, record.LoginId),
            TeamId = record.TeamId,
            WorkspaceId = workspaceId,
            IsDisabled = false,
            CreatedUtc = utcNow,
            IsPasswordTemporary = true
        });

        if (!isVerified)
        {
            requestSummary.Status = AgentBulkUploadStatus.Validated;
            requestSummary.Message = $"You're about to add {agents.Count()} {(agents.Count() == 1 ? "user" : "users")}.";
            requestSummary.Summary = uploadData.GroupBy(data => data.TeamName).Select(group => $"{group.Key}: {group.Count()} {(group.Count() == 1 ? "user" : "users")}").ToArray();
        }
        else
        {
            await agentsRepo.CreateManyAsync(agents);
            csv.Add(["Workspace ID", "Team", "Name", "User ID", "Password"]);
            foreach (var item in uploadData)
            {
                var agentName = item.Name.Contains(',', StringComparison.Ordinal) ? $"\"{item.Name}\"" : item.Name;
                var teamName = item.TeamName.Contains(',', StringComparison.Ordinal) ? $"\"{item.TeamName}\"" : item.TeamName;

                csv.Add([workspace.WorkspaceKey, teamName, agentName, item.LoginId, item.Password]);
            }

            requestSummary.Status = AgentBulkUploadStatus.Success;
            requestSummary.Message = $"{agents.Count()} {(agents.Count() == 1 ? "user" : "users are")} added successfully.";
            requestSummary.Summary = new string[] { };
        }

        return (true, csv, requestSummary);
    }

    public async Task<(bool IsSuccess, List<string[]> Result, AgentBulkUploadRequestSummaryDto RequestSummary)> HandleDisableBulkUploadAsync(int workspaceId, IList<AgentCsvDto> uploadData, bool isVerified)
    {
        var existingLoginIds = (await agentsRepo.GetLoginIdsInWorkspaceAsync(workspaceId)).ToHashSet();
        var errorSummary = new Dictionary<string, int>();
        for (int i = 0; i < uploadData.Count; i++)
        {
            var record = uploadData[i];
            if (!existingLoginIds.Contains(record.LoginId.ToLower().Trim()))
            {
                record.Error = "Incorrect User ID. The user does not exist.";
                string errorSummaryMessage = "Incorrect User ID";
                errorSummary[errorSummaryMessage] = errorSummary.ContainsKey(errorSummaryMessage) ? errorSummary[errorSummaryMessage] + 1 : 1;
                continue;
            }
        }

        var requestSummary = new AgentBulkUploadRequestSummaryDto();

        if (uploadData.Any(record => !string.IsNullOrWhiteSpace(record.Error)))
        {
            var csv = new List<string[]>();

            csv.Add(["User ID", "Error Message"]);

            foreach (var item in uploadData)
            {
                csv.Add([item.LoginId, item.Error]);
            }

            requestSummary.Status = AgentBulkUploadStatus.Error;
            requestSummary.Message = $"We encountered {uploadData.Count(record => !string.IsNullOrWhiteSpace(record.Error))} errors in your file and couldn't deactivate licenses for any users at this time.";
            requestSummary.Summary = errorSummary.Select(item => $"Error: '{item.Key}' : {item.Value} {(item.Value == 1 ? "record" : "records")} found.").ToArray();
            return (false, csv, requestSummary);
        }

        if (!isVerified)
        {
            requestSummary.Status = AgentBulkUploadStatus.Validated;
            requestSummary.Message = $"You’re about to disable {uploadData.Count()} {(uploadData.Count() == 1 ? "user" : "users")}.On disabling, the users will not be be able to login to the app.Do you want to continue?";
            requestSummary.Summary = [];
        }
        else
        {
            await agentsRepo.EnableDisableManyByIdAsync(workspaceId, uploadData.Select(a => a.LoginId), true);
            requestSummary.Status = AgentBulkUploadStatus.Success;
            requestSummary.Message = $"{uploadData.Count()} {(uploadData.Count() == 1 ? "user" : "users are")} disabled successfully.";
            requestSummary.Summary = [];
        }

        return (true, [], requestSummary);
    }

    public async Task<(bool IsSuccess, List<string[]> Result, AgentBulkUploadRequestSummaryDto RequestSummary)> BulkUploadAsync(int workspaceId, IList<AgentCsvDto> uploadData, string action, bool isVerified, int? teamId)
    {
        switch (action)
        {
            case "create":
                return await HandleCreateBulkUploadAsync(workspaceId, uploadData, isVerified, teamId);
            case "disable":
                return await HandleDisableBulkUploadAsync(workspaceId, uploadData, isVerified);
            default:
                return (false, null, null);
        }
    }

    public async Task UpdateICPAsync(int agentId, OverallCompatibilityReport icpReport)
    {
        if (icpReport is not null)
        {
            var agent = await agentsRepo.GetAgentByIdAsync(agentId);

            if (agent is not null)
            {
                var icpStatus = icpReport.OverallCompatibilityState;
                agent.IcpReport = JsonSerializer.Serialize(icpReport);
                agent.IcpStatus = icpStatus is CompatibilityState.None or CompatibilityState.Compatible ? 0 : ((int)icpStatus) - 1;
            }

            await agentsRepo.UpdateAgentAsync(agentId, agent);
        }
    }

    private static bool HasLoggedIn(Agent agent, string machineName)
    {
        // If the incoming machine name is same then he is allowed to login.
        if (!string.Equals(agent.LastMachine, machineName, StringComparison.OrdinalIgnoreCase))
        {
            if (agent.LastLoginUtc.HasValue)
            {
                if (agent.LastSeenUtc.HasValue)
                {
                    if (agent.LastSeenUtc > agent.LastLoginUtc)
                    {
                        // has logged out.
                        return false;
                    }
                }

                // still logged in
                return true;
            }
        }

        // Probably never logged in
        return false;
    }

    public async Task<OverallCompatibilityReport> GetIcpIncompatibilityReportAsync(int agentId)
    {
        var agent = await agentsRepo.GetAgentByIdAsync(agentId);

        if (agent is null)
        {
            throw new ArgumentException("User not found");
        }

        return agent.IcpReport is null ? null : JsonSerializer.Deserialize<OverallCompatibilityReport>(agent.IcpReport);
    }
}
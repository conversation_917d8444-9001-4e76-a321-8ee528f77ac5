﻿using Sanas.Backend.BO.Dto;
using Sanas.Backend.BO.Services;
using Sanas.Backend.Data.Repositories;

namespace Sanas.Backend.BO.Handlers;

public class AppEventHandler(
    INotificationsRepository notificationsRepo,
    IWorkspaceHandler workspaceHandler,
    ITeamsHandler teamsHandler,
    NotifierService notifierService) : IAppEventHandler
{
    public async Task OnEventReceivedFromApp(AgentDto agent, NotificationType notificationType, string additionalInfo)
    {
        var title = "Sanas Notification";
        var htmlContent = GetDescriptionForEmail(agent, notificationType, additionalInfo);
        var plainContent = GetDescriptionForPush(agent, notificationType, additionalInfo);

        await notificationsRepo.SaveNotificationAsync(new Data.Models.Notification
        {
            AgentId = agent.AgentId,
            WorkspaceId = agent.WorkspaceId,
            TeamId = agent.TeamId,
            CreatedUtc = DateTime.UtcNow,
            Content = htmlContent,
            NotificationType = (short)notificationType
        });

        var admins = await workspaceHandler.GetWorkspaceAdminsAsync(agent.WorkspaceId);
        var supervisors = await teamsHandler.GetTeamSupervisorsAsync(agent.WorkspaceId, agent.TeamId);
        var allUsers = admins.UnionBy(supervisors, u => u.PortalUserId);

        if (allUsers.Any())
        {
            // notifierService.NotifyByEmailAsync(allUsers, title, htmlContent);
            notifierService.SendPushNotificationAsync(allUsers, title, plainContent);
        }
    }

    private static string GetDescriptionForEmail(AgentDto agent, NotificationType notificationType, string additionalInfo)
    {
        var message = notificationType switch
        {
            NotificationType.AgentLogout       => $"<strong>{agent.LoginId}</strong> has logged out of Sanas.",
            NotificationType.SynthesisDisabled => $"<strong>{agent.LoginId}</strong> has disabled Sanas. Reason: <strong>{additionalInfo}</strong>.",
            NotificationType.FeedbackSubmitted => $"<strong>{agent.LoginId}</strong> has submitted a new feedback in app. Feedback: <strong>{additionalInfo}</strong>",
            _ => "You have a new notification",
        };

        return message;
    }

    private static string GetDescriptionForPush(AgentDto agent, NotificationType notificationType, string additionalInfo)
    {
        var message = notificationType switch
        {
            NotificationType.AgentLogout => $"{agent.LoginId} has logged out of Sanas.",
            NotificationType.SynthesisDisabled => $"{agent.LoginId} has disabled Sanas. Reason: {additionalInfo}.",
            NotificationType.FeedbackSubmitted => $"{agent.LoginId} has submitted a new feedback in app. Feedback: {additionalInfo}",
            _ => "You have a new notification",
        };

        return message;
    }
}

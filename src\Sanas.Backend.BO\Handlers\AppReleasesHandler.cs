﻿using System.Collections.ObjectModel;
using Sanas.Backend.BO.Dto;
using Sanas.Backend.BO.Dto.AppReleases;
using Sanas.Backend.BO.Dto.RequestDto;
using Sanas.Backend.BO.Dto.ResponseDto;
using Sanas.Backend.BO.Extensions;
using Sanas.Backend.BO.Services;
using Sanas.Backend.Data;
using Sanas.Backend.Data.Repositories;
using Sanas.Backend.Data.Repositories.Interfaces;

namespace Sanas.Backend.BO.Handlers;

public sealed class AppReleasesHandler(
    IAppReleasesRepository appReleasesRepository,
    IWorkspaceRepository workspaceRepository,
    IGenericRepository genericRepository,
    IAppDownloadService appDownloadService) : IAppReleasesHandler
{
    public async Task CreateAppReleaseAsync(AppReleaseCreateDto appRelease)
    {
        await appReleasesRepository.CreateAppReleaseAsync(AppReleaseCreateDto.ToDataModel(appRelease));
    }

    public async Task<PagedDto<AppReleaseDto>> GetAppReleasesAsync(int workspaceId, RequestOptions requestOptions, AppReleasesQueryFilters filters)
    {
        var modelTypesMap = await genericRepository.GetModelTypesAsync();
        var workspace = await workspaceRepository.GetByIdAsync(workspaceId);

        var queryOptions = new AppReleasesQueryOptions(requestOptions)
        {
            StartDate = filters.StartDate,
            EndDate = filters.EndDate,
            Versions = filters.Versions,
            CountryCodes = workspace.CountryCode
        };

        if (!filters.ModelTypeId.HasValue)
        {
            var modelTypes = workspace.ModelTypes.Select(m => modelTypesMap.TryGetValue(m, out int value) ? value : 0);
            queryOptions.ModelTypes.AddRange(modelTypes);
        }
        else
        {
            var workspaceModelTypeIds = new List<int>();

            foreach (var modelType in workspace.ModelTypes)
            {
                if (modelTypesMap.TryGetValue(modelType?.ToUpperInvariant(), out int modelTypeId))
                {
                    workspaceModelTypeIds.Add(modelTypeId);
                }
            }

            var modelTypeFilter = workspaceModelTypeIds.Contains(filters.ModelTypeId.Value) ? filters.ModelTypeId.Value : -1;
            queryOptions.ModelTypes.AddRange([modelTypeFilter]);
        }

        var (records, totalCount) = await appReleasesRepository.GetAppReleasesAsync(queryOptions);
        var items = records.Select(AppReleaseDto.FromModel).ToList();

        Parallel.ForEach(items, release =>
        {
            release.AppDownloads.ForEach(appDownload =>
            {
                appDownload.ExeFileSize = appDownloadService.GetInstallerSize(appDownload.ExeDownloadUrl);
                appDownload.MsiFileSize = appDownloadService.GetInstallerSize(appDownload.MsiDownloadUrl);
            });
        });

        var result = new PagedDto<AppReleaseDto>()
        {
            Items = new Collection<AppReleaseDto>(items),
            RecordsCount = items.Count,
            TotalRecordsCount = totalCount
        };

        return result;
    }

    public async Task UpdateReleaseNotesAsync(int releaseId, string releaseNotes)
    {
        await appReleasesRepository.UpdateReleaseNotesAsync(releaseId, releaseNotes);
    }

    public async Task<AppReleaseDto> GetByReleaseIdAsync(int releaseId)
    {
        var release = await appReleasesRepository.GetByReleaseIdAsync(releaseId);

        return AppReleaseDto.FromModel(release);
    }
}

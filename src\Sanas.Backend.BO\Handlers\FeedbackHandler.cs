﻿using System.Collections.ObjectModel;
using System.IO.Compression;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using Sanas.Backend.BO.Dto;
using Sanas.Backend.BO.Dto.Feedback.SystemSummary;
using Sanas.Backend.BO.Dto.RequestDto;
using Sanas.Backend.BO.Dto.ResponseDto;
using Sanas.Backend.BO.Handlers.Interfaces;
using Sanas.Backend.BO.Services.Interfaces;
using Sanas.Backend.Data.Models;
using Sanas.Backend.Data.Repositories.Interfaces;

namespace Sanas.Backend.BO.Handlers;

public class FeedbackHandler(
    ILogger<FeedbackHandler> logger,
    IBlobService blobService,
    IFeedbackRepository feedbackRepo,
    IFreshdeskService freshdeskService,
    IAgentsRepository agentsRepo) : IFeedbackHandler
{
    public async Task<bool> CreateFeedbackAsync(FeedbackDto createDto, string loginId)
    {
        _ = await agentsRepo.GetAgentByIdAsync(createDto.AgentId)
            ?? throw new ArgumentException("Agent not found");

        var now = DateTime.UtcNow;

        var feedback = new Feedback
        {
            WorkspaceId = createDto.WorkspaceId,
            AgentId = createDto.AgentId,
            AppVersion = createDto.AppVersion,
            Description = createDto.Description,
            SentimentType = createDto.SentimentType,
            AttachmentNumber = createDto.AttachmentName,
            TrackingNumber = createDto.TrackingNumber,
            SanasUniqueId = createDto.SanasUniqueId,
            LoginId = loginId,
            CreatedUtc = now,
            UpdatedUtc = now,
        };

        var newRecord = await feedbackRepo.CreateFeedbackAsync(feedback) 
            ?? throw new ArgumentException("Could not create feedback");

        var recordCreated = newRecord != null;

        if (recordCreated && createDto.Attachment != null)
        {
            _ = Task.Run(async () =>
            {
                await UploadBlobAsync(newRecord.AttachmentNumber, createDto.Attachment, newRecord.SanasUniqueId);
                await CreateAndSubmitFreshdeskTicketAsync(createDto.Attachment, newRecord);
            });
        }

        return recordCreated;
    }

    public async Task<PagedDto<FeedbackDto>> GetFeedbacksByWorkspaceIdAsync(int workspaceId, RequestOptions requestOptions)
    {
        var records = await feedbackRepo.GetFeedbacksAsync(workspaceId, requestOptions.PageNumber, requestOptions.PageSize);
        var totalCount = await feedbackRepo.GetFeedbacksCountAsync(workspaceId);
        return new PagedDto<FeedbackDto>
        {
            Items = new Collection<FeedbackDto>(records.Select(FeedbackDto.FromDataModel).ToList()),
            RecordsCount = records.Count,
            TotalRecordsCount = totalCount,
        };
    }

    private async Task<bool> UploadBlobAsync(string key, byte[] attachment, string uniqueId)
    {
        try
        {
            await using var stream = new MemoryStream(attachment);
            key = "feedback_attachment/" + key; // feedback_attachment/ is folder in s3 bucket
            var blobUploadStatus = await blobService.UploadToBlobAsync(key, stream);
            logger.LogInformation($"FeedbackHandler.UploadBlobAsync() Upload to S3 status: {blobUploadStatus}, Id: {key} for {uniqueId}.");

            return blobUploadStatus;
        }
        catch (Exception e)
        {
            logger.LogError($"FeedbackHandler.UploadBlobAsync() {e.Message}");
        }

        return false;
    }

    private async Task CreateAndSubmitFreshdeskTicketAsync(byte[] attachment, Feedback feedbackRecord)
    {
        try
        {
            if (!freshdeskService.IsEnabled())
            {
                return;
            }

            var systemSummary = ParseSystemSummary(attachment);
            var freshDeskTicket = freshdeskService.CreateTicket(feedbackRecord, systemSummary);

            if (freshDeskTicket == null)
            {
                logger.LogError($"FeedbackHandler.CreateAndSubmitFreshdeskTicketAsync() Failed to create Freshdesk ticket.");
                return;
            }

            freshDeskTicket.Attachment = attachment;
            await freshdeskService.SubmitTicketAsync(freshDeskTicket);
        }
        catch (Exception e)
        {
            logger.LogError($"FeedbackHandler.CreateAndSubmitFreshdeskTicketAsync() {e.Message}");
        }
    }

    /// <summary>
    /// Helper method to process SystemSummary.json
    /// </summary>
    /// <param name="attachment">byte[] of the attachment</param>
    /// <returns>Dictionary of summary values, null if failure</returns>
    private SystemSummary ParseSystemSummary(byte[] attachment)
    {
        using var stream = new MemoryStream(attachment);
        using var zipArchive = new ZipArchive(stream);
        SystemSummary systemSummary = null;

        foreach (var zipEntry in zipArchive.Entries)
        {
            string fileName = zipEntry.Name;
            string fileType = Path.GetExtension(fileName);

            if (fileType.Equals(".json", StringComparison.OrdinalIgnoreCase) &&
                fileName.StartsWith("SystemSummary", StringComparison.OrdinalIgnoreCase))
            {
                using Stream fileStream = zipEntry.Open();
                using StreamReader reader = new(fileStream);
                string systemSummaryString = reader.ReadToEnd();
                systemSummary = JsonConvert.DeserializeObject<SystemSummary>(systemSummaryString);
            }
        }

        if (systemSummary is null)
        {
            logger.LogError("FeedbackHandler.ProcessSystemSummary() SystemSummary.json could not be parsed.");
        }

        return systemSummary;
    }
}
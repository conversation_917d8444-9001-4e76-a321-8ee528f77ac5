﻿using System.Text.Json;
using Sanas.Backend.BO.Dto;
using Sanas.Backend.Data.Repositories;

namespace Sanas.Backend.BO.Handlers;

public class GenericHandler : IGenericHandler
{
    private readonly IGenericRepository _genericRepository;

    public GenericHandler(IGenericRepository genericRepository)
    {
        _genericRepository = genericRepository;
    }

    public async Task<string> GetICPAsync()
    {
        return await _genericRepository.GetICPAsync();
    }

    public async Task UpdateICPAsync(CompatibilityChecklist icpChecklist)
    {
        var json = JsonSerializer.SerializeToElement(icpChecklist);
        await _genericRepository.UpdateICPAsync(json);
    }
}

﻿using System.Text;
using Sanas.Backend.BO.Dto;
using Sanas.Backend.BO.Dto.Agent;
using Sanas.Backend.BO.Dto.RequestDto;
using Sanas.Backend.Data.Models;

namespace Sanas.Backend.BO.Handlers;

public interface IAgentsHandler
{
    Task<AgentAuthExDto> AgentAutoLoginAsync(int agentId, AgentAutoLoginDto autoLoginDto);
    Task<AgentAutoLoginResponse> AgentAutoLoginV3Async(int agentId, AgentAutoLoginDto autoLoginDto);
    
    Task<AgentAuthExDto> AuthenticateAgentAsync(AgentLoginDto agentLoginDto);
    Task<(AgentAuthExDto Agent, bool LastLoginVerified, string LastMachine)> AuthenticateAgentV2Async(AgentLoginDto agentLoginDto);
    Task<AgentAuthResponse> AuthenticateAgentV3Async(AgentLoginV3Dto agentLoginDto);
    Task<AgentAuthResponse> ReauthenticateAgentV3Async(AgentReauthenticationDto reauthDto);

    Task<AgentAuthExDto> ReauthenticateAgentAsync(AgentReauthenticationDto reauthDto);
    Task<bool> LogOutAgentAsync(int agentId);
    Task EnableDisableAgentsAsync(int workspaceId, IEnumerable<int> agentIds, bool disable);
    Task<AgentDto> GetByAgentIdAsync(int agentId);
    Task<bool> UpdateAgentPreferencesAsync(int agentId, AgentPreferencesUpdateDto updateDto);
    Task<AgentDto> UpdateAgentAsync(int agentId, AgentUpdateDto agentUpdateDto);
    Task UpdatePasswordFromAppAsync(int agentId, AgentChangePasswordDto changePasswordDto);
    Task<(bool IsSuccess, List<string[]> Result, AgentBulkUploadRequestSummaryDto RequestSummary)> BulkUploadAsync(int workspaceId, IList<AgentCsvDto> uploadData, string action, bool isVerified, int? teamId);
    Task<AgentCreds> CheckLoginIdAndSuggest(string loginId, int workspaceId);
    Task CreateManyAgentsAsync(int workspaceId, AgentsCreateDto request);
    Task<IEnumerable<Agent>> GetAgentsByIdAsync(int workspaceId, IEnumerable<int> agentIds);
    Task MoveAgentsToTeamAsync(int workspaceId, IEnumerable<int> agentIds, int destinationTeamId);
    Task UpdateAgentLastCallDetailsAsync(int agentId, AgentHeartbeatDto heartbeatDto);
    Task UpdateICPAsync(int agentId, OverallCompatibilityReport icpReport);
    Task<AgentDto> GetByLoginIdAsync(string workspaceKey, string loginId);
    Task<OverallCompatibilityReport> GetIcpIncompatibilityReportAsync(int agentId);
}
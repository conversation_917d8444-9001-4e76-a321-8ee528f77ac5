﻿using Sanas.Backend.BO.Dto;
using Sanas.Backend.BO.Dto.AppReleases;
using Sanas.Backend.BO.Dto.RequestDto;
using Sanas.Backend.BO.Dto.ResponseDto;

namespace Sanas.Backend.BO.Handlers;

public interface IAppReleasesHandler
{
    Task CreateAppReleaseAsync(AppReleaseCreateDto appRelease);
    Task<PagedDto<AppReleaseDto>> GetAppReleasesAsync(int workspaceId, RequestOptions requestOptions, AppReleasesQueryFilters filters);
    Task<AppReleaseDto> GetByReleaseIdAsync(int releaseId);
    Task UpdateReleaseNotesAsync(int releaseId, string releaseNotes);
}

﻿using Sanas.Backend.BO.Dto;
using Sanas.Backend.BO.Dto.RequestDto;

namespace Sanas.Backend.BO.Handlers;

public interface INotificationsHandler
{
    Task<PagedNotificationsDto> GetByUserId(int workspaceId, int userId, RequestOptions requestOptions, NotificationQueryFilters filters);
    Task MarkAllAsReadAsync(int workspaceId, int userId, IEnumerable<string> types);
    Task ToggleMarkAsRead(int notificationId, int userId, bool markAsUnread);
}
﻿using Sanas.Backend.BO.Dto;
using Sanas.Backend.BO.Dto.PortalUsers;

namespace Sanas.Backend.BO.Handlers;

public interface IRBACHandler
{
    Task<RolesResponseDto> GetRolesAsync();
    Task<IEnumerable<string>> GetUserPermissionsAsync(int workspaceId, int userId, bool isSanasUser);
    Task<IEnumerable<UserScope>> GetUserWorkspaceScopesAsync(int userId, bool isSanasUser);
    Task UpdateUserRoleAtWorkspaceAsync(int workspaceId, UserRoleUpdateDto request);
    Task UpdateSanasUserRoleAsync(UserRoleUpdateDto request);
}
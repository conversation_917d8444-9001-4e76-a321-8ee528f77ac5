﻿using Sanas.Backend.BO.Dto;
using Sanas.Backend.BO.Dto.RequestDto;
using Sanas.Backend.BO.Dto.ResponseDto;

namespace Sanas.Backend.BO.Handlers
{
    public interface ISanasUserHandler
    {
        Task<PortalUserDto> CreateSanasUserAsync(PortalUserCreateDto portalUser);
        Task EnableDisableSanasUserAsync(int portalUserId, bool disable);
        Task<PagedDto<PortalUserDto>> GetAllWithPagingAsync(RequestOptions queryOptions);
        Task<PortalUserGetDto> GetByUserIdAsync(int portalUserId);
    }
}
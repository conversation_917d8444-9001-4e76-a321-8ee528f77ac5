﻿using Sanas.Backend.BO.Dto;
using Sanas.Backend.BO.Dto.Reports;
using Sanas.Backend.BO.Dto.RequestDto;
using Sanas.Backend.BO.Dto.ResponseDto;
using Sanas.Backend.BO.Dto.Team;

namespace Sanas.Backend.BO.Handlers
{
    public interface ITeamsHandler
    {
        Task<TeamDto> CreateTeamAsync(int workspaceId, TeamCreateDto team);
        Task UpdateTeamAsync(int workspaceId, int teamId, TeamUpdateDto team);
        Task<TeamDto> GetByTeamIdAsync(int workspaceId, int teamId);
        Task<TeamDto> GetByTeamNameAsync(int workspaceId, string teamName);
        Task<PagedDto<AgentDto>> GetAgentsInTeamWithPagingAsync(int workspaceId, int teamId, RequestOptions queryOptions, AgentQueryFilters filterOptions);
        Task EnableOrDisableTeamAsync(int workspaceId, int teamId, bool disable);
        Task SetAsDefaultTeamAsync(int workspaceId, int teamId);
        Task<IEnumerable<PortalUserDto>> GetTeamSupervisorsAsync(int workspaceId, int teamId);
        Task<TeamStats> GetTeamStatsAsync(int teamId, int duration);
        Task<byte[]> DownloadAgentsReportAsync(int workspaceId, int teamId, TeamsReportDownloadOptions queryOptions, AgentQueryFilters filterOptions);
    }
}
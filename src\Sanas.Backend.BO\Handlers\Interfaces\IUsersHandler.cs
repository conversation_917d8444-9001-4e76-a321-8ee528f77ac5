﻿using Sanas.Backend.BO.Dto;
using Sanas.Backend.BO.Dto.RequestDto;

namespace Sanas.Backend.BO.Handlers;

public interface IUsersHandler
{
    Task<bool> EmailExists(string email, int workspaceId);

    Task ResendInviteAsync(int workspaceId, int portalUserId);
    Task CancelUserInviteAsync(int workspaceId, int portalUserId);
    Task EnableDisablePortalUserAsync(int workspaceId, int portalUserId, bool disable);
    Task UpdateUserProfileAsync(string idpUserId, string firstName, string lastName);

    Task<PortalUserGetDto> GetByUserIdAsync(int workspaceId, int portalUserId);
    Task<PortalUserGetDto> GetByUserIdAsync(string idpUserId);
    Task<IEnumerable<int>> GetWorkspaceIdsForUserAsync(string idpUserId);
    Task<IEnumerable<int>> GetWorkspaceIdsForUserAsync(int portalUserId, bool includeDisabled);
    Task<IEnumerable<int>> GetAccessibleTeamsAsync(int workspaceId, string idpUserId);
    
    Task UpdateUserPasswordAsync(string idpUserId, string password);
    Task<bool> IsWorkspaceAdmin(int workspaceId, int userId);
    Task<IEnumerable<int>> GetAccessibleTeamsAsync(int workspaceId, int portalUserId);
    Task UpdateNotificationTokenAsync(string userId, string token);
    Task<PortalUserGetDto> GetByUserEmailAsync(string email);
    Task<PortalUserDto> CreateNCUserAsync(int workspaceId, string idpUserId, NCUser newUser);
}
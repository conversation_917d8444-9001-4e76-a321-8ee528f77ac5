﻿using System.Text.Json;
using Sanas.Backend.BO.Dto;
using Sanas.Backend.BO.Dto.Dashboards;
using Sanas.Backend.BO.Dto.Enums;
using Sanas.Backend.BO.Dto.RequestDto;
using Sanas.Backend.BO.Dto.ResponseDto;

namespace Sanas.Backend.BO.Handlers;

public interface IWorkspaceHandler
{
    Task<WorkspaceDto> CreateWorkspaceAsync(WorkspaceCreateDto workspace);
    Task UpdateWorkspaceAsync(int workspaceId, WorkspaceUpdateDto workspace);
    Task UpdateAutoAppConfigurationAsync(int workspaceId, JsonElement appConfig);

    Task<TeamDto> GetDefaultTeamAsync(int workspaceId);
    Task<WorkspaceDto> GetByIdAsync(int workspaceId);
    Task<IEnumerable<WorkspaceDto>> GetByUserIdAsync(int portalUserId);
    Task<PagedDto<WorkspaceDto>> GetAllWithPagingAsync(RequestOptions queryOptions, bool skipAgents);
    Task<PagedDto<WorkspaceDto>> GetByUserIdWithPagingAsync(int portalUserId, RequestOptions queryOptions);
    Task<PagedDto<PortalUserDto>> GetUsersInWorkspaceWithPagingAsync(int workspaceId, RequestOptions queryOptions, string role);
    Task<PagedDto<TeamDto>> GetTeamsInWorkspaceWithPagingAsync(int workspaceId, RequestOptions queryOptions);
    Task<PagedDto<AgentDto>> GetAgentsInWorkspaceWithPagingAsync(int workspaceId, RequestOptions queryOptions);
    Task<Dictionary<string, int>> GetAllTeamNamesWithIdsAsync(int workspaceId);

    Task DeleteWorkspaceAsync(int workspaceId);
    Task ReactivateWorkspace(int workspaceId);

    Task InviteUsersToWorkspaceAsync(int workspaceId, IEnumerable<UserInvite> userInvites);

    Task<PagedDto<string>> GetBPOsAsync(RequestOptions requestOptions);
    Task<PagedDto<string>> GetSitesAsync(RequestOptions requestOptions);
    Task<IEnumerable<PortalUserDto>> GetWorkspaceAdminsAsync(int workspaceId);
    Task<AgentActivityReport> GetAgentActityReportAsync(int workspaceId, DurationTypes durationType);
    Task<string> GenerateEnterpriseNameFromEmailAsync(string email);
    Task<NCWorkspace> GetNCWorkspaceAsync(int workspaceId, int userId, bool forLogin);
    Task<InstallerCheckDto> ValidateInstallerIdAsync(Guid installerId);
}

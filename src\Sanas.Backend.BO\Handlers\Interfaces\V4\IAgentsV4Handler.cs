﻿using System.Text;
using Sanas.Backend.BO.Dto;
using Sanas.Backend.BO.Dto.Agent;
using Sanas.Backend.BO.Dto.Agent.V4;
using Sanas.Backend.BO.Dto.RequestDto;
using Sanas.Backend.BO.Dto.RequestDto.V4;
using Sanas.Backend.Data.Models;

namespace Sanas.Backend.BO.Handlers.Interfaces.V4;

public interface IAgentsV4Handler
{
    Task<AgentAutoLoginV4Response> AgentAutoLoginAsync(int agentId, AgentAutoLoginV4Dto autoLoginDto);
    Task<AgentActivationResponse> AuthenticateAgentAsync(AgentLoginV4Dto agentLoginDto);
    Task<AgentAuthV4Response> ReauthenticateAgentAsync(AgentReauthenticationV4Dto reauthDto);
    Task<bool> LogOutAgentAsync(int agentId);
    Task EnableDisableAgentsAsync(int workspaceId, IEnumerable<int> agentIds, bool disable);
    Task<AgentDto> GetByAgentIdAsync(int agentId);
    Task<bool> UpdateAgentPreferencesAsync(int agentId, AgentPreferencesUpdateDto updateDto);
    Task<AgentDto> UpdateAgentAsync(int agentId, AgentUpdateDto agentUpdateDto);
    Task UpdatePasswordFromAppAsync(int agentId, AgentChangePasswordDto changePasswordDto);
    Task<(bool IsSuccess, List<string[]> Result, AgentBulkUploadRequestSummaryDto RequestSummary)> BulkUploadAsync(int workspaceId, IList<AgentCsvDto> uploadData, string action, bool isVerified, int? teamId);
    Task<AgentCreds> CheckLoginIdAndSuggest(string loginId, int workspaceId);
    Task CreateManyAgentsAsync(int workspaceId, AgentsCreateDto request);
    Task<Agent> CreateAgentInWorkspace(Workspace workspace, AgentLoginV4Dto agentInfo);
    Task<IEnumerable<Agent>> GetAgentsByIdAsync(int workspaceId, IEnumerable<int> agentIds);
    Task MoveAgentsToTeamAsync(int workspaceId, IEnumerable<int> agentIds, int destinationTeamId);
    Task UpdateAgentLastCallDetailsAsync(int agentId, AgentHeartbeatDto heartbeatDto);
    Task UpdateICPAsync(int agentId, OverallCompatibilityReport icpReport);
    Task<AgentDto> GetByLoginIdAsync(string workspaceKey, string loginId);
    Task<OverallCompatibilityReport> GetIcpIncompatibilityReportAsync(int agentId);
}
﻿using System.Collections.ObjectModel;
using Sanas.Backend.BO.Dto;
using Sanas.Backend.BO.Dto.RequestDto;
using Sanas.Backend.Data;
using Sanas.Backend.Data.Repositories;

namespace Sanas.Backend.BO.Handlers;

public class NotificationsHandler(IUsersHandler usersHandler, INotificationsRepository notificationsRepo) : INotificationsHandler
{
    public async Task<PagedNotificationsDto> GetByUserId(int workspaceId, int userId, RequestOptions requestOptions, NotificationQueryFilters filters)
    {
        var queryOptions = new NotificationQueryOptions(requestOptions)
        {
            LoginIds = filters.LoginIds,
            NotificationTypes = filters.NotificationTypes,
            RequestingUserId = userId            
        };

        if (await usersHandler.IsWorkspaceAdmin(workspaceId, userId))
        {
            var queryResult = await notificationsRepo.GetByWorkspaceAsync(workspaceId, queryOptions);            

            var items = queryResult.Records.Select(NotificationDto.FromModel).ToList();

            items.ForEach(item => item.IsRead = item.ReadByUserIds.Any(id => id == userId));

            return new PagedNotificationsDto
            {
                Items = new Collection<NotificationDto>(items),
                RecordsCount = items.Count,
                TotalRecordsCount = queryResult.TotalRecords,
                TotalUnreadCount = queryResult.TotalUnreadCount,
                UnreadFeedbacksCount = queryResult.UnreadFeedbacksCount,
                UnreadAppEventsCount = queryResult.UnreadAppEventsCount
            };
        }
        else
        {
            var user = await usersHandler.GetByUserIdAsync(workspaceId, userId);
            var teamIds = await usersHandler.GetAccessibleTeamsAsync(workspaceId, user.PortalUserId);

            var queryResult = await notificationsRepo.GetByTeamsAsync(workspaceId, teamIds, queryOptions);

            var items = queryResult.Records.Select(NotificationDto.FromModel).ToList();

            items.ForEach(item => item.IsRead = item.ReadByUserIds.Any(id => id == userId));

            return new PagedNotificationsDto
            {
                Items = new Collection<NotificationDto>(items),
                RecordsCount = items.Count,
                TotalRecordsCount = queryResult.TotalRecords,
                TotalUnreadCount = queryResult.TotalUnreadCount,
                UnreadFeedbacksCount = queryResult.UnreadFeedbacksCount,
                UnreadAppEventsCount = queryResult.UnreadAppEventsCount
            };
        }
    }

    public async Task MarkAllAsReadAsync(int workspaceId, int userId, IEnumerable<string> types)
    {
        var notificationTypes = new List<int>();

        foreach (var type in types)
        {
            var typeValue = type.ToLower() switch
            {
                "userlogout" or "agentlogout" => 1,
                "sanasdisabled" or "synthesisdisabled" => 2,
                "feedback" or "feedbacksubmitted" => 3,
                _ => 0
            };

            notificationTypes.Add(typeValue);
        }

        await notificationsRepo.MarkAllAsReadAsync(workspaceId, userId, notificationTypes);
    }

    public async Task ToggleMarkAsRead(int notificationId, int userId, bool markAsUnread)
    {
        if (markAsUnread)
        {
            await notificationsRepo.MarkAsUnreadAsync(notificationId, userId);
        }
        else
        {
            await notificationsRepo.MarkAsReadAsync(notificationId, userId);
        }
    }
}

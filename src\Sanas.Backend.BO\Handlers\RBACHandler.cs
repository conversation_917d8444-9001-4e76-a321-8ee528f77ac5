﻿using Microsoft.Extensions.Logging;
using Sanas.Backend.BO.Dto;
using Sanas.Backend.BO.Dto.PortalUsers;
using Sanas.Backend.Data.Repositories;
using Sanas.Backend.Data.Repositories.Interfaces;

namespace Sanas.Backend.BO.Handlers;

public class RBACHandler(
    ILogger<RBACHandler> logger,
    IRBACRepository rbacRepository,
    ITeamsRepository teamsRepository) : IRBACHandler
{
    public async Task<RolesResponseDto> GetRolesAsync()
    {
        var result = new RolesResponseDto();

        var roleRecords = await rbacRepository.GetAvailableRolesAsync();
        var roles = roleRecords.Select(RoleDto.FromModel);

        foreach (var role in roles)
        {
            if (role.Name.StartsWith("sanas", StringComparison.OrdinalIgnoreCase))
            {
                if (!string.Equals(role.Name, "sanas:system-admin", StringComparison.OrdinalIgnoreCase))
                {
                    result.SanasRoles.Add(role);
                }
            }
            else
            {
                result.CustomerRoles.Add(role);
            }
        }

        return result;
    }

    public async Task<IEnumerable<string>> GetUserPermissionsAsync(int workspaceId, int userId, bool isSanasUser)
    {
        var permissions = new List<string>();
        var workspacePermissions = await rbacRepository.GetWorkspacePermissionsAsync(workspaceId, userId);

        permissions.AddRange(workspacePermissions);

        if (isSanasUser)
        {
            var sanasPermissions = await rbacRepository.GetSanasPermissionsAsync(userId);
            permissions.AddRange(workspacePermissions.Union(sanasPermissions));
        }

        var uniquePermssions = permissions.ToHashSet();
        return uniquePermssions;
    }

    public async Task<IEnumerable<UserScope>> GetUserWorkspaceScopesAsync(int userId, bool isSanasUser)
    {
        var scopes = new List<UserScope>();

        if (isSanasUser)
        {
            var sanasRolesAndPermissions = await rbacRepository.GetSanasRolesAndPermissionsAsync(userId);

            var scope = new UserScope()
            {
                WorkspaceId = 0,
                Roles = sanasRolesAndPermissions.Keys.Select(RoleDto.FromModel).ToList(),
                Permissions = sanasRolesAndPermissions.Values.SelectMany(p => p).ToHashSet().ToList()
            };

            scopes.Add(scope);
        }

        var workspacesScopes = await rbacRepository.GetWorkspaceScopesAsync(userId);

        foreach (var workspaceScope in workspacesScopes)
        {
            var scope = new UserScope
            {
                WorkspaceId = workspaceScope.Key,
                Roles = workspaceScope.Value.Keys.Select(RoleDto.FromModel).ToList(),
                Permissions = workspaceScope.Value.Values.SelectMany(p => p).ToHashSet().ToList(),
            };

            scopes.Add(scope);
        }

        return scopes;
    }

    public async Task UpdateUserRoleAtWorkspaceAsync(int workspaceId, UserRoleUpdateDto request)
    {
        await rbacRepository.UpdateWorkspaceUserRolesAsync(request.PortalUserId, workspaceId, [request.RoleName]);

        if (string.Equals(request.RoleName, "customer:admin", StringComparison.OrdinalIgnoreCase))
        {
            try
            {
                await teamsRepository.RemovePortalUserFromAllTeamsInWorkspaceAsync(workspaceId, request.PortalUserId);
            }
            catch (Exception e)
            {
                logger.LogError("Failed to remove portal users from team after. {Error}", e);
            }
        }
    }

    public async Task UpdateSanasUserRoleAsync(UserRoleUpdateDto request)
    {
        try
        {
            await rbacRepository.UpdateSanasUserRolesAsync(request.PortalUserId, [request.RoleName]);
        }
        catch (Exception e)
        {
            logger.LogError("Failed to change the role of Sanas user. {Error}", e);
        }
    }
}

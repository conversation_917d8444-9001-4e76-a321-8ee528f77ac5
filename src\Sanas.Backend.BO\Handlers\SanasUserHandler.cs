﻿using Microsoft.Extensions.Logging;
using Sanas.Auth0Client.Interfaces;
using Sanas.Backend.BO.Dto;
using Sanas.Backend.BO.Dto.RequestDto;
using Sanas.Backend.BO.Dto.ResponseDto;
using Sanas.Backend.BO.Extensions;
using Sanas.Backend.BO.Services;
using Sanas.Backend.Data.Models;
using Sanas.Backend.Data.Repositories;
using Sanas.Backend.Data.Repositories.Interfaces;

namespace Sanas.Backend.BO.Handlers
{
    public class SanasUserHandler(
        ILogger<SanasUserHandler> logger,
        IPortalUserRepository portalUserRepository,
        IRBACRepository rbacRepository,
        IAuth0UserManagementService userService,
        IUserInviterService userInviterService) : ISanasUserHandler
    {
        public async Task<PagedDto<PortalUserDto>> GetAllWithPagingAsync(RequestOptions queryOptions)
        {
            var (records, totalRecords) = await portalUserRepository.GetAllSanasUsersAsync(queryOptions);

            var result = new PagedDto<PortalUserDto>
            {
                Items = [],
                RecordsCount = records.Count,
                TotalRecordsCount = totalRecords
            };

            var availableRoles = (await rbacRepository.GetAvailableRolesAsync()).ToDictionary(r => r.RoleId, r => r.Description);

            foreach (var record in records)
            {
                try
                {
                    var sanasUser = PortalUserDto.FromDataModel(record);
                    sanasUser.Status = record.SanasUser.User.GetStatus();
                    sanasUser.Role = string.Join(", ", record.SanasUser.RoleIds
                        .Select(r => availableRoles.TryGetValue(r, out string value) ? value : null))
                        .TrimEnd(',');

                    result.Items.Add(sanasUser);
                }
                catch (Exception e)
                {
                    if (record.SanasUser is null)
                    {
                        logger.LogError("Error fetching details of Sanas user {UserId}. Reason - {Error}", record.PortalUserId, "user found in portal_users table but not in sanas_users table.");
                    }
                    else
                    {
                        logger.LogError("Error fetching details of Sanas user {UserId}. Reason - {Error}", record.PortalUserId, e.Message);
                    }
                }
            }

            return result;
        }

        public async Task<PortalUserGetDto> GetByUserIdAsync(int portalUserId)
        {
            var record = await portalUserRepository.GetByIdAsync(portalUserId);
            if (record is null || !record.IsSanasUser)
            {
                throw new ArgumentException("Sanas user not found or disabled");
            }

            return new PortalUserGetDto
            {
                PortalUserId = record.PortalUserId,
                Email = record.Email,
                FirstName = record.FirstName,
                LastName = record.LastName,
                Status = record.GetStatus(),
            };
        }

        public async Task EnableDisableSanasUserAsync(int portalUserId, bool disable)
        {
            var user = await portalUserRepository.GetByIdAsync(portalUserId);

            if (user is not null)
            {
                var idpUser = await userService.BlockUnblockUserAsync(user.IdpUserId, disable);

                if (idpUser.Blocked == false)
                {
                    logger.LogWarning("Unable to disable sanas user {email}", user.Email);
                }

                await portalUserRepository.EnableDisableSanasUserAsync(portalUserId, disable);

                if (disable)
                {
                    userInviterService.SendAccountDeactivationMaessage(user.Email);
                }
            }
            else
            {
                throw new ArgumentException("User not found or diabled.");
            }
        }

        public async Task<PortalUserDto> CreateSanasUserAsync(PortalUserCreateDto portalUser)
        {
            logger.LogInformation("Creating sanas user...");

            var existingUser = await portalUserRepository.GetByEmailAsync(portalUser.Email, 0);

            if (existingUser is null)
            {
                try
                {
                    var (user, passwordResetUrl) = await userService.CreateAuth0UserAsync(portalUser.FirstName, portalUser.LastName, portalUser.Email);

                    // Now create a record in our DB
                    var newRecord = new PortalUser
                    {
                        Email = portalUser.Email,
                        FirstName = portalUser.FirstName,
                        LastName = portalUser.LastName,
                        IdpUserId = user.UserId,
                        IsSanasUser = true
                    };

                    var record = await portalUserRepository.CreateSanasUserAsync(newRecord, portalUser.Roles.Select(r => r.Name));

                    if (!string.IsNullOrWhiteSpace(passwordResetUrl))
                    {
                        userInviterService.SendWorkspaceInvitationToNewUser(record.Email, passwordResetUrl);
                    }

                    return PortalUserDto.FromDataModel(record);
                }
                catch (Exception e)
                {
                    logger.LogInformation("Failed to create user. {Error}", e);
                    throw new Exception("Failed to create user");
                }
            }
            else
            {
                /*if (!existingUser.IsSanasUser && !portalUser.ForceMakeSanasUserIfAlreadyExists)
                {
                    throw new PortalException(PortalErrorCodes.NonSanasUserFound, $"Another non-Sanas user exists with email {portalUser.Email}");
                }*/

                logger.LogInformation("Existing user found. Trying to link with Sanas.");
                
                var sanasUser = await portalUserRepository.GetSanasUsersByIdAsync(existingUser.PortalUserId);

                if (sanasUser is null)
                {
                    sanasUser = new PortalUser
                    {
                        FirstName = portalUser.FirstName,
                        LastName = portalUser.LastName,
                        PortalUserId = existingUser.PortalUserId,
                        IdpUserId = existingUser.IdpUserId,
                        IsSanasUser = true
                    };

                    await portalUserRepository.LinkPortalUserToSanasUsersAsync(sanasUser, portalUser.Roles.Select(r => r.RoleId));
                    return PortalUserDto.FromDataModel(existingUser);
                }
                else
                {
                    throw new ArgumentException($"A Sanas user already exists with email {portalUser.Email}");
                }
            }
        }
    }
}

﻿using System.Collections.ObjectModel;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Sanas.Backend.BO.Dto;
using Sanas.Backend.BO.Dto.Reports;
using Sanas.Backend.BO.Dto.RequestDto;
using Sanas.Backend.BO.Dto.ResponseDto;
using Sanas.Backend.BO.Dto.Team;
using Sanas.Backend.BO.Extensions;
using Sanas.Backend.BO.Helpers;
using Sanas.Backend.BO.Services;
using Sanas.Backend.Data;
using Sanas.Backend.Data.Models;
using Sanas.Backend.Data.Repositories.Interfaces;

namespace Sanas.Backend.BO.Handlers;

public class TeamsHandler(
    IConfiguration configuration,
    ILogger<TeamsHandler> logger,
    ITeamsRepository teamsRepository,
    IAgentsRepository agentsRepository,
    IPortalUserRepository userRepository,
    IRBACHandler rbacHandler,
    ITelemetryReader telemetryReader) : ITeamsHandler
{
    public async Task<TeamDto> GetByTeamIdAsync(int workspaceId, int teamId)
    {
        var record = await teamsRepository.GetByTeamIdAsync(workspaceId, teamId);
        if (record is not null)
        {
            var dto = TeamDto.FromDataModel(record);
            dto.Agents = await teamsRepository.GetAgentsCountAsync(workspaceId, teamId);
            return dto;
        }

        return null;
    }

    public async Task<TeamDto> GetByTeamNameAsync(int workspaceId, string teamName)
    {
        var record = await teamsRepository.GetByTeamNameAsync(workspaceId, teamName);
        if (record is not null)
        {
            var dto = TeamDto.FromDataModel(record);
            dto.Agents = await teamsRepository.GetAgentsCountAsync(workspaceId, record.TeamId);
            return dto;
        }

        return null;
    }

    public async Task<TeamDto> CreateTeamAsync(int workspaceId, TeamCreateDto team)
    {
        var existingTeam = await teamsRepository.GetByTeamNameAsync(workspaceId, team.Name?.Trim());

        if (existingTeam is not null)
        {
            throw new ArgumentException($"Another team with name '{team.Name}' already exists in the workspace.");
        }

        if (team.Agents.Count > 0)
        {
            if (team.Agents.Select(a => a.LoginId.Trim()).ToHashSet().Count < team.Agents.Count)
            {
                throw new ArgumentException("Request contains users with duplicate Login IDs");
            }

            (var agentsInWorkspace, int _) = await agentsRepository.GetByWorkspaceIdWithPagingAsync(workspaceId, QueryOptions.FetchAll);

            var duplicateAgentIDs = new List<string>();

            foreach (var agent in team.Agents)
            {
                if (agentsInWorkspace.Any(a => string.Equals(a.LoginId, agent.LoginId.Trim(), StringComparison.OrdinalIgnoreCase)))
                {
                    duplicateAgentIDs.Add(agent.LoginId);
                }
            }

            if (duplicateAgentIDs.Count > 0)
            {
                throw new ArgumentException($"These agent IDs already exist in the workspace - ({string.Join(", ", duplicateAgentIDs)})");
            }
        }

        if (team.PortalUsersIds?.Count > 0)
        {
            var workspaceUsers = await userRepository.GetAllUsersInWorkspaceAsync(workspaceId);

            var invalidUsers = team.PortalUsersIds.Except(workspaceUsers.Select(wu => wu.PortalUserId));

            if (invalidUsers.Any())
            {
                throw new ArgumentException($"These user ids do not belong to this workspace - ({string.Join(", ", invalidUsers)})");
            }
        }

        var newRecord = await teamsRepository.CreateTeamAsync(workspaceId, team.Name);

        if (team.Agents?.Count > 0)
        {
            await agentsRepository.CreateManyAsync(team.Agents.Select(a => new Agent()
            {
                LoginId = a.LoginId.Trim(),
                Name = a.LoginId?.Trim(),
                Password = Crypto.GetHash(a.Password.Trim(), a.LoginId.Trim()),
                WorkspaceId = workspaceId,
                TeamId = newRecord.TeamId,
                CreatedUtc = DateTime.UtcNow,
                IsPasswordTemporary = true,
                AgentPreferences = new()
            }));
        }

        if (team.PortalUsersIds?.Count > 0)
        {
            await teamsRepository.AssignPortalUsersToTeamAsync(workspaceId, newRecord.TeamId, team.PortalUsersIds);
        }

        return TeamDto.FromDataModel(newRecord);
    }

    public async Task UpdateTeamAsync(int workspaceId, int teamId, TeamUpdateDto team)
    {
        var record = await teamsRepository.GetByTeamIdAsync(workspaceId, teamId);

        if (record == null || record.IsDisabled)
        {
            throw new ArgumentException($"No team found for ID {teamId}");
        }

        var existingTeams = await teamsRepository.GetAllTeamsInWorkspaceAsync(record.WorkspaceId);

        if (existingTeams.Any(t => string.Equals(t.Name.Trim(), team.Name.Trim(), StringComparison.OrdinalIgnoreCase) && t.TeamId != teamId))
        {
            throw new ArgumentException($"Another team exists with name {team.Name}");
        }

        var workspaceUsers = await userRepository.GetAllUsersInWorkspaceAsync(record.WorkspaceId);

        if (team.PortalUserIds is not null && team.PortalUserIds.Any())
        {
            var invalidUsers = team.PortalUserIds.Except(workspaceUsers.Select(wu => wu.PortalUserId));

            if (invalidUsers.Any())
            {
                throw new ArgumentException($"These user ids do not belong to this workspace - ({string.Join(", ", invalidUsers)})");
            }
        }

        await teamsRepository.UpdateTeamAsync(workspaceId, teamId, team.Name, team.PortalUserIds);
    }

    public async Task EnableOrDisableTeamAsync(int workspaceId, int teamId, bool disable)
    {
        if (disable)
        {
            await teamsRepository.DisableTeamAsync(workspaceId, teamId);
        }
        else
        {
            await teamsRepository.ReactivateTeamAsync(workspaceId, teamId);
        }
    }

    public async Task<PagedDto<AgentDto>> GetAgentsInTeamWithPagingAsync(int workspaceId, int teamId, RequestOptions queryOptions, AgentQueryFilters filterOptions)
    {
        var team = await teamsRepository.GetByTeamIdAsync(workspaceId, teamId);

        if (team is null || team.IsDisabled)
        {
            throw new ArgumentException("Team not found or disabled");
        }

        var agentQueryOptions = new AgentsQueryOptions(queryOptions)
        {
            AppStatuses = filterOptions.AppStatuses,
            AppVersions = filterOptions.AppVersions,
            Voices = filterOptions.Voices,
            LicenseStatuses = filterOptions.LicenseStatuses,
            IcpStatuses = filterOptions.IcpStatuses
        };

        var (records, totalRecords) = await agentsRepository.GetByTeamIdWithPagingAsync(teamId, agentQueryOptions);
        var agents = records.Select(AgentDto.FromDataModel).ToList();
        await LoadLastCallDetails(team, agents);

        return new PagedDto<AgentDto>
        {
            Items = new Collection<AgentDto>(agents),
            RecordsCount = agents.Count,
            TotalRecordsCount = totalRecords
        };
    }

    public async Task<byte[]> DownloadAgentsReportAsync(int workspaceId, int teamId, TeamsReportDownloadOptions queryOptions, AgentQueryFilters filterOptions)
    {
        List<AgentDto> agents = null;
        var team = await teamsRepository.GetByTeamIdAsync(workspaceId, teamId);

        if (team is null || team.IsDisabled)
        {
            throw new ArgumentException("Team not found or disabled");
        }

        var agentQueryOptions = new AgentsQueryOptions(queryOptions)
        {
            AppStatuses = filterOptions.AppStatuses,
            AppVersions = filterOptions.AppVersions,
            Voices = filterOptions.Voices,
            LicenseStatuses = filterOptions.LicenseStatuses,
            IcpStatuses = filterOptions.IcpStatuses,
        };

        if (queryOptions.SelectedAgentIds is null || queryOptions.SelectedAgentIds.Length == 0)
        {
            var (records, totalRecords) = await agentsRepository.GetAllAgentsInTeamAsync(teamId, agentQueryOptions);
            agents = records.Select(AgentDto.FromDataModel).ToList();
        }
        else
        {
            var records = await agentsRepository.GetAgentsByIdAsync(workspaceId, queryOptions.SelectedAgentIds);
            agents = records.Select(AgentDto.FromDataModel).ToList();
        }

        foreach (var agent in agents)
        {
            if (agent.LastLoginUtc.HasValue && agent.LastSeenUtc.HasValue)
            {
                if (agent.LastLoginUtc.Value > agent.LastSeenUtc.Value)
                {
                    agent.LastSeenUtc = null;
                }
            }
        }

        await LoadLastCallDetails(team, agents);

        var headers = new string[]
        {
            "User ID", "Name", "App Status", "Last Logged In (UTC)", "Last Logged Out (UTC)", "Last Engaged (UTC)", "Voice", "Version", "Machine", "License"
        };

        var rows = agents.Select(a => new string[]
        {
            a.LoginId,
            a.AgentName,
            a.SanasStatus,
            a.LastLoginUtc.HasValue ? a.LastLoginUtc.Value.ToString() : null,
            a.LastSeenUtc.HasValue ? a.LastSeenUtc.Value.ToString() : null,
            a.LastEngagedUtc.HasValue ? a.LastEngagedUtc.Value.ToString() : null,
            a.Voice,
            a.AppVersion,
            a.MachineName,
            a.IsDisabled ? "Inactive" : "Active"
        }).ToArray();

        byte[] reportData = ExcelGenerator.GenerateExcel(headers, rows);
        return reportData;
    }

    public async Task SetAsDefaultTeamAsync(int workspaceId, int teamId)
    {
        await teamsRepository.SetAsDefaultTeamAsync(workspaceId, teamId);
    }

    public async Task<IEnumerable<PortalUserDto>> GetTeamSupervisorsAsync(int workspaceId, int teamId)
    {
        var teamUsers = await teamsRepository.GetTeamUsersAsync(workspaceId, teamId);
        var sendList = new List<PortalUserDto>();

        foreach (var user in teamUsers)
        {
            if (user.GetStatus(workspaceId) != UserStatus.Active)
            {
                continue;
            }

            var permissions = await rbacHandler.GetUserPermissionsAsync(workspaceId, user.PortalUserId, user.IsSanasUser);

            if (permissions.Any(p => string.Equals(p, Permissions.CreateAppUser, StringComparison.OrdinalIgnoreCase)))
            {
                sendList.Add(PortalUserDto.FromDataModel(user));
            }
        }

        return sendList;
    }

    public async Task<TeamStats> GetTeamStatsAsync(int teamId, int duration)
    {
        var stats = new TeamStats();
        var agents = await teamsRepository.GetActiveAgentsInTeamAsync(teamId);

        if (agents.Any())
        {
            stats.TotalUsers = agents.Count();
            stats.UsersWithSanasDisabled = agents.Count(a => a.AppStatus == 2);
            stats.LoggedOutUsers = agents.Count(agent => !agent.LastLoginUtc.HasValue || agent.LastSeenUtc > agent.LastLoginUtc);
            stats.NotEngagedUsers = await GetEngagedCountAsync(teamId, duration);
            stats.IcpCompatibilities = new IcpCompatibilityCount
            {
                Good = agents.Count(a => a.IcpStatus == 0),
                Issue = agents.Count(a => a.IcpStatus == 1),
                Critical = agents.Count(a => a.IcpStatus == 2)
            };
        }

        return stats;
    }

    private async Task<string> GetEngagedCountAsync(int teamId, int durationInHours)
    {
        try
        {
            var environment = configuration["SanasApp:Environment"];
            var appEnv = "('prod')";

            if (string.Equals(environment, "dev", StringComparison.OrdinalIgnoreCase))
            {
                appEnv = "('dev', 'pr')";
            }
            else if (string.Equals(environment, "uat", StringComparison.OrdinalIgnoreCase))
            {
                appEnv = "('uat')";
            }

            var engagedCount = 0;
            var kql = $@"AppEvents
                            | where Name == 'AudioSessionStateChangedEvent'    
                            | where Properties.AgentTeamId == {teamId}
                                and Properties.AppGeneration == '{configuration["SanasApp:Generation"] ?? "V2"}'
                                and Properties.AppEnvironment in {appEnv}
                                and Properties.IsSanasEnabled == 'True' 
                                and Properties.IsSanasMicrophoneMuted == 'False'
                                and Properties.State == 'Active'
                                and Properties.ProcessName != 'Settings' and Properties.ProcessName !contains 'Rundll32' 
                            | where datetime_diff('hour', now(), TimeGenerated) < {durationInHours}
                            | summarize arg_max(TimeGenerated, *) by UserId
                            | project toint(UserAuthenticatedId)";

            var engagedAgents = await telemetryReader.ReadResultSetAsync<int>(kql);
            var agentsInTeam = await teamsRepository.GetActiveAgentsInTeamAsync(teamId);
            engagedCount = agentsInTeam.Count(agent => !engagedAgents.Contains(agent.AgentId));

            return engagedCount.ToString();
        }
        catch (Exception e)
        {
            logger.LogError("Error in fetching last call details from telemetry, {Error}", e);
            return "Error...";
        }
    }

    private async Task LoadLastCallDetails(Team team, List<AgentDto> agents)
    {
        if (agents is null || agents.Count == 0)
        {
            return;
        }

        try
        {
            var environment = configuration["SanasApp:Environment"];
            var appEnv = "('prod')";

            if (string.Equals(environment, "dev", StringComparison.OrdinalIgnoreCase))
            {
                appEnv = "('dev', 'pr')";
            }
            else if (string.Equals(environment, "uat", StringComparison.OrdinalIgnoreCase))
            {
                appEnv = "('uat')";
            }

            var filter = string.Join(',', agents.Select(a => $"'sanas.{team.Workspace.WorkspaceKey.ToLower()}.{a.LoginId.ToLower()}'"));
            var kql = $@"AppEvents
                    | where Name == 'AudioSessionStateChangedEvent'
                    | where UserId in ({filter})
                    | where Properties.AppGeneration == '{configuration["SanasApp:Generation"]}'
                        and Properties.AppEnvironment in {appEnv}
                        and Properties.IsSanasEnabled == 'True' 
                        and Properties.IsSanasMicrophoneMuted == 'False'
                        and Properties.State == 'Active'
                        and Properties.ProcessName != 'Settings' and Properties.ProcessName !contains 'Rundll32' 
                    | summarize arg_max(TimeGenerated, *) by UserId
                    | project UserAuthenticatedId,
                              AppVersion = Properties.AppVersion,
                              MachineName = Properties.MachineName,                              
                              LastEngagedUtc = TimeGenerated";

            var lastCallInfo = (await telemetryReader.ReadResultSetAsync<AgentLastCallDto>(kql))
                .ToDictionary(info => info.UserAuthenticatedId, info => info);

            foreach (var agent in agents)
            {
                if (lastCallInfo.TryGetValue(agent.AgentId.ToString(), out AgentLastCallDto value))
                {
                    agent.AppVersion ??= value.AppVersion;
                    agent.MachineName ??= value.MachineName;
                    agent.LastEngagedUtc = value.LastEngagedUtc;
                }

                agent.AgentPreferences = null;
            }
        }
        catch (Exception e)
        {
            logger.LogError("Error in fetching last call details from telemetry, {Error}", e);

            foreach (var agent in agents)
            {
                agent.AppVersion = "Error...";
                agent.MachineName = "Error...";
                agent.Voice = "Error...";
                agent.AgentPreferences = null;
            }
        }
    }
}
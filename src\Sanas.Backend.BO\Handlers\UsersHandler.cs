﻿using Sanas.Auth0Client.Interfaces;
using Sanas.Backend.BO.Dto;
using Sanas.Backend.BO.Dto.RequestDto;
using Sanas.Backend.BO.Extensions;
using Sanas.Backend.BO.Handlers;
using Sanas.Backend.BO.Helpers;
using Sanas.Backend.BO.Services;
using Sanas.Backend.Data.Repositories.Interfaces;

namespace Sanas.Backend.BO.Controllers;

public class UsersHandler(
    IUserInviterService userInviterService,
    IAuth0UserManagementService userManager,
    IPortalUserRepository portalUserRepository,
    IRBACHandler rbacHandler) : IUsersHandler
{
    public async Task<PortalUserGetDto> GetByUserIdAsync(int workspaceId, int portalUserId)
    {
        var record = await portalUserRepository.GetByIdAsync(portalUserId) ?? throw new ArgumentException("User not found");

        return new PortalUserGetDto
        {
            PortalUserId = record.PortalUserId,
            Email = record.Email,
            FirstName = record.FirstName,
            LastName = record.LastName,
            Status = record.GetStatus(workspaceId),
        };
    }

    public async Task<PortalUserGetDto> GetByUserIdAsync(string idpUserId)
    {
        var record = await portalUserRepository.GetByIdpUserIdAsync(idpUserId);

        if (record is not null)
        {
            return new PortalUserGetDto
            {
                PortalUserId = record.PortalUserId,
                Email = record.Email,
                FirstName = record.FirstName,
                LastName = record.LastName,
                IsSanasUser = record.IsSanasUser,
            };
        }

        return null;
    }

    public async Task UpdateUserProfileAsync(string idpUserId, string firstName, string lastName)
    {
        var user = await portalUserRepository.GetByIdpUserIdAsync(idpUserId);

        if (user is null || user.IsDisabled)
        {
            throw new ArgumentException("User not found or disabled.");
        }

        await portalUserRepository.UpdateUserProfileAsync(idpUserId, firstName, lastName);
        await userManager.UpdateUserProfileAsync(idpUserId, firstName, lastName);
    }

    public async Task UpdateUserPasswordAsync(string idpUserId, string password)
    {
        var user = await portalUserRepository.GetByIdpUserIdAsync(idpUserId);

        if (user is null || user.IsDisabled)
        {
            throw new ArgumentException("User not found or disabled.");
        }

        await userManager.UpdateUserPasswordAsync(idpUserId, password);
    }

    public async Task EnableDisablePortalUserAsync(int workspaceId, int portalUserId, bool disable)
    {
        var recordToDelete = await portalUserRepository.GetByIdAsync(portalUserId);

        if (recordToDelete is not null)
        {
            await portalUserRepository.EnableDisableWorkspaceUserAsync(workspaceId, portalUserId, disable: disable);

            var userWorkspaceIds = await portalUserRepository.GetUserWorkspaceIdsAsync(portalUserId, includeDisabled: false);

            if (disable)
            {
                if (!userWorkspaceIds.Any() && !recordToDelete.IsSanasUser)
                {
                    var user = await userManager.BlockUnblockUserAsync(recordToDelete.IdpUserId, block: true);
                    if (user is not null && user.Blocked == disable)
                    {
                        userInviterService.SendAccountDeactivationMaessage(user.Email);
                    }
                }
            }
            else
            {
                await userManager.BlockUnblockUserAsync(recordToDelete.IdpUserId, block: false);
            }
        }
        else
        {
            throw new ArgumentException("Portal user not found");
        }
    }

    public async Task<bool> EmailExists(string email, int workspaceId)
    {
        var user = await portalUserRepository.GetByEmailAsync(email?.Trim(), workspaceId);
        return user is not null;
    }

    public async Task<IEnumerable<int>> GetAccessibleTeamsAsync(int workspaceId, string idpUserId)
    {
        return await portalUserRepository.GetAccessibleTeamsAsync(workspaceId, idpUserId);
    }

    public async Task<IEnumerable<int>> GetAccessibleTeamsAsync(int workspaceId, int portalUserId)
    {
        var user = await portalUserRepository.GetByIdAsync(portalUserId);
        if (user is not null && !user.IsDisabled)
        {
            return await portalUserRepository.GetAccessibleTeamsAsync(workspaceId, user.IdpUserId);
        }

        return [];
    }

    public async Task<IEnumerable<int>> GetWorkspaceIdsForUserAsync(string idpUserId)
    {
        return await portalUserRepository.GetUserWorkspaceIdsAsync(idpUserId);
    }

    public async Task<IEnumerable<int>> GetWorkspaceIdsForUserAsync(int portalUserId, bool includeDisabled)
    {
        return await portalUserRepository.GetUserWorkspaceIdsAsync(portalUserId, includeDisabled);
    }

    public async Task ResendInviteAsync(int workspaceId, int portalUserId)
    {
        var user = await portalUserRepository.GetByIdAsync(portalUserId);

        if (user is not null)
        {
            if (user.GetStatus(workspaceId) is UserStatus.Unknown or UserStatus.Inactive)
            {
                throw new ArgumentException("Cannot send invite. The user status inactive or unknown.");
            }

            var passwordResetUrl = await userManager.CreatePasswordChangeRequestAsync(user.IdpUserId);

            if (!string.IsNullOrWhiteSpace(passwordResetUrl))
            {
                userInviterService.SendWorkspaceInvitationToNewUser(user.Email, passwordResetUrl);
                await portalUserRepository.UpdateLastInvitedUtcAsync(user.PortalUserId, DateTime.UtcNow);
            }
        }
        else
        {
            throw new ArgumentException("User not found");
        }
    }

    public async Task CancelUserInviteAsync(int workspaceId, int portalUserId)
    {
        var user = await portalUserRepository.GetByIdAsync(portalUserId) ?? throw new ArgumentException("User not found");

        if (!(user.GetStatus(workspaceId) is UserStatus.Invited or UserStatus.InviteExpired))
        {
            throw new ArgumentException("User is not in a state to cancel the invitation");
        }

        await portalUserRepository.RemoveUserFromWorkspaceAsync(workspaceId, portalUserId);

        var userWorkspaceIds = await portalUserRepository.GetUserWorkspaceIdsAsync(portalUserId, true);

        if (userWorkspaceIds.Count == 0)
        {
            await portalUserRepository.DeletePortalUserAsync(portalUserId);
            await userManager.DeleteUserAsync(user.IdpUserId);
        }
    }

    public async Task<bool> IsWorkspaceAdmin(int workspaceId, int userId)
    {
        var user = await portalUserRepository.GetByIdAsync(userId);

        if (user is not null)
        {
            if (user.IsSanasUser)
            {
                return true;
            }

            var permissions = await rbacHandler.GetUserPermissionsAsync(workspaceId, user.PortalUserId, user.IsSanasUser);

            if (permissions.Any(p => string.Equals(p, Permissions.CreateTeam, StringComparison.OrdinalIgnoreCase)))
            {
                return true;
            }
        }

        return await Task.FromResult(false);
    }

    public async Task UpdateNotificationTokenAsync(string userId, string token)
    {
        await portalUserRepository.UpdateNotificationTokenAsync(userId, token);
    }

    public async Task<PortalUserGetDto> GetByUserEmailAsync(string email)
    {
        var record = await portalUserRepository.GetByEmailAsync(email?.Trim(), workspaceId: 0);

        if (record is not null)
        {
            return new PortalUserGetDto
            {
                PortalUserId = record.PortalUserId,
                Email = record.Email,
                FirstName = record.FirstName,
                LastName = record.LastName,
                IsSanasUser = record.IsSanasUser,
            };
        }

        return null;
    }

    public async Task<PortalUserDto> CreateNCUserAsync(int workspaceId, string idpUserId, NCUser newUser)
    {
        var record = new Data.Models.PortalUser
        {
            Email = newUser.Email,
            FirstName = newUser.FirstName,
            LastName = newUser.LastName,
            IdpUserId = idpUserId,
            IsAccountVerified = true
        };

        var user = await portalUserRepository.CreatePortalUserAsync(record, workspaceId, ["customer:admin"]);

        await portalUserRepository.CreateNCUserTrackingAsync(user.PortalUserId, workspaceId);

        return PortalUserDto.FromDataModel(user);
    }
}

﻿using System.Collections.ObjectModel;
using System.Net.Mail;
using System.Text.Json;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Sanas.Auth0Client.Interfaces;
using Sanas.Backend.BO.Dto;
using Sanas.Backend.BO.Dto.Dashboards;
using Sanas.Backend.BO.Dto.Enums;
using Sanas.Backend.BO.Dto.RequestDto;
using Sanas.Backend.BO.Dto.ResponseDto;
using Sanas.Backend.BO.Extensions;
using Sanas.Backend.BO.Helpers;
using Sanas.Backend.BO.Services;
using Sanas.Backend.Data;
using Sanas.Backend.Data.Models;
using Sanas.Backend.Data.Repositories;
using Sanas.Backend.Data.Repositories.Interfaces;

namespace Sanas.Backend.BO.Handlers;

public class WorkspaceHandler(
    ILogger<WorkspaceHandler> logger,
    IConfiguration configuration,
    IWorkspaceRepository workspaceRepo,
    ITeamsRepository teamRepository,
    IPortalUserRepository portalUserRepo,
    IAgentsRepository agentsRepository,
    IAuth0UserManagementService userManagementService,
    IUserInviterService userInviterService,
    IRBACRepository rbacHandler,
    ITelemetryReader telemetryReader) : IWorkspaceHandler
{
    private readonly IWorkspaceRepository _workspaceRepo = workspaceRepo;
    private readonly ITeamsRepository _teamRepository = teamRepository;
    private readonly IPortalUserRepository _portalUserRepo = portalUserRepo;
    private readonly IAgentsRepository _agentsRepository = agentsRepository;
    private readonly IAuth0UserManagementService _userManager = userManagementService;

    public async Task<IEnumerable<WorkspaceDto>> GetByUserIdAsync(int portalUserId)
    {
        var result = await _workspaceRepo.GetByUserIdAsync(portalUserId);
        return result.Select(WorkspaceDto.FromDataModel);
    }

    public async Task<PagedDto<WorkspaceDto>> GetAllWithPagingAsync(RequestOptions queryOptions, bool skipAgents)
    {
        var (records, totalRecords) = await _workspaceRepo.GetAllWithPagingAsync(queryOptions, skipAgents);
        var workspaces = records.Select(WorkspaceDto.FromDataModel).ToList();

        return new PagedDto<WorkspaceDto>
        {
            TotalRecordsCount = totalRecords,
            RecordsCount = workspaces.Count,
            Items = new Collection<WorkspaceDto>(workspaces),
        };
    }

    public async Task<PagedDto<WorkspaceDto>> GetByUserIdWithPagingAsync(int portalUserId, RequestOptions queryOptions)
    {
        // Return only the workspaces that the user is assigned to.
        var (records, totalRecords) = await _workspaceRepo.GetByUserIdWithPagingAsync(portalUserId, queryOptions);
        var workspaces = records.Select(WorkspaceDto.FromDataModel).ToList();

        /*foreach (var record in records)
        {
            var dto = WorkspaceDto.FromDataModel(record);
            dto.LicensedSeats = await _workspaceRepo.GetAgentsCountAsync(dto.WorkspaceId);
            workspaces.Add(dto);
        }*/

        return new PagedDto<WorkspaceDto>
        {
            TotalRecordsCount = totalRecords,
            RecordsCount = workspaces.Count,
            Items = new Collection<WorkspaceDto>(workspaces),
        };
    }

    public async Task<WorkspaceDto> GetByIdAsync(int workspaceId)
    {
        var record = await _workspaceRepo.GetByIdAsync(workspaceId);
        return WorkspaceDto.FromDataModel(record);
    }

    public async Task<WorkspaceDto> CreateWorkspaceAsync(WorkspaceCreateDto workspace)
    {
        var defaultTeamName = /*configuration["Settings:DefaultTeamName"] ??*/ "Default Team";
        var newRecord = await _workspaceRepo.CreateWorkspaceAsync(new Workspace
        {
            Enterprise = workspace.Enterprise.Trim(),
            CountryCode = workspace.CountryCode.Trim(),
            ModelTypes = workspace.ModelTypes,
            AllocatedSeats = workspace.AllocatedSeats,
            BpoSite = new BpoSite
            {
                BPO = workspace.BPO.Trim(),
                Site = workspace.Site.Trim(),
            }
        });

        await _teamRepository.CreateTeamAsync(newRecord.WorkspaceId, defaultTeamName, true);
        await AddPortalUsersToWorkspace(newRecord, workspace.UserInvites);

        return WorkspaceDto.FromDataModel(newRecord);
    }

    public async Task UpdateWorkspaceAsync(int workspaceId, WorkspaceUpdateDto workspace)
    {
        var existingWorkspace = await _workspaceRepo.GetByIdAsync(workspaceId);
        if (existingWorkspace != null)
        {
            if (existingWorkspace.AllocatedSeats > workspace.AllocatedSeats && string.IsNullOrWhiteSpace(workspace.SeatAdjustmentReason))
            {
                throw new ArgumentException("Please provide a reson for adjusting the seats");
            }

            await _workspaceRepo.UpdateWorkspaceAsync(workspaceId, new Workspace
            {
                Enterprise = workspace.Enterprise.Trim(),
                CountryCode = workspace.CountryCode.Trim(),
                ModelTypes = workspace.ModelTypes,
                AllocatedSeats = workspace.AllocatedSeats,
                BpoSite = new BpoSite
                {
                    BPO = workspace.BPO.Trim(),
                    Site = workspace.Site.Trim(),
                }
            });

            await SendLicenseAdjustmentEmail(workspaceId, existingWorkspace.AllocatedSeats, workspace.AllocatedSeats);
        }
    }

    public async Task DeleteWorkspaceAsync(int workspaceId)
    {
        var deletedRecord = await _workspaceRepo.DeleteWorkspaceAsync(workspaceId);
        if (deletedRecord is null)
        {
            throw new ArgumentException($"Workspace not found.");
        }
    }

    public async Task<PagedDto<PortalUserDto>> GetUsersInWorkspaceByRoleAsync(int workspaceId, string roleName, RequestOptions queryOptions)
    {
        var (records, total) = await _portalUserRepo.GetByRoleNameAsync(workspaceId, roleName, queryOptions);
        var users = records.Select(PortalUserDto.FromPortalUserLite).ToList();

        return new PagedDto<PortalUserDto>()
        {
            Items = new Collection<PortalUserDto>(users),
            RecordsCount = users.Count,
            TotalRecordsCount = total
        };
    }

    public async Task<PagedDto<PortalUserDto>> GetUsersInWorkspaceWithPagingAsync(int workspaceId, RequestOptions queryOptions, string role)
    {
        if (!string.IsNullOrWhiteSpace(role))
        {
            if (string.Equals(role, "supervisor", StringComparison.OrdinalIgnoreCase))
            {
                role = "customer:supervisor";
            }
            else if (string.Equals(role, "admin", StringComparison.OrdinalIgnoreCase))
            {
                role = "customer:admin";
            }

            return await GetUsersInWorkspaceByRoleAsync(workspaceId, role, queryOptions);
        }

        var (records, totalRecords) = await _portalUserRepo.GetByWorkspaceIdWithPagingAsync(workspaceId, queryOptions);
        var usersWithRoles = await rbacHandler.GetWorkspaceUsersAndTheirRolesAsync(workspaceId);

        var result = new PagedDto<PortalUserDto>()
        {
            RecordsCount = records.Count,
            TotalRecordsCount = totalRecords,
            Items = []
        };

        foreach (var record in records)
        {
            var dto = PortalUserDto.FromDataModel(record);

            if (usersWithRoles.TryGetValue(record.PortalUserId, out IEnumerable<Role> roles))
            {
                dto.Role = string.Join(", ", roles.Select(r => r.Description));
                dto.Status = record.GetStatus(workspaceId);
            }

            result.Items.Add(dto);
        }

        return result;
    }

    public async Task<PagedDto<TeamDto>> GetTeamsInWorkspaceWithPagingAsync(int workspaceId, RequestOptions queryOptions)
    {
        var (teams, totalRecords) = await _teamRepository.GetByWorkspaceIdWithPagingAsync(workspaceId, queryOptions);

        var records = new List<TeamDto>();

        foreach (var record in teams)
        {
            var dto = TeamDto.FromDataModel(record);
            dto = TeamDto.UpdateSupervisors(dto, await _teamRepository.GetSupervisorsInTeamAsync(workspaceId, dto.TeamId));
            dto.Agents = await _teamRepository.GetAgentsCountAsync(workspaceId, dto.TeamId);
            records.Add(dto);
        }

        return new PagedDto<TeamDto>
        {
            Items = new Collection<TeamDto>(records),
            RecordsCount = records.Count,
            TotalRecordsCount = totalRecords
        };
    }

    public async Task<PagedDto<AgentDto>> GetAgentsInWorkspaceWithPagingAsync(int workspaceId, RequestOptions queryOptions)
    {
        var (agents, totalRecords) = await _agentsRepository.GetByWorkspaceIdWithPagingAsync(workspaceId, queryOptions);

        var agentRecords = agents.Select(AgentDto.FromDataModel).ToList();

        return new PagedDto<AgentDto>
        {
            Items = new Collection<AgentDto>(agentRecords),
            RecordsCount = agentRecords.Count,
            TotalRecordsCount = totalRecords
        };
    }

    public async Task ReactivateWorkspace(int workspaceId)
    {
        await _workspaceRepo.ReactivateWorkspaceAsync(workspaceId);
    }

    public async Task InviteUsersToWorkspaceAsync(int workspaceId, IEnumerable<UserInvite> userInvites)
    {
        var workspace = await _workspaceRepo.GetByIdAsync(workspaceId);

        if (workspace is not null)
        {
            await AddPortalUsersToWorkspace(workspace, userInvites);
            return;
        }

        throw new ArgumentException("Workspace not found");
    }

    private async Task AddPortalUsersToWorkspace(Workspace workspace, IEnumerable<UserInvite> userInvites)
    {
        if (userInvites is null || !userInvites.Any())
        {
            return;
        }

        foreach (var invite in userInvites)
        {
            var existingUser = await _portalUserRepo.GetByEmailAsync(invite.Email?.Trim(), 0);

            if (existingUser == null)
            {
                // First create a user record in Auth0
                var (user, passwordResetUrl) = await _userManager.CreateAuth0UserAsync(invite.Email, null, invite.Email);

                var portalUserRecord = new PortalUser
                {
                    Email = invite.Email.Trim(),
                    FirstName = invite.Email.Trim(),
                    IdpUserId = user.UserId,
                    IsSanasUser = false
                };

                await _portalUserRepo.CreatePortalUserAsync(portalUserRecord, workspace.WorkspaceId, [invite.RoleName]);

                if (!string.IsNullOrWhiteSpace(passwordResetUrl))
                {
                    bool sendInviteEmail = true;
                    var ssoDomains = configuration["Sso:Domains"];

                    if (!string.IsNullOrWhiteSpace(ssoDomains))
                    {
                        var domains = ssoDomains.Split(',', StringSplitOptions.RemoveEmptyEntries);

                        if (domains.Any(domain => invite.Email.EndsWith(domain, StringComparison.OrdinalIgnoreCase)))
                        {
                            sendInviteEmail = false;
                        }
                    }

                    if (sendInviteEmail)
                    {
                        userInviterService.SendWorkspaceInvitationToNewUser(portalUserRecord.Email, passwordResetUrl);
                    }
                }
            }
            else if (await _portalUserRepo.GetByEmailAsync(invite.Email, workspace.WorkspaceId) is null)
            {
                await _portalUserRepo.AddUserToWorkspaceAsync(existingUser.PortalUserId, workspace.WorkspaceId, invite.RoleName);
                userInviterService.SendWorkspaceInvitationToExistingUser(existingUser.Email, configuration["Settings:PortalEndpoint"], $"{workspace.BpoSite.BPO} {workspace.Enterprise} ({workspace.BpoSite.Site})");
            }
        }
    }

    public async Task<TeamDto> GetDefaultTeamAsync(int workspaceId)
    {
        var defaultTeam = await _workspaceRepo.GetDefaultTeamAsync(workspaceId);
        return TeamDto.FromDataModel(defaultTeam);
    }

    private async Task SendLicenseAdjustmentEmail(int workspaceId, int oldLicenseCount, int newLicenseCount)
    {
        try
        {
            if (oldLicenseCount == newLicenseCount)
            {
                return;
            }

            var (users, _) = await _portalUserRepo.GetByWorkspaceIdsWithPagingAsync([workspaceId], QueryOptions.FetchAll);
            var sendList = new List<string>();

            foreach (var user in users)
            {
                var roles = await rbacHandler.GetUserRolesAsync(user.PortalUserId, workspaceId);

                if (roles.Any(role => role.Equals("customer:admin", StringComparison.OrdinalIgnoreCase)))
                {
                    sendList.Add(user.Email);
                }
            }

            userInviterService.SendLicenseAdjustMessage(sendList, oldLicenseCount, newLicenseCount);
        }
        catch (Exception e)
        {
            logger.LogWarning("Failed to send license adjustment email to one or more receipients. \r\n{Exception}", e);
        }
    }

    public async Task<Dictionary<string, int>> GetAllTeamNamesWithIdsAsync(int workspaceId)
    {
        return await _workspaceRepo.GetAllTeamNamesWithIdsAsync(workspaceId);
    }

    public async Task<PagedDto<string>> GetBPOsAsync(RequestOptions requestOptions)
    {
        var (records, totalRecords) = await _workspaceRepo.GetAllBposAsync(requestOptions);
        var items = new List<string>();

        foreach (var record in records)
        {
            items.Add(record);
        }

        return new PagedDto<string>
        {
            TotalRecordsCount = totalRecords,
            RecordsCount = items.Count,
            Items = new Collection<string>(items),
        };
    }

    public async Task<PagedDto<string>> GetSitesAsync(RequestOptions requestOptions)
    {
        var (records, totalRecords) = await _workspaceRepo.GetAllSitesAsync(requestOptions);
        var items = new List<string>();

        foreach (var record in records)
        {
            items.Add(record);
        }

        return new PagedDto<string>
        {
            TotalRecordsCount = totalRecords,
            RecordsCount = items.Count,
            Items = new Collection<string>(items),
        };
    }

    public async Task<IEnumerable<PortalUserDto>> GetWorkspaceAdminsAsync(int workspaceId)
    {
        var usersWhoCanCreateTeams = await _workspaceRepo.GetUsersByPermissionsAsync(workspaceId, [Permissions.CreateTeam]);
        return usersWhoCanCreateTeams.Select(PortalUserDto.FromDataModel);
    }

    public async Task<string> GenerateEnterpriseNameFromEmailAsync(string email)
    {
        var emailAddress = new MailAddress(email.Trim());
        return await _workspaceRepo.GetIncrementalEnterpriseNameAsync(emailAddress.Host.Split('.')[0]);
    }

    public async Task UpdateAutoAppConfigurationAsync(int workspaceId, JsonElement appConfig)
    {
        await _workspaceRepo.UpdateAutoAppConfigurationAsync(workspaceId, appConfig);
    }

    private async Task<List<AgentActivityCount>> GetAgentActivityCountsAsync(string workspaceKey, int duration, bool isDurationInHours)
    {
        var environment = configuration["SanasApp:Environment"];
        var appEnv = "('prod')";

        if (string.Equals(environment, "dev", StringComparison.OrdinalIgnoreCase))
        {
            appEnv = "('dev', 'pr')";
        }
        else if (string.Equals(environment, "uat", StringComparison.OrdinalIgnoreCase))
        {
            appEnv = "('uat')";
        }

        var query = $@"AppEvents
                        | where TimeGenerated >= ago({duration}{(isDurationInHours ? "h" : "d")})
                            and Properties.AppGeneration == '{configuration["SanasApp:Generation"] ?? "V2"}'
                            and Properties.AppEnvironment in {appEnv}
                            and Properties.AgentWorkspaceKey =~ '{workspaceKey}'
                            and Properties.ProcessName != 'Settings' and Properties.ProcessName !contains 'Rundll32'
                        | summarize EngagedUsersCount = dcountif(UserAuthenticatedId, 
                                                              Name == 'AudioSessionStateChangedEvent' and
                                                              Properties.State == 'Active' and 
                                                              Properties.IsSanasEnabled == 'True' and 
                                                              Properties.IsSanasMicrophoneMuted == 'False'),
                                    ActiveUsersCount = dcount(UserAuthenticatedId) by bin(TimeGenerated, 1{(isDurationInHours ? "h" : "d")})
                        | project DateTimeUtc = TimeGenerated, ActiveUsersCount, EngagedUsersCount
                        | order by DateTimeUtc asc  
                        | project DateTimeUtc, ActiveUsersCount, EngagedUsersCount";

        var activities = await telemetryReader.ReadResultSetAsync<AgentActivityCount>(query);
        return activities.ToList();
    }

    public async Task<AgentActivityReport> GetAgentActityReportAsync(int workspaceId, DurationTypes durationType)
    {
        AgentActivityReport report;

        var workspace = await _workspaceRepo.GetByIdAsync(workspaceId);

        if (durationType is DurationTypes.TwelveHours or DurationTypes.OneDay)
        {
            var duration = durationType == DurationTypes.TwelveHours ? 12 : 24;
            report = await GetAgentActivityByHoursAsync(workspace.WorkspaceKey, duration);
        }
        else
        {
            var duration = durationType == DurationTypes.OneWeek ? 7 : 30;
            report = await GetAgentActivityByDaysAsync(workspace.WorkspaceKey, duration);
        }

        report.TotalLicenseCount = workspace.AllocatedSeats;
        report.UsedLicenseCount = await _workspaceRepo.GetAgentsCountAsync(workspaceId);
        report.LoggedInUsersCount = await _workspaceRepo.GetLoggedInAgentsCountAsync(workspaceId);

        return report;
    }

    private async Task<AgentActivityReport> GetAgentActivityByHoursAsync(string workspaceKey, int duration)
    {
        var totalDuration = (duration + 1) * 2;
        var activities = await GetAgentActivityCountsAsync(workspaceKey, totalDuration, isDurationInHours: true);

        var startAtUtc = DateTime.UtcNow.Subtract(TimeSpan.FromHours(totalDuration - 1));

        var list = new List<AgentActivityCount>();

        for (int i = 0; i < totalDuration; i++)
        {
            var atUtc = startAtUtc.AddHours(i);
            var activityCountAt = activities.FirstOrDefault(a => a.DateTimeUtc.Year == atUtc.Year && a.DateTimeUtc.Month == atUtc.Month && a.DateTimeUtc.Day == atUtc.Day && a.DateTimeUtc.Hour == atUtc.Hour);

            var activeCount = activityCountAt is null ? 0 : activityCountAt.ActiveUsersCount;
            var engagedCount = activityCountAt is null ? 0 : activityCountAt.EngagedUsersCount;

            list.Add(new AgentActivityCount(atUtc.AddHours(1), activeCount, engagedCount));
        }

        var prev = list[0..duration];
        var cur = list[(duration + 1)..^1];

        return new AgentActivityReport
        {
            AgentsEngageRates = cur,
            CurrentPeriodEngageCount = cur.Sum(a => a.ActiveUsersCount),
            PreviousPeriodEngageCount = prev.Sum(a => a.ActiveUsersCount)
        };
    }

    private async Task<AgentActivityReport> GetAgentActivityByDaysAsync(string workspaceKey, int duration)
    {
        var totalDuration = duration * 2;
        var activities = await GetAgentActivityCountsAsync(workspaceKey, totalDuration, isDurationInHours: false);

        var startAtUtc = DateTime.UtcNow.Date.Subtract(TimeSpan.FromDays(totalDuration - 1));

        var list = new List<AgentActivityCount>();

        for (int i = 0; i < totalDuration; i++)
        {
            var atUtc = startAtUtc.AddDays(i);
            var activityCountAt = activities.FirstOrDefault(a => a.DateTimeUtc.Year == atUtc.Year && a.DateTimeUtc.Month == atUtc.Month && a.DateTimeUtc.Day == atUtc.Day);

            var activeCount = activityCountAt is null ? 0 : activityCountAt.ActiveUsersCount;
            var engagedCount = activityCountAt is null ? 0 : activityCountAt.EngagedUsersCount;

            list.Add(new AgentActivityCount(atUtc, activeCount, engagedCount));
        }

        var prev = list[..duration];
        var cur = list[duration..];

        return new AgentActivityReport
        {
            AgentsEngageRates = cur,
            CurrentPeriodEngageCount = cur.Sum(a => a.ActiveUsersCount),
            PreviousPeriodEngageCount = prev.Sum(a => a.ActiveUsersCount)
        };
    }

    public async Task<Dto.NCWorkspace> GetNCWorkspaceAsync(int workspaceId, int userId, bool forLogin)
    {
        if (forLogin)
        {
            await _portalUserRepo.UpdateLoginCountAsync(workspaceId, userId);
        }

        var record = await _workspaceRepo.GetNCWorkspaceInfoAsync(workspaceId);

        return Dto.NCWorkspace.FromModel(record);
    }

    public async Task<InstallerCheckDto> ValidateInstallerIdAsync(Guid installerId)
    {
        var workspace = await _workspaceRepo.GetByInstallerId(installerId);

        if (workspace == null)
        {
            return new();
        }

        return new InstallerCheckDto()
        {
            ActivationMechanism = ActivationModeConverter.GetActivationMechanismName(workspace.ActivationMode),
            IsInstallerIdValid = true,
            ModelType = workspace.ModelTypes.FirstOrDefault(),
            WorkspaceKey = workspace.WorkspaceKey,
        };
    }
}


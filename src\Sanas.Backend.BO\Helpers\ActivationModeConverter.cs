namespace Sanas.Backend.BO.Helpers;

using Sanas.Backend.Data.Models;
public static class ActivationModeConverter
{
    public static ActivationMechanismName GetActivationMechanismName(ActivationMode? activationMode)
    {
        return activationMode switch
        {
            ActivationMode.UAK => ActivationMechanismName.UAK,
            ActivationMode.AA => ActivationMechanismName.AA,
            _ => ActivationMechanismName.UAK // Default to UAK for invalid values
        };
    }

    public static ActivationMode GetActivationMode(ActivationMechanismName? activationMechanismName)
    {
        return activationMechanismName switch
        {
            ActivationMechanismName.UAK => ActivationMode.UAK,
            ActivationMechanismName.AA => ActivationMode.AA,
            _ => ActivationMode.UAK // Default to UAK (0) for invalid values
        };
    }
}

﻿using System.IdentityModel.Tokens.Jwt;
using System.Reflection;
using System.Security.Claims;
using System.Security.Cryptography;
using System.Text;
using System.Text.Json;
using Microsoft.IdentityModel.Tokens;
using Sanas.Backend.BO.Dto;

namespace Sanas.Backend.BO.Helpers;

public static class Crypto
{
    private const string TokenIssuer = "sanas.ai";
    private const string TokenAudience = "sanas.accent.translator";

    private static readonly JsonSerializerOptions _serializerOptions = new()
    {
        PropertyNamingPolicy = JsonNamingPolicy.CamelCase,
        WriteIndented = false
    };

    private static RSA _rsa = RSA.Create();

    /// <summary>
    /// Static initializer set in Startup.cs
    /// Use SecretManager.cs when ready
    /// </summary>
    /// <param name="privateKey">Private key to sign responses to Sanas Accent Translator</param>
    public static void Initialize(byte[] privateKey)
    {
        _rsa?.Dispose();

        _rsa = RSA.Create();
        _rsa.ImportPkcs8PrivateKey(privateKey, out _);
    }

    /// <summary>
    /// Static method to generate JWT tokens for API requests from Sanas Accent Translator
    /// </summary>
    /// <param name="agent">Agent</param>
    /// <param name="validityDuration">Timespan for token validity</param>
    /// <param name="now">current DateTime in utc</param>
    /// <returns></returns>
    public static string GenerateTokenFromPrivateKey(AgentAuthDto agent, TimeSpan validityDuration, DateTime now)
    {
        var securityKey = new RsaSecurityKey(_rsa);
        var credentials = new SigningCredentials(securityKey, SecurityAlgorithms.RsaSha256);
        var handler = new JwtSecurityTokenHandler();
        var descriptor = new SecurityTokenDescriptor
        {
            Subject = new ClaimsIdentity(new[]
            {
                new Claim(ClaimTypes.NameIdentifier, agent.LoginId.ToString()),
                new Claim(ClaimTypes.Name, agent.AgentName),
            }),

            Expires = now.Add(validityDuration),
            SigningCredentials = credentials,
            IssuedAt = now,
            Issuer = TokenIssuer,
            Audience = TokenAudience
        };

        var token = handler.CreateToken(descriptor);

        return handler.WriteToken(token);
    }

    /// <summary>
    /// Validates JWT tokens recieved from Sanas Accent Translator
    /// </summary>
    /// <param name="token">string Access or refresh token</param>
    /// <param name="verifiedLoginId">Verified LoginId of the agent</param>
    /// <returns></returns>
    public static bool ValidateToken(string token, out string verifiedLoginId)
    {
        var validationParameters = new TokenValidationParameters
        {
            ValidateIssuerSigningKey = true,
            IssuerSigningKey = new RsaSecurityKey(_rsa),
            ValidateIssuer = true,
            ValidIssuer = TokenIssuer,
            ValidateAudience = true,
            ValidAudience = TokenAudience,
            ValidateLifetime = true,
            ClockSkew = TimeSpan.Zero
        };

        var handler = new JwtSecurityTokenHandler();
        try
        {
            var principal = handler.ValidateToken(token, validationParameters, out SecurityToken validatedToken);

            // Token is valid
            var agentLoginIdClaim = principal.FindFirst(ClaimTypes.NameIdentifier)?.Value;
            verifiedLoginId = agentLoginIdClaim;
            return true;
        }
        catch
        {
            verifiedLoginId = string.Empty;
            return false;
        }
    }

    /// <summary>
    /// Static method to hash plain-text with a unique salt
    /// </summary>
    /// <param name="plaintext">string to be hashed</param>
    /// <param name="salt">unique salt to concatenate before hashing</param>
    /// <returns></returns>
    public static string GetHash(string plaintext, string salt)
    {
        var bytes = Encoding.UTF8.GetBytes(string.Concat(plaintext, salt));
        var hashedBytes = SHA256.HashData(bytes);

        return Convert.ToBase64String(hashedBytes);
    }

    /// <summary>
    /// Generic static method that hashes T data with the server's private key
    /// Used to sign responses to Sanas Accent Translator.
    /// </summary>
    /// <typeparam name="T">Generic type</typeparam>
    /// <param name="data">data to be signed</param>
    /// <returns></returns>
    public static string SignWithPrivateKey<T>(T data)
        where T : class
    {
        var dataString = SerializeInOrder(data);
        var dataBytes = Encoding.UTF8.GetBytes(dataString);
        var signatureBytes = _rsa.SignData(dataBytes, HashAlgorithmName.SHA256, RSASignaturePadding.Pkcs1);

        return Convert.ToBase64String(signatureBytes);
    }

    /// <summary>
    /// <para>
    /// Static method to serialize data in alphabetical order
    /// This is required to ensure signature validation on Sanas Accent Translator works as expected
    /// </para>
    /// </summary>
    /// <typeparam name="T">Generic type</typeparam>
    /// <param name="data">data to be stringified</param>
    /// <returns></returns>
    private static string SerializeInOrder<T>(T data)
        where T : class
    {
        if (typeof(T) == typeof(string))
        {
            return data as string;
        }
        
        // Convert the object to a dictionary and order by property name
        var properties = typeof(T).GetProperties(BindingFlags.Instance | BindingFlags.Public)
            .OrderBy(p => p.Name)
            .ToDictionary(p => p.Name, p => p.GetValue(data));

        return JsonSerializer.Serialize(properties, _serializerOptions);
    }

    /// <summary>
    /// Dispose RSA when shutting down
    /// </summary>
    public static void Dispose()
    {
        _rsa?.Dispose();
        _rsa = null;
    }

    /// <summary>
    /// Internal method to verify access & refresh tokens for an agent request
    /// </summary>
    /// <param name="token">Access or refresh token</param>
    /// <param name="loginId">LoginId (claim) for the agent</param>
    /// <returns></returns>
    public static bool VerifyTokenAuthorization(string token, string loginId)
    {
        return ValidateToken(token.Trim(), out var verifiedLoginId) &&
            string.Equals(loginId, verifiedLoginId, StringComparison.InvariantCulture);
    }
}
﻿using DocumentFormat.OpenXml;
using DocumentFormat.OpenXml.Packaging;
using DocumentFormat.OpenXml.Spreadsheet;

namespace Sanas.Backend.BO.Helpers;

public static class ExcelGenerator
{
    public static byte[] GenerateExcel(string[] headers, string[][] rows)
    {
        byte[] result;

        using (var memoryStream = new MemoryStream())
        {
            using (var spreadsheetDocument = SpreadsheetDocument.Create(memoryStream, SpreadsheetDocumentType.Workbook))
            {
                WorkbookPart workbookPart = spreadsheetDocument.AddWorkbookPart();
                workbookPart.Workbook = new Workbook();

                WorksheetPart worksheetPart = workbookPart.AddNewPart<WorksheetPart>();
                worksheetPart.Worksheet = new Worksheet();

                WorkbookStylesPart stylesPart = workbookPart.AddNewPart<WorkbookStylesPart>();
                stylesPart.Stylesheet = GenerateStylesheet();
                stylesPart.Stylesheet.Save();

                worksheetPart.Worksheet.Append(GenerateColumns());

                Sheets sheets = spreadsheetDocument.WorkbookPart.Workbook.AppendChild(new Sheets());
                sheets.Append(new Sheet()
                {
                    Id = spreadsheetDocument.WorkbookPart.GetIdOfPart(worksheetPart),
                    SheetId = 1,
                    Name = "Users"
                });

                var sheetData = new SheetData();

                // Headers
                var headerRow = new Row()
                {
                    Height = 25,
                    CustomHeight = true,
                };

                foreach (var header in headers)
                {
                    Cell cell = ConstructCell(header, CellValues.String, 2);
                    headerRow.Append(cell);
                }

                sheetData.AppendChild(headerRow);

                // Output data to cells
                foreach (var rowData in rows)
                {
                    var row = new Row()
                    {
                        Height = 25,
                        CustomHeight = true,
                    };

                    foreach (var cellData in rowData)
                    {
                        Cell cell = ConstructCell(cellData, CellValues.String, 1);
                        row.Append(cell);
                    }

                    sheetData.AppendChild(row);
                }

                worksheetPart.Worksheet.Append(sheetData);
            }

            result = memoryStream.ToArray();
        }

        return result;
    }

    private static Columns GenerateColumns()
    {
        return new Columns(
            new Column // Id column
            {
                Min = 1,
                Max = 1,
                Width = 16,
                CustomWidth = true
            },
            new Column // Name and Birthday columns
            {
                Min = 2,
                Max = 2,
                Width = 30,
                CustomWidth = true
            },
            new Column // Salary column
            {
                Min = 3,
                Max = 3,
                Width = 16,
                CustomWidth = true
            },
            new Column // Salary column
            {
                Min = 4,
                Max = 6,
                Width = 24,
                CustomWidth = true
            },
            new Column // Salary column
            {
                Min = 7,
                Max = 10,
                Width = 16,
                CustomWidth = true
            });
    }

    private static Cell ConstructCell(string value, CellValues dataType, uint styleIndex = 0)
    {
        return new Cell()
        {
            CellValue = new CellValue(value),
            DataType = new EnumValue<CellValues>(dataType),
            StyleIndex = styleIndex
        };
    }

    private static Stylesheet GenerateStylesheet()
    {
        var fonts = GenerateFonts();
        var fills = GenerateFills();
        var borders = GenerateBorders();
        var cellFormats = GenerateCellFormats();

        return new Stylesheet(fonts, fills, borders, cellFormats);
    }

    private static CellFormats GenerateCellFormats()
    {
        var defaultFormat = new CellFormat(); // default
        var bodyFormat = new CellFormat { FontId = 2, FillId = 0, BorderId = 1, ApplyBorder = true }; // body
        var headerFormat = new CellFormat
        {
            FontId = 1,
            FillId = 2,
            BorderId = 1,
            ApplyFill = true,
            Alignment = new Alignment()
            {
                Horizontal = new EnumValue<HorizontalAlignmentValues>(HorizontalAlignmentValues.Justify),
                Vertical = new EnumValue<VerticalAlignmentValues>(VerticalAlignmentValues.Center),
            },
            ApplyAlignment = true,
        }; // header

        return new CellFormats(defaultFormat, bodyFormat, headerFormat);
    }

    private static Fonts GenerateFonts()
    {
        var defaultFont = new Font(new FontSize() { Val = 10 }, new FontName() { Val = "Calibri" }); // Index 0 - default
        var headerFont = new Font(new FontSize() { Val = 12 }, new Bold(), new Color() { Rgb = new HexBinaryValue { Value = "FFFFFF" } }, new FontName() { Val = "Calibri" }); // Index 1 - header
        var bodyFont = new Font(new FontSize() { Val = 12 }, new FontName() { Val = "Calibri" }); // Index 1 - header

        return new Fonts(defaultFont, headerFont, bodyFont);
    }

    private static Fills GenerateFills()
    {
        var defaultFill = new Fill(new PatternFill() { PatternType = PatternValues.None }); // Index 0 - default
        var defaultFill2 = new Fill(new PatternFill() { PatternType = PatternValues.Gray125 }); //Index 1 - default
        var headerFill = new Fill(new PatternFill() { PatternType = PatternValues.Solid, ForegroundColor = new ForegroundColor { Rgb = new HexBinaryValue { Value = "66666666" } } });

        return new Fills(defaultFill, defaultFill2, headerFill);
    }

    private static Borders GenerateBorders()
    {
        var defaultBorder = new Border(); // Index 0 - Default
        var sheetBorder = new Border( // index 1 black border
            new LeftBorder(new Color() { Auto = true }) { Style = BorderStyleValues.Hair },
            new RightBorder(new Color() { Auto = true }) { Style = BorderStyleValues.Hair },
            new TopBorder(new Color() { Auto = true }) { Style = BorderStyleValues.Hair },
            new BottomBorder(new Color() { Auto = true }) { Style = BorderStyleValues.Hair },
            new DiagonalBorder());

        return new Borders(defaultBorder, sheetBorder);
    }
}
﻿using System.Reflection;

namespace Sanas.Backend.BO.Helpers;

public static class Permissions
{
    public const string CreateWorkspace = "create:workspace";
    public const string UpdateWorkspace = "update:workspace";
    public const string ReadWorkspaces = "read:workspaces";
    public const string DeleteWorkspace = "delete:workspace";
    public const string ReadAllWorkspaces = "read:all-workspaces";
    public const string ReadWorkspaceReports = "read:workspace-reports";
    public const string CreateTeam = "create:team";
    public const string UpdateTeam = "update:team";
    public const string ReadTeams = "read:teams";
    public const string DeleteTeam = "delete:team";
    public const string ReadTeamReports = "read:team-reports";
    public const string CreateAppUser = "create:app-user";
    public const string UpdateAppUser = "update:app-user";
    public const string ReadAppUsers = "read:app-users";
    public const string DeleteAppUser = "delete:app-user";
    public const string CreatePortalUser = "create:portal-user";
    public const string UpdatePortalUser = "update:portal-user";
    public const string ReadPortalUsers = "read:portal-users";
    public const string DeletePortalUsers = "delete:portal-user";
    public const string CreateRoles = "create:roles";
    public const string UpdateRoles = "update:roles";
    public const string DeleteRoles = "delete:roles";
    public const string CreatePermissions = "create:permissions";
    public const string UpdatePermissions = "update:permissions";
    public const string DeletePermissions = "delete:permissions";
    public const string QueryConfigurations = "query:config";
    public const string MoveAppUsers = "move:app-users";
    public const string MoveAppUser = "move:app-user";

    public static IEnumerable<string> GetAll()
    {
        var constants = typeof(Permissions).GetFields(BindingFlags.Static | BindingFlags.Public);

        foreach (var constant in constants)
        {
            yield return constant.GetValue(null) as string;
        }
    }
}

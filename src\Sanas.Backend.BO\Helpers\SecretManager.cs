﻿using System.Text;
using Amazon.SecretsManager;
using Amazon.SecretsManager.Model;

namespace Sanas.Backend.BO.Helpers;

/// <summary>
/// Static class to pull secrets from AWS Key Management System
/// todo: Use this class to get secrets and deprecate the 'ResponseSettings: PrivateKey' in appSettings.Development.Json
/// </summary>
public static class SecretManager
{
    public static string GetSecret()
    {
        // todo: Configure AWS and change this below
        string secretName = "v2portal/AppSecret";
        string region = "us-west-2";

        var config = new AmazonSecretsManagerConfig 
        { 
            RegionEndpoint = Amazon.RegionEndpoint.GetBySystemName(region) 
        };

        var client = new AmazonSecretsManagerClient(config);

        var request = new GetSecretValueRequest
        {
            SecretId = secretName
        };

        GetSecretValueResponse response;
        try
        {
            response = client.GetSecretValueAsync(request).Result;
        }
        catch (Exception)
        {
            throw;
        }

        if (response == null)
        {
            return null;
        }

        if (response.SecretString != null)
        {
            return response.SecretString;
        }
        else
        {
            var memoryStream = response.SecretBinary;
            return Encoding.UTF8.GetString(memoryStream.ToArray());
        }
    }
}
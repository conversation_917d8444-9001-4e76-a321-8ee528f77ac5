namespace Sanas.Backend.BO.Helpers;

using Sanas.Backend.Data.Models;

public static class SysUserProfileTypeConverter
{
    public static SysUserProfileTypeName? GetSysUserProfileTypeName(SysUserProfileType? sysUserProfileType)
    {
        return sysUserProfileType switch
        {
            SysUserProfileType.LocalOrWorkgroup => SysUserProfileTypeName.LocalOrWorkgroup,
            SysUserProfileType.AD => SysUserProfileTypeName.AD,
            SysUserProfileType.AAD => SysUserProfileTypeName.AAD,
            _ => null // Default to null for invalid values
        };
    }

    public static SysUserProfileType? GetSysUserProfileType(object sysUserProfileTypeName)
    {
        if (sysUserProfileTypeName is SysUserProfileTypeName enumValue)
        {
            return enumValue switch
            {
                SysUserProfileTypeName.LocalOrWorkgroup => SysUserProfileType.LocalOrWorkgroup,
                SysUserProfileTypeName.AD => SysUserProfileType.AD,
                SysUserProfileTypeName.AAD => SysUserProfileType.AAD,
                _ => null
            };
        }
        else if (sysUserProfileTypeName is string stringValue)
        {
            if (Enum.TryParse<SysUserProfileTypeName>(stringValue, true, out var parsedEnum))
            {
                return GetSysUserProfileType(parsedEnum);
            }
        }

        return null;
    }
}

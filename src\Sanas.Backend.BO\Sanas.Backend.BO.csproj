﻿<Project Sdk="Microsoft.NET.Sdk">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <ImplicitUsings>enable</ImplicitUsings>
    <Nullable>disable</Nullable>
    <UserSecretsId>dbe4edf8-d0ba-4118-8742-5f57c53374aa</UserSecretsId>
    <DockerDefaultTargetOS>Linux</DockerDefaultTargetOS>
    <TreatWarningsAsErrors>True</TreatWarningsAsErrors>
    <CodeAnalysisRuleSet>..\Sanas.Portal.ruleset</CodeAnalysisRuleSet>
  </PropertyGroup>

  <ItemGroup>
    <ProjectReference Include="..\Sanas.Auth0Client\Sanas.Auth0Client.csproj" />
    <ProjectReference Include="..\Sanas.Backend.Data\Sanas.Backend.Data.csproj" />
    <PackageReference Include="DocumentFormat.OpenXml" Version="3.0.2" />
    <PackageReference Include="AWSSDK.S3" Version="3.7.307.28" />
    <PackageReference Include="AWSSDK.SecretsManager" Version="3.7.302.61" />
    <PackageReference Include="AWSSDK.SecurityToken" Version="3.7.300.87" />
    <PackageReference Include="CsvHelper" Version="32.0.1" />
    <PackageReference Include="Azure.Identity" Version="1.11.4" />
    <PackageReference Include="Azure.Monitor.Query" Version="1.3.1" />
    <PackageReference Include="Microsoft.Identity.Client" Version="4.61.3" />
    <PackageReference Include="StyleCop.Analyzers.Unstable" Version="1.2.0.556">
      <PrivateAssets>all</PrivateAssets>
      <IncludeAssets>runtime; build; native; contentfiles; analyzers; buildtransitive</IncludeAssets>
    </PackageReference>
  </ItemGroup>

</Project>

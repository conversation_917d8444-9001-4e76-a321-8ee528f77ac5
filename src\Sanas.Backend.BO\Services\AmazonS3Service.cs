﻿using Amazon.S3;
using Amazon.S3.Transfer;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Sanas.Backend.BO.Services.Interfaces;

namespace Sanas.Backend.BO.Services;

public class AmazonS3Service : IBlobService, IDisposable
{
    private readonly string _bucketName;
    private readonly AmazonS3Client _s3Client;

    private bool _disposedValue;

    public AmazonS3Service(ILogger<AmazonS3Service> logger, IConfiguration configuration)
    {
        try
        {
            _bucketName = configuration["S3Settings:FeedbackBucketName"];
            _s3Client = new(Amazon.RegionEndpoint.GetBySystemName(configuration["S3Settings:Region"]));
        }
        catch (Exception e)
        {
            logger.LogError("Failed to configure Amazon S3 bucket for Feedback. {Error}", e);
        }
    }

    public async Task<bool> UploadToBlobAsync(string keyName, Stream fileStream)
    {
        try
        {
            if (_s3Client is null)
            {
                return false;
            }

            var transferUtility = new TransferUtility(_s3Client);
            await transferUtility.UploadAsync(fileStream, _bucketName, keyName);

            return true;
        }
        catch (Exception)
        {
            throw;
        }
    }

    protected virtual void Dispose(bool disposing)
    {
        if (!_disposedValue)
        {
            if (disposing)
            {
                _s3Client.Dispose();
            }

            _disposedValue = true;
        }
    }

    public void Dispose()
    {
        // Do not change this code. Put cleanup code in 'Dispose(bool disposing)' method
        Dispose(disposing: true);
        GC.SuppressFinalize(this);
    }
}
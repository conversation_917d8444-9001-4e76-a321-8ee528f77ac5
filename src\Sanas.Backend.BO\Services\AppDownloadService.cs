﻿using Microsoft.Extensions.Logging;

namespace Sanas.Backend.BO.Services;

public class AppDownloadService(HttpClient httpClient, ILogger<AppDownloadService> logger) : IAppDownloadService
{
    public long GetInstallerSize(string fileUrl)
    {
        try
        {
            var response = httpClient.Send(new HttpRequestMessage(HttpMethod.Head, fileUrl));
            var header = response.Content.Headers.FirstOrDefault(h => h.Key == "Content-Length");

            if (header.Value is not null && header.Value.Any())
            {
                if (long.TryParse(header.Value.First(), out long fileSize))
                {
                    return fileSize;
                }
            }
        }
        catch (Exception e)
        {
            logger.LogError("Error in fetching size of app download file. {Error}", e);
        }

        return 0;
    }
}
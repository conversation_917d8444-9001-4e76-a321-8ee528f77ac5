﻿using Azure.Identity;
using Azure.Monitor.Query;
using Azure.Monitor.Query.Models;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Sanas.Backend.BO.Services;

public class AzureLogAnalyticsReader : ITelemetryReader
{
    private readonly ILogger<AzureLogAnalyticsReader> _logger;
    private readonly LogsQueryClient _logsQueryClient;
    private readonly string _workspaceId;

    public AzureLogAnalyticsReader(IConfiguration configuration, ILogger<AzureLogAnalyticsReader> logger)
    {
        var endpoint = configuration["AzLogAnalytics:ApiEndpoint"];
        var tenantId = configuration["AzLogAnalytics:TenantId"];
        var clientId = configuration["AzLogAnalytics:ClientId"];
        var clientSecret = configuration["AzLogAnalytics:ClientSecret"];
        var credentials = new ClientSecretCredential(tenantId, clientId, clientSecret);

        _logger = logger;
        _workspaceId = configuration["AzLogAnalytics:WorkspaceId"];
        _logsQueryClient = new LogsQueryClient(new Uri(endpoint), credentials);
    }

    public async Task<IEnumerable<T>> ReadResultSetAsync<T>(string query, DateTime? from, DateTime? to)
    {
        try
        {
            var queryTimeRange = from.HasValue ? new QueryTimeRange(from.Value, to ?? DateTime.UtcNow) : QueryTimeRange.All;
            var response = await _logsQueryClient.QueryWorkspaceAsync(_workspaceId, query, queryTimeRange);

            if (!response.HasValue || response.Value.Status != LogsQueryResultStatus.Success)
            {
                return default;
            }

            var properties = typeof(T).GetProperties();
            var returnResult = new List<T>();
            var columns = response.Value.Table.Columns.Select(c => c.Name).ToHashSet();

            foreach (var row in response.Value.Table.Rows)
            {
                if (typeof(T) == typeof(string) || typeof(T) == typeof(DateTimeOffset))
                {
                    returnResult.Add((T)row[0]);
                }
                else if (typeof(T) == typeof(int))
                {
                    var rowValue = row.GetInt32(0);
                    if (rowValue.HasValue)
                    {
                        returnResult.Add((T)Convert.ChangeType(rowValue.Value, typeof(T)));
                    }
                }
                else
                {
                    var instance = Activator.CreateInstance<T>();

                    foreach (var prop in properties)
                    {
                        if (columns.Contains(prop.Name))
                        {
                            if (row[prop.Name].GetType() == typeof(DateTimeOffset))
                            {
                                var date = (DateTimeOffset)row[prop.Name];

                                prop.SetValue(instance, ((DateTimeOffset)row[prop.Name]).UtcDateTime);
                            }
                            else if(row[prop.Name].GetType() == typeof(int))
                            {
                                prop.SetValue(instance, int.Parse(row[prop.Name].ToString()));
                            }
                            else if (row[prop.Name].GetType() == typeof(long))
                            {
                                prop.SetValue(instance, long.Parse(row[prop.Name].ToString()));
                            }
                            else
                            {
                                var typename = row[prop.Name].GetType().FullName;
                                prop.SetValue(instance, row[prop.Name].ToString());
                            }
                        }
                    }

                    returnResult.Add(instance);
                }
            }

            return returnResult;
        }
        catch (Exception e)
        {
            _logger.LogError("Error fetching telemetry. MethodName: {MethodName}, Error:{Error}", nameof(ReadResultSetAsync), e.ToString());
            throw;
        }
    }

    public async Task<T> ReadSingleResultAsync<T>(string query, DateTime? from, DateTime? to)
    {
        try
        {
            var returnResult = default(T);
            var queryTimeRange = from.HasValue ? new QueryTimeRange(from.Value, to ?? DateTime.UtcNow) : QueryTimeRange.All;
            var response = await _logsQueryClient.QueryWorkspaceAsync(_workspaceId, query, queryTimeRange);

            if (!response.HasValue || response.Value.Status != LogsQueryResultStatus.Success)
            {
                return default;
            }

            var properties = typeof(T).GetProperties();
            var columns = response.Value.Table.Columns.Select(c => c.Name).ToHashSet();

            if (response.Value.Table.Rows.Count > 0)
            {
                var row = response.Value.Table.Rows[0];
                returnResult = Activator.CreateInstance<T>();

                foreach (var prop in properties)
                {
                    if (columns.Contains(prop.Name))
                    {
                        prop.SetValue(returnResult, row[prop.Name].ToString());
                    }
                }
            }

            return returnResult;
        }
        catch (Exception e)
        {
            _logger.LogError("Error fetching telemetry. MethodName: {MethodName}, Error:{Error}", nameof(ReadSingleResultAsync), e.ToString());
            throw;
        }
    }

    public async Task<T> ReadScalarValue<T>(string query, DateTime? from, DateTime? to)
    {
        try
        {
            var returnValue = default(T);
            var queryTimeRange = from.HasValue ? new QueryTimeRange(from.Value, to ?? DateTime.UtcNow) : QueryTimeRange.All;
            var response = await _logsQueryClient.QueryWorkspaceAsync(_workspaceId, query, queryTimeRange);

            if (!response.HasValue || response.Value.Status != LogsQueryResultStatus.Success)
            {
                return default;
            }

            if (response.Value.Table.Rows.Count > 0 && response.Value.Table.Columns.Count > 0)
            {
                returnValue = (T)Convert.ChangeType(response.Value.Table.Rows[0][0], typeof(T));
            }

            return returnValue;
        }
        catch (Exception e)
        {
            _logger.LogError("Error fetching telemetry. MethodName: {MethodName}, Error:{Error}", nameof(ReadScalarValue), e.ToString());
            throw;
        }
    }
}

﻿using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Sanas.Backend.BO.Services.Interfaces;

namespace Sanas.Backend.BO.Services;

public class BnsService : IBnsService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<BnsService> _logger;
    private readonly IConfiguration _configuration;

    public BnsService(HttpClient httpClient, IConfiguration configuration, ILogger<BnsService> logger)
    {
        _configuration = configuration;
        _httpClient = httpClient;
        _logger = logger;
        ConfigureHttpClient();
    }

    private void ConfigureHttpClient()
    {
        var baseUrl = _configuration["Bns:BaseUrl"];
        _httpClient.BaseAddress = new Uri(baseUrl!);
    }

    public async Task<HttpResponseMessage> PostToBnsAsync(string endpoint, JsonElement request)
    {
        _logger.LogInformation("Calling BNS service at - {Endpoint}", endpoint);

        return await _httpClient.PostAsJsonAsync("/bns/" + endpoint, request);
    }

    public async Task<HttpResponseMessage> GetAgentUsageReportAsync(int workspaceId, StringBuilder queryParams, bool download)
    {
        _logger.LogInformation("Getting agent usage report");

        var apiPath = $"/api/Workspaces/{workspaceId}/Agents/usage-reports{(download ? "/download" : null)}?" + queryParams.ToString();
        return await _httpClient.GetAsync(apiPath);
    }
}
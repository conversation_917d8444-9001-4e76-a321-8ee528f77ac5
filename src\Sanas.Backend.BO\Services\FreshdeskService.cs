﻿using System.Net.Http.Headers;
using System.Text;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Sanas.Backend.BO.Dto.Feedback.SystemSummary;
using Sanas.Backend.BO.Services.Interfaces;
using Sanas.Backend.Data;
using Sanas.Backend.Data.Models;

namespace Sanas.Backend.BO.Services;

public class FreshdeskService : IFreshdeskService
{
    private readonly ILogger<FreshdeskService> _logger;
    private readonly HttpClient _freshDeskHttpClient;
    private readonly string _freshDeskApiPath;
    private readonly bool  _freshDeskEnabled;
    public FreshdeskService(ILogger<FreshdeskService> logger, HttpClient httpClient, IConfiguration configuration)
    {
        _freshDeskHttpClient = httpClient;
        _logger = logger;
        _freshDeskApiPath = configuration["FreshdeskSettings:ApiPath"];
        var apiKey = configuration["FreshdeskSettings:ApiKey"];
        var apiEndPoint = configuration["FreshdeskSettings:Endpoint"];
        _freshDeskEnabled = configuration["FreshdeskSettings:Enabled"] == "1"; 
        ConfigureHttpClient(apiKey, apiEndPoint);
        _logger.LogInformation($"Freshdesk ticket creation: {_freshDeskEnabled}");
    }

    public bool IsEnabled()
    {
        return _freshDeskEnabled;
    }

    private void ConfigureHttpClient(string apikey, string endpoint)
    {
        var authToken = Convert.ToBase64String(Encoding.ASCII.GetBytes(apikey + ":X"));
        _freshDeskHttpClient.BaseAddress = new Uri(endpoint);
        _freshDeskHttpClient.Timeout = TimeSpan.FromSeconds(15);
        _freshDeskHttpClient.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        _freshDeskHttpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("Basic", authToken);
    }

    public async Task SubmitTicketAsync(FreshdeskTicketDto ticket)
    {
        try
        {
            await using var stream = new MemoryStream(ticket.Attachment);
            using var form = new MultipartFormDataContent
            {
                { new StreamContent(stream), "attachments[]", $"{ticket.AttachmentName}.zip" },
                { new StringContent(ticket.Subject), "\"subject\"" },
                { new StringContent(ticket.Description), "\"description\"" },
                { new StringContent(ticket.CompanyName), "custom_fields[cf_client_name]" },
                { new StringContent(ticket.RequestorName), "custom_fields[cf_requester_name]" },
                { new StringContent(ticket.ApplicationVersion), "custom_fields[cf_application_version]" },
                { new StringContent(ticket.SanasUniqueId), "custom_fields[cf_sanas_unique_id]" },
                { new StringContent(ticket.SentimentType), "custom_fields[cf_sentiment_type]" },
                
                // TODO: Reference number expects an int, this needs to be changed on Freshdesk
                // { new StringContent(ticket.AttachmentName), "custom_fields[cf_reference_number]" },
                { new StringContent(ticket.WorkspaceKey), "custom_fields[cf_workspace_key]" },
                { new StringContent(ticket.ProductType), "custom_fields[cf_product_type]" },
                { new StringContent(ticket.ReporterEmail), "\"email\"" },
                { new StringContent(ticket.Priority), "\"priority\"" },
                { new StringContent(ticket.Status), "\"status\"" },
                { new StringContent(ticket.IssueType), "\"type\"" },
                { new StringContent(ticket.LineOfBusiness), "custom_fields[cf_lob]" },

                // These fields will not change
                // '9' is set to Portal in Freshdesk
                { new StringContent("9"), "\"source\"" },
                { new StringContent("V2"), "custom_fields[cf_app_generation]" }
            };

            var response = await _freshDeskHttpClient.PostAsync(_freshDeskApiPath, form);
            var responseMessage = await response.Content.ReadAsStringAsync();
            
            if (!response.IsSuccessStatusCode)
            {
                throw new InvalidOperationException($"Freshdesk request was not successful {response.StatusCode} :: {(!string.IsNullOrEmpty(responseMessage) ? responseMessage : response.ReasonPhrase)}");
            }
            else
            {
                // Freshdesk doesn't return the ticket number created. We can end here
                _logger.LogInformation($"FreshdeskService.CreateTicketAsync() Freshdesk ticket submitted for {ticket.SanasUniqueId}.");
            }
        }
        catch (Exception ex)
        {
            _logger.LogError($"FreshdeskService.CreateTicketAsync() Error in submitting freshdesk Ticket {ex.Message}");
        }
    }

    public FreshdeskTicketDto CreateTicket(Feedback feedback, SystemSummary systemSummary = null)
    {
        var ticket = new FreshdeskTicketDto();

        var descriptionBuilder = new StringBuilder();
        var sentimentString = feedback.SentimentType == 0 ? "Good" : "Bad";

        // Feedback Details (heading)
        descriptionBuilder.Append("<b>Feedback Details</b><br>");
        descriptionBuilder.Append($"User: {feedback.LoginId}<br>");
        descriptionBuilder.Append($"Workspace: {feedback.Workspace.WorkspaceKey}<br>");
        descriptionBuilder.Append($"Team: {feedback.Workspace.Teams.First().Name ?? "Default"}<br>");
        descriptionBuilder.Append($"Sentiment: {sentimentString}<br>");
        descriptionBuilder.Append($"Description: {feedback.Description}<br>");
        descriptionBuilder.Append($"App Version: {feedback.AppVersion}<br>");
        descriptionBuilder.Append($"Attachment: {feedback.AttachmentNumber}<br>");
        descriptionBuilder.Append("-------<br>");
        descriptionBuilder.Append("<br>");

        if (systemSummary != null)
        {
            var machineInformation = systemSummary.ComputerSystem.First();
            var opsInformation = systemSummary.OS.First();
            var cpuInformation = systemSummary.CPU.First();

            // System Information (heading)
            descriptionBuilder.AppendLine("<b>System Information</b><br>");
            descriptionBuilder.AppendLine($"MachineName: {machineInformation.MachineName}<br>");
            descriptionBuilder.AppendLine($"System Manufacturer: {machineInformation.ComputerManufacturer}<br>");
            descriptionBuilder.AppendLine($"System Model: {machineInformation.ComputerModel}<br>");
            descriptionBuilder.AppendLine($"CPU Manufacturer: {cpuInformation.CPUManufacturer}<br>");
            descriptionBuilder.AppendLine($"CPU Name: {cpuInformation.CPUName}<br>");
            descriptionBuilder.AppendLine($"No. of cores: {cpuInformation.NumberOfCores}<br>");
            descriptionBuilder.AppendLine($"No. of processors: {machineInformation.NumberOfLogicalProcessors}<br>");
            
            long totalMemoryMB = machineInformation.TotalPhysicalMemory ?? 0 / (1024L * 1024);
            descriptionBuilder.AppendLine($"Total Memory: {totalMemoryMB} MB<br>");
            descriptionBuilder.Append("<br>");

            // OS Information (heading)
            descriptionBuilder.AppendLine("<b>OS Information</b><br>");
            descriptionBuilder.AppendLine($"OS Manufacturer: {opsInformation.OSManufacturer}<br>");
            descriptionBuilder.AppendLine($"OS Name: {opsInformation.OSName}<br>");
            descriptionBuilder.AppendLine($"OS Architecture: {opsInformation.OSArchitecture}<br>");
            descriptionBuilder.AppendLine($"OS Version: {opsInformation.OSVersion}<br>");
        }

        ticket.Subject = $"Feedback: {feedback.Description}";
        ticket.Description = descriptionBuilder.ToString();
        ticket.ApplicationVersion = feedback.AppVersion;
        ticket.CompanyName = feedback.Workspace.Enterprise;
        ticket.RequestorName = feedback.LoginId;
        ticket.WorkspaceKey = feedback.Workspace.WorkspaceKey;
        ticket.SanasUniqueId = feedback.SanasUniqueId;
        ticket.SentimentType = sentimentString;
        ticket.AttachmentName = feedback.AttachmentNumber;

        return ticket;
    }
}
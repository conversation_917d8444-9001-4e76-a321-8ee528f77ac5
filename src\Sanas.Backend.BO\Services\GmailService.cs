﻿using System.ComponentModel;
using System.Net;
using System.Net.Mail;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Sanas.Backend.BO.Services;

public class GmailService : IEmailService
{
    private readonly string _host;
    private readonly int _port;
    private readonly string _email;
    private readonly string _password;

    private readonly ILogger _logger;

    public GmailService(IConfiguration configuration, ILogger<GmailService> logger)
    {
        _logger = logger;
        _port = 587;
        _host = configuration["SmtpSettings:Host"] ?? "smtp.gmail.com";
        _email = configuration["SmtpSettings:Email"] ?? "<EMAIL>";
        _password = configuration["SmtpSettings:Password"];

        if (int.TryParse(configuration["SmtpSettings:Port"], out int port))
        {
            _port = port;
        }
    }

    public void SendEmail(MailMessage message)
    {
        try
        {
            _logger.LogInformation("Sending email from {From} to {To}", _email, message.To);

            if (message is null)
            {
                _logger.LogWarning("No email sent. NULL message");
                return;
            }

            using var client = new SmtpClient();
            client.SendCompleted += OnEmailSent;
            client.Host = _host;
            client.Port = _port;
            client.DeliveryMethod = SmtpDeliveryMethod.Network;
            client.UseDefaultCredentials = false;
            client.EnableSsl = true;
            client.Credentials = new NetworkCredential(_email, _password);
            client.Send(message);

            _logger.LogInformation("Successfully sent the email");
        }
        catch (Exception e)
        {
            _logger.LogError("Failed to send email. {Error}", e);
        }
    }

    private void OnEmailSent(object sender, AsyncCompletedEventArgs e)
    {
        if (e.Cancelled)
        {
            _logger.LogInformation("Email sending cancelled");
        }
        else if (e.Error is not null)
        {
            _logger.LogError("Failed to send email. {Error}", e.Error);
        }
        else
        {
            _logger.LogInformation("Successfully sent the email");
        }
    }
}

﻿using System.Net.Http.Headers;
using System.Net.Http.Json;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Sanas.Backend.BO.Dto;

namespace Sanas.Backend.BO.Services.Interfaces;

public class FcmPushNotificationService : IPushNotificationService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<FcmPushNotificationService> _logger;
    private readonly IConfiguration _configuration;

    public FcmPushNotificationService(HttpClient httpClient, IConfiguration configuration, ILogger<FcmPushNotificationService> logger)
    {
        _configuration = configuration;
        _httpClient = httpClient;
        _logger = logger;
        ConfigureHttpClient();
    }

    private void ConfigureHttpClient()
    {
        var serverKey = _configuration["Fcm:AuthKey"];
        _httpClient.BaseAddress = new Uri("https://fcm.googleapis.com");
        _httpClient.DefaultRequestHeaders.Authorization = new AuthenticationHeaderValue("key", $"={serverKey}");
    }

    public async Task SendNotificationAsync(IEnumerable<PortalUserDto> recipientUsers, string title, string description)
    {
        _logger.LogInformation("Sending push notifications...");

        if (recipientUsers is null || !recipientUsers.Any())
        {
            _logger.LogInformation("No notification sent. Reason: No reciepients found.");
            return;
        }

        var content = new Dictionary<string, string>()
        {
            { "title", title },
            { "body", description },
        };

        var toList = recipientUsers.Where(u => !string.IsNullOrWhiteSpace(u.NotificationToken))
            .Select(u => u.NotificationToken).ToArray();

        if (toList.Length > 0)
        {
            try
            {
                var message = new
                {
                    registration_ids = toList,
                    data = content
                };

                var response = await _httpClient.PostAsJsonAsync("/fcm/send", message, CancellationToken.None);

                if (response.IsSuccessStatusCode)
                {
                    _logger.LogInformation("Push notification sent successfully. {Reason}:{Response}", response.ReasonPhrase, response.Content.ReadAsStringAsync().Result);
                }
                else
                {
                    _logger.LogError("Error sending push notification. {Reason}:{Response}", response.ReasonPhrase, response.Content.ReadAsStringAsync().Result);                    
                }
            }
            catch (Exception ex)
            {
                _logger.LogError("An exception occurred while sending push notification. {Error}", ex);
            }
        }
    }
}

﻿namespace Sanas.Backend.BO.Services;

public interface IUserInviterService
{
    public void SendAccountDeactivationMaessage(string email);
    
    public void SendLicenseAdjustMessage(IEnumerable<string> emails, int previousLicenses, int adjustedLicenses);

    public void SendWorkspaceInvitationToNewUser(string email, string workspaceUrl);

    public void SendWorkspaceInvitationToExistingUser(string email, string workspaceUrl, string workspaceName);
}
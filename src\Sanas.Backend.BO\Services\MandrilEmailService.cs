﻿using System.Net.Http.Json;
using System.Net.Mail;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;

namespace Sanas.Backend.BO.Services;

public class MandrilEmailService : IEmailService
{
    private readonly ILogger<MandrilEmailService> _logger;
    private readonly HttpClient _httpClient;
    private readonly IConfiguration _configuration;

    public MandrilEmailService(ILogger<MandrilEmailService> logger, HttpClient httpClient, IConfiguration configuration)
    {
        _configuration = configuration;
        _logger = logger;
        _httpClient = httpClient;
        ConfigureHttpClient();
    }

    private void ConfigureHttpClient()
    {
        var mandrilUrl = _configuration["mandrilConfig:Url"] ?? "https://mandrillapp.com/";
        _httpClient.BaseAddress = new Uri(mandrilUrl);
    }

    public async void SendEmail(MailMessage message)
    {
        try
        {
            var toAddresses = message.To.Select(to => new { email = to.Address, type = "to" });

            var madrilApiKey = _configuration["mandrilConfig:ApiKey"] ?? "md-NA5Ze0lIJISoLCNlC5fh6g";

            var response = await _httpClient.PostAsJsonAsync<dynamic>("api/1.0/messages/send.json", new
            {
                key = madrilApiKey,
                message = new
                {
                    from_name = message.From.DisplayName,
                    from_email = message.From.Address,
                    subject = message.Subject,
                    html = message.Body,
                    to = toAddresses
                }
            });

            if (!response.IsSuccessStatusCode)
            {
                var error = await response.Content.ReadAsStringAsync();
                _logger.LogError("Failed to send email: {Error}", error);
            }
        }
        catch (Exception e)
        {
            _logger.LogError("Failed to send email: {Error}", e);
        }
    }
}

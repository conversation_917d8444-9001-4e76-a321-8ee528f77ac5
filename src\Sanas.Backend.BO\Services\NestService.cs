﻿using System.Net.Http.Json;
using System.Text;
using System.Text.Json;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.Logging;
using Sanas.Backend.BO.Services.Interfaces;

namespace Sanas.Backend.BO.Services;

public class NestService : INestService
{
    private readonly HttpClient _httpClient;
    private readonly ILogger<BnsService> _logger;
    private readonly IConfiguration _configuration;

    public NestService(HttpClient httpClient, IConfiguration configuration, ILogger<BnsService> logger)
    {
        _configuration = configuration;
        _httpClient = httpClient;
        _logger = logger;
        ConfigureHttpClient();
    }

    private void ConfigureHttpClient()
    {
        var baseUrl = _configuration["Bns:BaseUrl"];
        _httpClient.BaseAddress = new Uri(baseUrl!);
    }

    public async Task<HttpResponseMessage> PostToNestServerAsync(string endpoint, JsonElement request)
    {
        _logger.LogInformation("[POST] to Nest service - {Endpoint}", endpoint);
        return await _httpClient.PostAsJsonAsync(endpoint, request);
    }

    public async Task<HttpResponseMessage> GetFromNestServerAsync(string endpoint)
    {
        _logger.LogInformation("[GET] download from Nest service - {Endpoint}", endpoint);
        return await _httpClient.GetAsync(endpoint);
    }
}
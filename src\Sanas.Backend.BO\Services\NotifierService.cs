﻿using System.Net.Mail;
using Microsoft.Extensions.Logging;
using Sanas.Backend.BO.Dto;

namespace Sanas.Backend.BO.Services;

public class NotifierService(ILogger<NotifierService> logger, IEmailService emailService, IPushNotificationService pushNotificationService)
{
    // TODO - We need to replace these hardcoded values once we get new email domain from Chandan.
    public async void NotifyByEmailAsync(IEnumerable<PortalUserDto> recipientUsers, string title, string description)
    {
        try
        {
            await Task.CompletedTask;
            var message = new MailMessage
            {
                From = new MailAddress("<EMAIL>", "Sanas support"),
                Subject = title,
                Body = description
            };
            recipientUsers.Select(user => user.Email).ToList().ForEach(message.To.Add);
            emailService.SendEmail(message);
        }
        catch (Exception e)
        {
            logger.LogError("Failed to notification email. {Error}", e);
        }
    }

    public async void SendPushNotificationAsync(IEnumerable<PortalUserDto> recipientUsers, string title, string description)
    {
        try
        {
            await pushNotificationService.SendNotificationAsync(recipientUsers, title, description);
        }
        catch (Exception e)
        {
            logger.LogError("Failed to send push notification. {Error}", e);
        }
    }
}
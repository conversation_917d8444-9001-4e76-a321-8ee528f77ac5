﻿using System.Net.Mail;
using Microsoft.Extensions.Logging;

namespace Sanas.Backend.BO.Services;

public class UserInviterService(ILogger<UserInviterService> logger, IEmailService emailService) : IUserInviterService
{
    private readonly MailAddress _fromAddress = new("<EMAIL>", "Sanas Notification");

    public async void SendWorkspaceInvitationToNewUser(string email, string workspaceUrl)
    {
        try
        {
            await Task.CompletedTask;

            var toAddress = new MailAddress(email);

            using MailMessage message = GetNewUserInviteMessage(_fromAddress, toAddress, workspaceUrl);

            emailService.SendEmail(message);
        }
        catch (Exception e)
        {
            logger.LogError("Failed to send initation email to new user. {Error}", e);
        }
    }

    public async void SendWorkspaceInvitationToExistingUser(string email, string workspaceUrl, string workspaceName)
    {
        try
        {
            var toAddress = new MailAddress(email);

            using MailMessage message = GetExistingUserInviteMessage(_fromAddress, toAddress, workspaceUrl, workspaceName);

            emailService.SendEmail(message);

            await Task.CompletedTask;
        }
        catch (Exception e)
        {
            logger.LogError("Failed to send workspace initation email to existing user. {Error}", e);
        }
    }

    public async void SendLicenseAdjustMessage(IEnumerable<string> emails, int previousLicenses, int adjustedLicenses)
    {
        try
        {
            if (emails.Any())
            {
                var toAddress = new MailAddress(emails.FirstOrDefault());

                using MailMessage message = GetLicenseCountChangeNotifyMessage(_fromAddress, toAddress, previousLicenses, adjustedLicenses);

                foreach (var email in emails)
                {
                    message.To.Add(email);
                }

                emailService.SendEmail(message);

                await Task.CompletedTask;
            }
        }
        catch(Exception e)
        {
            logger.LogError("Failed to send license adjustment email. {Error}", e);
        }
    }

    public async void SendAccountDeactivationMaessage(string email)
    {
        try
        {
            var toAddress = new MailAddress(email);

            using MailMessage message = GetAccountDeactivationNotifyMessage(_fromAddress, toAddress);

            emailService.SendEmail(message);

            await Task.CompletedTask;
        }
        catch (Exception e)
        {
            logger.LogError("Failed to send account deactivation email. {Error}", e);
        }
    }

    private static MailMessage GetNewUserInviteMessage(MailAddress from, MailAddress to, string invitationUrl)
    {
        var content = $"""                        
                        <p>Dear User,</p>
                        <p>Welcome to Sanas Portal!</p>
                        <p>We are delighted to have you on board. Your exclusive workspace is now ready, and you can access your account, manage teams and agents, and do much more from our new portal.</p>
                        <p><b>Your username: </b>{to}</p>
                        <p>Please click the button below to accept the invitation and set your password.</p>
                        <br/><a class="btn" href="{invitationUrl}">Accept Invitation</a><br/><br/>
                        <p>Please note, this invitation link expires in 7 days.</p>
                        <p>If you have any questions or need further assistance, reach <NAME_EMAIL></p>
                        <p>Welcome Aboard!<br/>                        
                           Team Sanas
                        </p>
                     """;

        var message = new MailMessage(from, to)
        {
            Subject = "Welcome to Sanas Portal!",
            IsBodyHtml = true,
            Body = GetEmailTemplate().Replace("[[[email-content]]]", content, StringComparison.OrdinalIgnoreCase)
        };

        return message;
    }

    private static MailMessage GetInvitationReminderMessage(MailAddress from, MailAddress to, string invitationUrl)
    {
        var message = new MailMessage(from, to)
        {
            Subject = "Your Workspace Invitation on Sanas will expire in 3 days",
            IsBodyHtml = true,
            Body = $@"
                        <html>
                            <head>
                                <style>
                                    .button, .button:link, .button:visited, .button:active {{
                                        background-color: #04AA6D;
                                        border: none;
                                        border-radius: 5px;
                                        color: white !important;
                                        padding: 8px 12px;
                                        text-align: center;
                                        text-decoration: none;
                                        display: inline-block;
                                        cursor: pointer;
                                    }}
                                </style>
                            </head>
                                <body>
                                    <p>Hi {to},</p>
                                    <p>Just a friendly reminder that the password link to your workspace will automatically <b>expire in next 3 days</b>. Click on the button below to set your password and sign in to your workspace.</p>                                        
                                    <a class=""button"" href=""{invitationUrl}"">Accept Invitation</a>
                                    <p> Please avoid sharing this email with anyone else as it could potentially lead to unauthorized access and misuse of your account.</p>
                                    <p> If you have any questions or are facing any issues with login, please <a href = ""https://support.sanas.ai""> click here </a> to connect with our support team.</p>
                                    <br>
                                    <p>Best Regards,</p>
                                    <span style=""margin=0px""> — Sanas Team </span>
                                </body>
                        </html> "
        };
        return message;
    }

    private static MailMessage GetExistingUserInviteMessage(MailAddress from, MailAddress to, string workspaceUrl, string workspaceName)
    {
        var content = $"""                        
                        <p>Dear User,</p>
                        <p>You have been added to a new workspace, “{workspaceName}”, on Sanas Portal. </p>
                        <p><b>Your username: </b>{to}</p>
                        <p>Please click the button below to login to Sanas and view your new workspace:</p>
                        <br/><a class="btn" href="{workspaceUrl}">View Workspace</a><br/><br/>
                        <p>Please note, this invitation link expires in 7 days.</p>
                        <p>If you have any questions or need further assistance, reach <NAME_EMAIL></p>
                        <p>Regards,<br/>                        
                           Team Sanas
                        </p>
                     """;

        var message = new MailMessage(from, to)
        {
            Subject = "Welcome to your new workspace",
            IsBodyHtml = true,
            Body = GetEmailTemplate().Replace("[[[email-content]]]", content, StringComparison.OrdinalIgnoreCase)
        };
        return message;
    }

    private static MailMessage GetLicenseCountChangeNotifyMessage(MailAddress from, MailAddress to, int previousLicenses, int adjustedLicenses)
    {
        var content = $"""
                        <p>Dear User,</p>
                        <p>We are writing to inform you of a recent update we’ve made to your allocated seat counts.</p>
                        <p><b>Details:</b></p>
                        <ul>
                            <li><b>Previous Number of Licenses:</b> {previousLicenses}</li>
                            <li><b>New Number of Licenses:</b> {adjustedLicenses}</li>
                        </ul>
                        <p>This change will take effect immediately.</p>
                        <p>If you have any questions or need further assistance, reach <NAME_EMAIL></p>
                        <p>Regards,<br/>                        
                          Team Sanas
                        </p>
                    """;

        var message = new MailMessage(from, to)
        {
            Subject = "Sanas seat count updated",
            IsBodyHtml = true,
            Body = GetEmailTemplate().Replace("[[[email-content]]]", content, StringComparison.OrdinalIgnoreCase)
        };
        return message;
    }

    public static MailMessage GetAccountDeactivationNotifyMessage(MailAddress from, MailAddress to)
    {
        var content = $"""
                <p>Dear User,</p>
                <p>We are writing to inform you that your account on the Sanas Portal has been deactivated. As a result, you will no longer have access to any workspaces, including account details management, team and agent oversight, and report viewing functionalities.</p>
                <p>If you have any questions or need further assistance, reach <NAME_EMAIL></p>
                <p>Thank you for choosing Sanas!</p>
                <p>Regards,<br/>                        
                  Team Sanas
                </p>
            """;

        var message = new MailMessage(from, to)
        {
            Subject = "Sanas account deactivated",
            IsBodyHtml = true,
            Body = GetEmailTemplate().Replace("[[[email-content]]]", content, StringComparison.OrdinalIgnoreCase)
        };
        return message;
    }

    private static string GetEmailTemplate()
    {
        return """
                        <!DOCTYPE html>
            <html lang="en">
            <head>
                <meta charset="UTF-8">
                <title>Invitation</title>
                <meta name="viewport" content="width=device-width, initial-scale=1"/>
                <style>
                    html, body {
                        background: #fafbfe;
                        font-family: Verdana, serif;
                        font-size: 16px;
                        margin: 0;
                    }

                    p {
                        color: rgba(0, 0, 0, 0.6);
                    }

                    strong {
                        color: rgba(0, 0, 0, 0.8);
                    }

                    .container {
                        width: 100%;
                        max-width: 720px;
                        margin: 0 auto;
                    }


                    .logo {
                        margin: 24px 0;
                        width: 100%;
                    }

                    .logo span {
                        font-size: 32px;
                        font-weight: bold;
                        line-height: 0;
                    }

                    .logo td {
                        padding: 10px;
                        width: 50%;
                    }

                    .logo img {
                        height: 32px;
                    }

                    .card {
                        background: white;
                        border: 1px solid rgba(64, 35, 135, 0.2);
                        border-radius: 24px;
                    }

                    .top-radius {
                        border-bottom-right-radius: 0;
                        border-bottom-left-radius: 0;
                        border-bottom: none;
                        margin-top: 24px;
                    }

                    .padding {
                        padding: 32px;
                    }

                    p {
                        line-height: 24px;
                    }

                    .text-small {
                        font-size: 12px;
                        line-height: 18px;
                    }

                    .link {
                        text-decoration: none !important;
                        color: #143FDA !important;
                    }

                    .btn {
                        text-decoration: none !important;
                        background: #143FDA !important;
                        padding: 12px 24px;
                        color: white !important;
                        border-radius: 8px;
                    }

                    .pv-12 {
                        padding-top: 24px;
                        padding-bottom: 24px;
                    }

                    .text-center {
                        text-align: center;
                    }

                    .copyright {
                        font-weight: 600;
                        margin: 0;
                    }

                </style>
            </head>
            <body>

            <div class="container">
                <table class="logo">
                    <tr>
                        <td style="text-align: right">
                            <img src="https://portal-v2-web-dev.sanas.ai/logo192.png"
                                 alt="logo"/>
                        </td>
                        <td style="text-align: left">
                            <span>sanas</span>
                        </td>
                    </tr>
                </table>
                <div class="card">
                    <div class="padding">
                        [[[email-content]]]
                    </div>
                </div>

                <div class="card top-radius">
                    <div class="padding text-center">
                        <p class="copyright">© sanas.ai 2024</p>
                        <p class="text-small">
                            This email was sent from a notification only address that cannot accept incoming emails.
                            <br/>Please do not reply to this message
                        </p>
                    </div>
                </div>
            </div>
            </body>
            </html>
            """;
    }
}

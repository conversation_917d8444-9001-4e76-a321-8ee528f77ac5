﻿namespace Sanas.Backend.Data;

public record AgentsQueryOptions : QueryOptions
{
    public AgentsQueryOptions(QueryOptions queryOptions)
    {
        SortBy = queryOptions.SortBy;
        Skip = queryOptions.Skip;
        SortBy = queryOptions.SortBy;
        SearchText = queryOptions.SearchText;
        LoadAll = queryOptions.LoadAll;
        Fetch = queryOptions.Fetch;
    }

    public IEnumerable<int> IcpStatuses { get; set; }

    public IEnumerable<string> AppStatuses { get; set; }

    public IEnumerable<string> LicenseStatuses { get; set; }

    public IEnumerable<string> Voices { get; set; }

    public IEnumerable<string> AppVersions { get; set; }
}

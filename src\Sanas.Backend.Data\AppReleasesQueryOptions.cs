﻿namespace Sanas.Backend.Data;

public record AppReleasesQueryOptions : QueryOptions
{
    public AppReleasesQueryOptions(QueryOptions queryOptions)
    {
        SortBy = queryOptions.SortBy;
        Skip = queryOptions.Skip;
        SortBy = queryOptions.SortBy;
        SearchText = queryOptions.SearchText;
        LoadAll = queryOptions.LoadAll;
        Fetch = queryOptions.Fetch;
    }

    public IList<int> ModelTypes { get; set; } = [];

    public string CountryCodes { get; set; }

    public DateTime? StartDate { get; set; }

    public DateTime? EndDate { get; set; }

    public IList<string> Versions { get; set; } = [];
}

﻿namespace Sanas.Backend.Data;

internal class AppVersionComparer : IComparer<string>
{
    public int Compare(string x, string y)
    {
        if (string.IsNullOrWhiteSpace(x) && string.IsNullOrWhiteSpace(y))
        {
            return 0;
        }

        if (string.IsNullOrWhiteSpace(x))
        {
            return -1;
        }

        if (string.IsNullOrWhiteSpace(y))
        {
            return 1;
        }

        var tokensX = x.Split('.', StringSplitOptions.RemoveEmptyEntries);
        var tokensY = y.Split('.', StringSplitOptions.RemoveEmptyEntries);

        for (var i = 0; i < Math.Max(tokensX.Length, tokensY.Length); i++)
        {
            var segmentX = 0;
            var segmentY = 0;

            if (tokensX.Length > i)
            {
                _ = int.TryParse(tokensX[i], out segmentX);
            }

            if (tokensY.Length > i)
            {
                _ = int.TryParse(tokensY[i], out segmentY);
            }

            if(segmentX != segmentY)
            {
                return segmentX.CompareTo(segmentY);
            }
        }

        return 0;
    }
}

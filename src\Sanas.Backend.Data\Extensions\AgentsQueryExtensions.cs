﻿using Microsoft.EntityFrameworkCore;
using Sanas.Backend.Data.Models;

namespace Sanas.Backend.Data.Extensions;

internal static class AgentsQueryExtensions
{
    public static IQueryable<Agent> Where(this IQueryable<Agent> query, string search = null)
    {
        if (string.IsNullOrWhiteSpace(search))
        {
            return query;
        }

        return query.Where(agent => EF.Functions.Like(agent.Name, $"%{search}%") || EF.Functions.Like(agent.LoginId, $"%{search}%"));
    }

    public static IQueryable<Agent> SortBy(this IQueryable<Agent> query, string sortBy = null)
    {
        if (string.IsNullOrWhiteSpace(sortBy))
        {
            return query.OrderBy(agent => agent.IsDisabled).ThenByDescending(agent => agent.CreatedUtc);
        }

        if (sortBy[0] == '-')
        {
            return SortByDescending(query, sortBy);
        }

        return SortByAscending(query, sortBy);
    }

    public static IQueryable<Agent> Filter(this IQueryable<Agent> query, AgentsQueryOptions filterOptions)
    {
        if (!filterOptions.LicenseStatuses.All(string.IsNullOrWhiteSpace))
        {
            bool fetchEnabled = false;
            bool fetchDisabled = false;

            foreach (var licenseStatus in filterOptions.LicenseStatuses)
            {
                if (string.Equals(licenseStatus, "enabled", StringComparison.OrdinalIgnoreCase))
                {
                    fetchEnabled = true;
                }
                else if (string.Equals(licenseStatus, "disabled", StringComparison.OrdinalIgnoreCase))
                {
                    fetchDisabled = true;
                }
            }

            if (fetchEnabled && fetchDisabled)
            {
                // Do nothing.
            }
            else if (fetchDisabled)
            {
                query = query.Where(agent => agent.IsDisabled);
            }
            else
            {
                query = query.Where(agent => !agent.IsDisabled);
            }
        }

        if (!filterOptions.AppStatuses.All(string.IsNullOrWhiteSpace))
        {
            var requestedStatuses = new List<int>();

            foreach (var appStatus in filterOptions.AppStatuses)
            {
                if (string.Equals(appStatus, "loggedOut", StringComparison.OrdinalIgnoreCase))
                {
                    requestedStatuses.Add(0);
                }
                else if (string.Equals(appStatus, "sanasEnabled", StringComparison.OrdinalIgnoreCase))
                {
                    requestedStatuses.Add(1);
                }
                else if (string.Equals(appStatus, "sanasDisabled", StringComparison.OrdinalIgnoreCase))
                {
                    requestedStatuses.Add(2);
                }

                query = query.Where(agent => requestedStatuses.Contains(agent.AppStatus));
            }
        }

        if (!filterOptions.Voices.All(string.IsNullOrWhiteSpace))
        {
            var requestedVoices = new List<int>();

            foreach (var voice in filterOptions.Voices)
            {
                if (string.Equals(voice, "masculine", StringComparison.OrdinalIgnoreCase))
                {
                    requestedVoices.Add(1);
                }
                else if (string.Equals(voice, "feminine", StringComparison.OrdinalIgnoreCase))
                {
                    requestedVoices.Add(0);
                }

                query = query.Where(agent => requestedVoices.Contains(agent.AgentPreferences.AgentVoice));
            }
        }

        if (!filterOptions.AppVersions.All(string.IsNullOrWhiteSpace))
        {
#pragma warning disable CA1307 // Specify StringComparison for clarity
            query = query.Where(agent => filterOptions.AppVersions.Any(v => agent.LastAppVersion.Contains(v)));
#pragma warning restore CA1307 // Specify StringComparison for clarity
        }

        if (filterOptions.IcpStatuses.Any())
        {
            query = query.Where(agent => filterOptions.IcpStatuses.Any(status => status == agent.IcpStatus));
        }

        return query;
    }

    private static IQueryable<Agent> SortByAscending(IQueryable<Agent> query, string sortBy)
    {
        var result = sortBy.ToLower() switch
        {
            "agentname" or "name" => query.OrderBy(agent => agent.Name),
            "userid" or "loginid" => query.OrderBy(agent => agent.LoginId),
            "lastloginutc" or "lastlogin" => query.OrderBy(agent => agent.LastLoginUtc),
            "lastseenutc" or "lastseen" or "lastlogout" => query.OrderBy(agent => agent.LastSeenUtc),
            "isdisabled" or "disabled" => query.OrderBy(agent => agent.IsDisabled),
            "sanasstatus" or "appstatus" => query.OrderBy(agent => agent.AppStatus),
            "voice" or "agentvoice" => query.OrderBy(agent => agent.AgentPreferences.AgentVoice),
            _ => query.OrderBy(agent => agent.IsDisabled).ThenByDescending(agent => agent.CreatedUtc)
        };

        return result;
    }

    private static IQueryable<Agent> SortByDescending(IQueryable<Agent> query, string sortBy)
    {
        return sortBy.ToLower() switch
        {
            "-agentname" or "-name" => query.OrderByDescending(agent => agent.Name),
            "-userid" or "-loginid" => query.OrderByDescending(agent => agent.LoginId),
            "-lastloginutc" or "-lastlogin" => query.OrderByDescending(agent => agent.LastLoginUtc),
            "-lastseenutc" or "-lastseen" or "-lastlogout" => query.OrderByDescending(agent => agent.LastSeenUtc),
            "-isdisabled" or "-disabled" => query.OrderByDescending(agent => agent.IsDisabled),
            "-sanasstatus" or "-appstatus" => query.OrderByDescending(agent => agent.AppStatus),
            "-voice" or "-agentvoice" => query.OrderByDescending(agent => agent.AgentPreferences.AgentVoice),
            _ => query.OrderBy(agent => agent.IsDisabled).ThenByDescending(agent => agent.CreatedUtc)
        };
    }
}

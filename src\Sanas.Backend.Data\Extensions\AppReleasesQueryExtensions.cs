﻿using Microsoft.EntityFrameworkCore;
using Sanas.Backend.Data.Models;

namespace Sanas.Backend.Data.Extensions;

public static class AppReleasesQueryExtensions
{
    public static IQueryable<AppDownload> Where(this IQueryable<AppDownload> query, string search = null)
    {
        if (string.IsNullOrWhiteSpace(search))
        {
            return query;
        }

        return query.Where(d => EF.Functions.Like(d.Release.Version, $"%{search}%"));
    }

    public static IQueryable<AppDownload> Filter(this IQueryable<AppDownload> query, AppReleasesQueryOptions filterOptions)
    {
        query = query.Where(d => EF.Functions.ILike(filterOptions.CountryCodes, d.CountryCode));

        if (!filterOptions.ModelTypes.All(m => m == 0))
        {
            query = query.Where(d => filterOptions.ModelTypes.Any(modelType => d.ModelType.ModelTypeId == modelType));
        }

        if (filterOptions.StartDate.HasValue)
        {
            query = query.Where(d => d.Release.ReleaseDate.Date >= filterOptions.StartDate.Value.Date);
        }

        if (filterOptions.EndDate.HasValue)
        {
            query = query.Where(d => d.Release.ReleaseDate.Date < filterOptions.EndDate.Value.Date.AddDays(1));
        }

        if (filterOptions.Versions.Count > 0)
        {
            query = query.Where(d => filterOptions.Versions.Any(version => EF.Functions.ILike(d.Release.Version, version)));
        }

        return query;
    }

    public static IQueryable<AppDownload> SortBy(this IQueryable<AppDownload> query, string sortBy = null)
    {
        if (string.IsNullOrWhiteSpace(sortBy))
        {
            return query.OrderByDescending(d => d.Release.ReleaseDate);
        }

        if (sortBy[0] == '-')
        {
            return SortByDescending(query, sortBy);
        }

        return SortByAscending(query, sortBy);
    }

    private static IQueryable<AppDownload> SortByAscending(this IQueryable<AppDownload> query, string sortBy = null)
    {
        var result = sortBy.ToLower() switch
        {
            "modelType" => query.OrderBy(d => d.ModelType),
            "countryCode" => query.OrderBy(d => d.ModelType),
            "version" => query.OrderBy(d => d.Release.Version),
            _ or "releaseDate" => query.OrderBy(d => d.Release.ReleaseDate),
        };

        return query;
    }

    private static IQueryable<AppDownload> SortByDescending(this IQueryable<AppDownload> query, string sortBy = null)
    {
        var result = sortBy.ToLower() switch
        {
            "-modelType" => query.OrderByDescending(d => d.ModelType),
            "-countryCode" => query.OrderByDescending(d => d.ModelType),
            "-version" => query.OrderByDescending(d => d.Release.Version),
            _ or "-releaseDate" => query.OrderByDescending(d => d.Release.ReleaseDate)
        };

        return query;
    }
}

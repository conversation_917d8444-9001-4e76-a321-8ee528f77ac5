﻿using Microsoft.EntityFrameworkCore;
using Sanas.Backend.Data.Models;

namespace Sanas.Backend.Data.Extensions;

internal static class NotificationsQueryExtensions
{
    public static IQueryable<Notification> Where(this IQueryable<Notification> query, string search = null)
    {
        if (string.IsNullOrWhiteSpace(search))
        {
            return query;
        }

        return query.Where(notification => EF.Functions.Like(notification.Content, $"%{search}%"));
    }

    public static IQueryable<Notification> Filter(this IQueryable<Notification> query, NotificationQueryOptions queryOptions)
    {
        if (!queryOptions.NotificationTypes.All(type => string.IsNullOrWhiteSpace(type)))
        {
            var typeFilterValues = new List<int>();

            foreach (var type in queryOptions.NotificationTypes)
            {
                var typeValue = type.ToLower() switch
                {
                    "userlogout" or "agentlogout" => 1,
                    "sanasdisabled" or "synthesisdisabled" => 2,
                    "feedback" or "feedbacksubmitted" => 3,
                    _ => 0
                };

                if (typeValue > 0)
                {
                    typeFilterValues.Add(typeValue);
                }
            }

            if (typeFilterValues.Count > 0)
            {
                query = query.Where(n => typeFilterValues.Contains(n.NotificationType));
            }
        }

        if (!queryOptions.LoginIds.All(string.IsNullOrEmpty))
        {
            query = query.Where(n => queryOptions.LoginIds.Any(id => EF.Functions.ILike(n.Agent.LoginId, id)));
        }

        return query;
    }
}

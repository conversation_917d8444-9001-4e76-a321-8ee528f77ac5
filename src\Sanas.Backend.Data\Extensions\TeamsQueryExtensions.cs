﻿using Microsoft.EntityFrameworkCore;
using Sanas.Backend.Data.Models;

namespace Sanas.Backend.Data.Extensions;

public static class TeamsQueryExtensions
{
    public static IQueryable<Team> Where(this IQueryable<Team> query, string search = null)
    {
        if (string.IsNullOrWhiteSpace(search))
        {
            return query;
        }

        return query.Where(x => EF.Functions.Like(x.Name, $"%{search}%"));
    }

    public static IQueryable<Team> SortBy(this IQueryable<Team> query, string sortBy = null)
    {
        if (string.IsNullOrWhiteSpace(sortBy))
        {
            return query.OrderBy(team => team.IsDisabled).ThenBy(team => team.Name);
        }

        if (sortBy[0] == '-')
        {
            return SortByDescending(query, sortBy);
        }

        return SortByAscending(query, sortBy);
    }

    private static IQueryable<Team> SortByAscending(IQueryable<Team> query, string sortBy)
    {
        return sortBy.ToLower() switch
        {
            "name" => query.OrderBy(team => team.Name),
            "isdisabled" or "disabled" => query.OrderBy(team => team.IsDisabled),
            "workspace" or "workspaceid" => query.OrderBy(team => team.WorkspaceId),
            _ => query.OrderBy(team => team.IsDisabled).ThenBy(team => team.Name),
        };
    }

    private static IQueryable<Team> SortByDescending(IQueryable<Team> query, string sortBy)
    {
        return sortBy.ToLower() switch
        {
            "-name" => query.OrderByDescending(team => team.Name),
            "-isdisabled" or "-disabled" => query.OrderByDescending(team => team.IsDisabled),
            "-workspace" or "-workspaceid" => query.OrderByDescending(team => team.WorkspaceId),
            _ => query.OrderBy(team => team.IsDisabled).ThenBy(team => team.Name),
        };
    }
}

﻿using Microsoft.EntityFrameworkCore;
using Sanas.Backend.Data.Models;

namespace Sanas.Backend.Data.Extensions;

internal static class UsersQueryExtensions
{
    public static IQueryable<PortalUser> Where(this IQueryable<PortalUser> query, string search = null)
    {
        if (string.IsNullOrWhiteSpace(search))
        {
            return query;
        }

        return query.Where(agent =>
        EF.Functions.Like(agent.FirstName, $"%{search}%") ||
        EF.Functions.Like(agent.LastName, $"%{search}%") ||
        EF.Functions.Like(agent.Email, $"%{search}%"));
    }

    public static IQueryable<PortalUserLite> Where(this IQueryable<PortalUserLite> query, string search = null)
    {
        if (string.IsNullOrWhiteSpace(search))
        {
            return query;
        }

        return query.Where(agent =>
        EF.Functions.Like(agent.FirstName, $"%{search}%") ||
        EF.Functions.Like(agent.LastName, $"%{search}%") ||
        EF.Functions.Like(agent.Email, $"%{search}%"));
    }

    public static IQueryable<PortalUser> SortBy(this IQueryable<PortalUser> query, string sortBy = null)
    {
        if (string.IsNullOrWhiteSpace(sortBy))
        {
            return query
                .OrderBy(user => user.IsDisabled)
                .ThenBy(user => user.FirstName)
                .ThenBy(user => user.LastName);
        }

        if (sortBy[0] == '-')
        {
            return sortBy.ToLower() switch
            {
                "-firstname" => query.OrderByDescending(user => user.FirstName),
                "-lastname" => query.OrderByDescending(user => user.LastName),
                "-email" => query.OrderByDescending(user => user.Email),
                _ => query.OrderByDescending(user => user.FirstName)
            };
        }

        return sortBy.ToLower() switch
        {
            "firstname" => query.OrderBy(user => user.FirstName),
            "lastname" => query.OrderBy(user => user.LastName),
            "email" => query.OrderBy(user => user.Email),
            _ => query.OrderBy(user => user.FirstName)
        };
    }

    public static IQueryable<PortalUserLite> SortBy(this IQueryable<PortalUserLite> query, string sortBy = null)
    {
        if (string.IsNullOrWhiteSpace(sortBy))
        {
            return query
                .OrderBy(user => user.IsDisabled)
                .ThenBy(user => user.FirstName)
                .ThenBy(user => user.LastName);
        }

        if (sortBy[0] == '-')
        {
            return sortBy.ToLower() switch
            {
                "-firstname" => query.OrderByDescending(user => user.FirstName),
                "-lastname" => query.OrderByDescending(user => user.LastName),
                "-email" => query.OrderByDescending(user => user.Email),
                _ => query.OrderByDescending(user => user.FirstName)
            };
        }

        return sortBy.ToLower() switch
        {
            "firstname" => query.OrderBy(user => user.FirstName),
            "lastname" => query.OrderBy(user => user.LastName),
            "email" => query.OrderBy(user => user.Email),
            _ => query.OrderBy(user => user.FirstName)
        };
    }
}

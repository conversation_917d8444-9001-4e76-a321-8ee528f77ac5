﻿using Microsoft.EntityFrameworkCore;
using Sanas.Backend.Data.Models;

namespace Sanas.Backend.Data.Extensions;

internal static class WorkspaceQueryExtensions
{
    public static IQueryable<Workspace> Where(this IQueryable<Workspace> query, string search = null)
    {
        if (string.IsNullOrWhiteSpace(search))
        {
            return query;
        }

        return query.Where(workspace =>
        EF.Functions.Like(workspace.WorkspaceKey, $"%{search}%") ||
        EF.Functions.Like(workspace.Enterprise, $"%{search}%") ||
        EF.Functions.Like(workspace.BpoSite.BPO, $"%{search}%") ||
        EF.Functions.Like(workspace.BpoSite.Site, $"%{search}%"));
    }

    public static IQueryable<Workspace> SortBy(this IQueryable<Workspace> query, string sortBy = null)
    {
        if (string.IsNullOrWhiteSpace(sortBy))
        {
            return query
                .OrderBy(w => w.IsDisabled)
                .ThenBy(w => w.Enterprise)
                .ThenBy(w => w.BpoSite.BPO)
                .ThenBy(w => w.BpoSite.Site);
        }

        if (sortBy[0] == '-')
        {
            return SortByDescending(query, sortBy);
        }

        return SortByAscending(query, sortBy);
    }

    private static IQueryable<Workspace> SortByAscending(IQueryable<Workspace> query, string sortBy)
    {
        return sortBy.ToLower() switch
        {
            "enterprise" => query.OrderBy(w => w.Enterprise).ThenBy(w => w.BpoSite.BPO).ThenBy(w => w.BpoSite.Site),
            "bpo" or "process" => query.OrderBy(w => w.BpoSite.BPO).ThenBy(w => w.BpoSite.Site).ThenBy(w => w.Enterprise),
            "site" or "location" => query.OrderBy(w => w.BpoSite.Site).ThenBy(w => w.BpoSite.BPO).ThenBy(w => w.Enterprise),
            "allocatedseats" or "seats" => query.OrderBy(w => w.AllocatedSeats),
            "teams" => query.OrderBy(w => w.Teams.Count).ThenBy(w => w.Enterprise).ThenBy(w => w.BpoSite.BPO).ThenBy(w => w.BpoSite.Site),
            "users" or "agents" => query.OrderBy(w => w.Agents.Count).ThenBy(w => w.Enterprise).ThenBy(w => w.BpoSite.BPO).ThenBy(w => w.BpoSite.Site),
            "portalusers" or "members" => query.OrderBy(w => w.WorkspaceUsers.Count).ThenBy(w => w.Enterprise).ThenBy(w => w.BpoSite.BPO).ThenBy(w => w.BpoSite.Site),
            "workspacekey" => query.OrderBy(w => w.WorkspaceKey),
            "isdisabled" => query.OrderBy(w => w.IsDisabled).ThenBy(w => w.Enterprise).ThenBy(w => w.BpoSite.BPO).ThenBy(w => w.BpoSite.Site),
            _ => query.OrderBy(w => w.IsDisabled).ThenBy(w => w.Enterprise).ThenBy(w => w.BpoSite.BPO).ThenBy(w => w.BpoSite.Site)
        };
    }

    private static IQueryable<Workspace> SortByDescending(IQueryable<Workspace> query, string sortBy)
    {
        return sortBy.ToLower() switch
        {
            "-enterprise" => query.OrderByDescending(w => w.Enterprise).ThenByDescending(w => w.BpoSite.BPO).ThenByDescending(w => w.BpoSite.Site),
            "-bpo" or "-process" => query.OrderByDescending(w => w.BpoSite.BPO).ThenByDescending(w => w.BpoSite.Site).ThenByDescending(w => w.Enterprise),
            "-site" or "-location" => query.OrderByDescending(w => w.BpoSite.Site).ThenByDescending(w => w.BpoSite.BPO).ThenByDescending(w => w.Enterprise),
            "-allocatedseats" or "-seats" => query.OrderByDescending(w => w.AllocatedSeats),
            "-teams" => query.OrderByDescending(w => w.Teams.Count).ThenByDescending(w => w.Enterprise).ThenByDescending(w => w.BpoSite.BPO).ThenByDescending(w => w.BpoSite.Site),
            "-users" or "-agents" => query.OrderByDescending(w => w.Agents.Count).ThenByDescending(w => w.Enterprise).ThenByDescending(w => w.BpoSite.BPO).ThenByDescending(w => w.BpoSite.Site),
            "-portalusers" or "-members" => query.OrderByDescending(w => w.WorkspaceUsers.Count).ThenByDescending(w => w.Enterprise).ThenByDescending(w => w.BpoSite.BPO).ThenByDescending(w => w.BpoSite.Site),
            "-workspacekey" => query.OrderByDescending(w => w.WorkspaceKey),
            "-isdisabled" => query.OrderByDescending(w => w.IsDisabled).ThenByDescending(w => w.Enterprise).ThenByDescending(w => w.BpoSite.BPO).ThenByDescending(w => w.BpoSite.Site),
            _ => query.OrderBy(w => w.IsDisabled).ThenBy(w => w.Enterprise).ThenBy(w => w.BpoSite.BPO).ThenBy(w => w.BpoSite.Site)
        };
    }
}

﻿using System.Text.Json.Serialization;

namespace Sanas.Backend.Data.Models;

[JsonConverter(typeof(JsonStringEnumConverter))]
public enum SysUserProfileTypeName
{
    LocalOrWorkgroup,
    AD,
    AAD,
}

public enum SysUserProfileType
{
    LocalOrWorkgroup = 0,
    AD = 1,
    AAD = 2,
}

public partial class Agent
{
    public int AgentId { get; set; }

    public string LoginId { get; set; }

    public string Name { get; set; }

    public string Password { get; set; }

    public bool IsPasswordTemporary { get; set; }

    public int WorkspaceId { get; set; }

    public int TeamId { get; set; }

    public bool IsDisabled { get; set; }

    public int AppStatus { get; set; }

    public DateTime? LastSeenUtc { get; set; }

    public DateTime? LastLoginUtc { get; set; }

    public DateTime? LastEngagedUtc { get; set; }

    public string LastAppVersion { get; set; }

    public string LastMachine { get; set; }

    public DateTime CreatedUtc { get; set; }

    public DateTime UpdatedUtc { get; set; }

    public string IcpReport { get; set; }
    
    public int IcpStatus { get; set; }

    public SysUserProfileType? SysUserProfileType { get; set; }
    
    public string SysUserName { get; set; }
    
    public string SysDomainName { get; set; }

    public virtual AgentPreferences AgentPreferences { get; set; } = new();

    public virtual ICollection<Feedback> Feedbacks { get; set; } = [];

    public virtual ICollection<Notification> Notifications { get; set; } = [];

    public virtual Team Team { get; set; }

    public virtual Workspace Workspace { get; set; }
}

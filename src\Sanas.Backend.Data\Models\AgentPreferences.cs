﻿namespace Sanas.Backend.Data.Models;

public partial class AgentPreferences
{
    public int AgentPreferencesId { get; set; }

    public bool IsSynthesisEnabled { get; set; }

    public bool IsFeedbackSubmissionEnabled { get; set; }

    public bool IsAlcEnabled { get; set; }

    public bool IsAutologinEnabled { get; set; }

    public int AgentVoice { get; set; }

    public int AgentAccent { get; set; }

    public int AgentVad { get; set; }

    public int AgentId { get; set; }

    public DateTime CreatedUtc { get; set; }

    public DateTime UpdatedUtc { get; set; }

    public virtual Agent Agent { get; set; }

    public AgentPreferences()
    {
        IsAutologinEnabled = true;
        IsSynthesisEnabled = true;
        IsAlcEnabled = true;
        IsFeedbackSubmissionEnabled = true;
        AgentVoice = 1;
    }
}

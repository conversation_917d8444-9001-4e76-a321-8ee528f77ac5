﻿namespace Sanas.Backend.Data.Models;

public partial class AppDownload
{
    public int DownloadId { get; set; }

    public int ReleaseId { get; set; }

    public string CountryCode { get; set; }

    public int ModelTypeId { get; set; }

    public string ExeDownloadUrl { get; set; }

    public string MsiDownloadUrl { get; set; }

    public virtual ModelType ModelType { get; set; }

    public virtual AppRelease Release { get; set; }
}

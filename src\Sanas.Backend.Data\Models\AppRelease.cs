﻿namespace Sanas.Backend.Data.Models;

public partial class AppRelease
{
    public int ReleaseId { get; set; }

    public string Version { get; set; }

    public DateTimeOffset ReleaseDate { get; set; }

    public string ReleaseNotes { get; set; }

    public bool IsBeta { get; set; }

    public DateTime CreatedUtc { get; set; }

    public DateTime UpdatedUtc { get; set; }

    public virtual ICollection<AppDownload> AppDownloads { get; set; } = [];
}

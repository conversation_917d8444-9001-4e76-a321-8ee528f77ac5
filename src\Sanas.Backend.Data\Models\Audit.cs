﻿namespace Sanas.Backend.Data.Models;

public partial class Audit
{
    public int AuditId { get; set; }

    public int WorkspaceId { get; set; }

    public string TableName { get; set; }

    public int RecordId { get; set; }

    public int PortalUserId { get; set; }

    public int AuditType { get; set; }

    public string ChangeDescription { get; set; }

    public DateTime CreatedUtc { get; set; }

    public DateTime UpdatedUtc { get; set; }

    public virtual PortalUser PortalUser { get; set; }

    public virtual Workspace Workspace { get; set; }
}

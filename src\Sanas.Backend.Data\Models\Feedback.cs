﻿namespace Sanas.Backend.Data.Models;

public partial class Feedback
{
    public int FeedbackId { get; set; }

    public int WorkspaceId { get; set; }

    public int AgentId { get; set; }

    public string LoginId { get; set; }

    public string AppVersion { get; set; }

    public string Description { get; set; }

    public short SentimentType { get; set; }

    public string AttachmentType { get; set; }

    public string AttachmentNumber { get; set; }

    public string TrackingNumber { get; set; }

    public string SanasUniqueId { get; set; }

    public DateTime CreatedUtc { get; set; }

    public DateTime UpdatedUtc { get; set; }

    public virtual Agent Agent { get; set; }

    public virtual Workspace Workspace { get; set; }
}

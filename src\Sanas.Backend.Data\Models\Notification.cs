﻿namespace Sanas.Backend.Data.Models;

public partial class Notification
{
    public int NotificationId { get; set; }

    public int WorkspaceId { get; set; }
    
    public int TeamId { get; set; }

    public int AgentId { get; set; }

    public short NotificationType { get; set; }

    public string Content { get; set; }

    public IList<int> ReadbyUserIds { get; set; } = [];

    public DateTime CreatedUtc { get; set; }

    public DateTime UpdatedUtc { get; set; }

    public virtual Agent Agent { get; set; }

    public virtual Team Team { get; set; }

    public virtual Workspace Workspace { get; set; }
}

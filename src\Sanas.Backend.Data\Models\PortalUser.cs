﻿namespace Sanas.Backend.Data.Models;

public partial class PortalUser
{
    public int PortalUserId { get; set; }

    public string IdpUserId { get; set; }

    public string FirstName { get; set; }

    public string LastName { get; set; }

    public string Email { get; set; }

    public string NotificationToken { get; set; }

    public bool IsDisabled { get; set; }

    public bool IsSanasUser { get; set; }

    public DateTime CreatedUtc { get; set; }

    public DateTime UpdatedUtc { get; set; }

    public DateTime? InvitedUtc { get; set; }

    public bool IsAccountVerified { get; set; }

    public virtual ICollection<Audit> Audits { get; set; } = [];

    public virtual SanasUser SanasUser { get; set; }

    public virtual ICollection<TeamUser> TeamUsers { get; set; } = [];

    public virtual ICollection<WorkspaceUser> WorkspaceUsers { get; set; } = [];
}

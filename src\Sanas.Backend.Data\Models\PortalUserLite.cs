﻿using System.ComponentModel.DataAnnotations.Schema;

namespace Sanas.Backend.Data.Models;

public class PortalUserLite
{
    [Column("portal_user_id")]
    public int PortalUserId { get; set; }

    [Column("idp_user_id")]
    public string IdpUserId { get; set; }

    [Column("first_name")]
    public string FirstName { get; set; }

    [Column("last_name")]
    public string LastName { get; set; }

    [Column("email")]
    public string Email { get; set; }

    [Column("notification_token")]
    public string NotificationToken { get; set; }

    [Column("is_disabled")]
    public bool IsDisabled { get; set; }

    [Column("is_sanas_user")]
    public bool IsSanasUser { get; set; }

    [Column("created_utc")]
    public DateTime CreatedUtc { get; set; }

    [Column("updated_utc")]
    public DateTime UpdatedUtc { get; set; }

    [Column("invited_utc")]
    public DateTime? InvitedUtc { get; set; }

    [Column("is_account_verified")]
    public bool IsAccountVerified { get; set; }

    [Column("role_name")]
    public string RoleName { get; set; }
}

﻿namespace Sanas.Backend.Data.Models;

public partial class Team
{
    public int TeamId { get; set; }

    public string Name { get; set; }

    public int WorkspaceId { get; set; }

    public bool IsDisabled { get; set; }

    public bool IsDefaultTeam { get; set; }

    public DateTime CreatedUtc { get; set; }

    public DateTime UpdatedUtc { get; set; }

    public virtual ICollection<Agent> Agents { get; set; } = [];

    public virtual ICollection<Notification> Notifications { get; set; } = [];

    public virtual ICollection<TeamUser> TeamUsers { get; set; } = [];

    public virtual Workspace Workspace { get; set; }
}

﻿using System.Text.Json.Serialization;

namespace Sanas.Backend.Data.Models;

[JsonConverter(typeof(JsonStringEnumConverter))]
public enum ActivationMechanismName
{
    UAK,
    AA
}

public enum ActivationMode
{
    UAK = 0,
    AA = 1
}

public partial class Workspace
{
    public int WorkspaceId { get; set; }

    public int AllocatedSeats { get; set; }

    public string Enterprise { get; set; }

    public string WorkspaceKey { get; set; }

    public bool IsDisabled { get; set; }

    public int BpoSiteId { get; set; }

    public string CountryCode { get; set; }

    public string[] ModelTypes { get; set; }

    public DateTime CreatedUtc { get; set; }

    public DateTime UpdatedUtc { get; set; }

    public string AppConfig { get; set; }

    public Guid InstallerId { get; set; }

    public ActivationMode ActivationMode { get; set; }

    public virtual BpoSite BpoSite { get; set; }
    
    public virtual ICollection<Agent> Agents { get; set; } = [];

    public virtual ICollection<Audit> Audits { get; set; } = [];

    public virtual ICollection<Feedback> Feedbacks { get; set; } = [];

    public virtual ICollection<Notification> Notifications { get; set; } = [];

    public virtual ICollection<Team> Teams { get; set; } = [];

    public virtual ICollection<WorkspaceUser> WorkspaceUsers { get; set; } = [];
}

﻿namespace Sanas.Backend.Data;

public record NotificationQueryOptions : QueryOptions
{
    public NotificationQueryOptions(QueryOptions queryOptions)
    {
        SortBy = queryOptions.SortBy;
        Skip = queryOptions.Skip;
        SortBy = queryOptions.SortBy;
        SearchText = queryOptions.SearchText;
        LoadAll = queryOptions.LoadAll;
        Fetch = queryOptions.Fetch;
    }

    public IEnumerable<string> NotificationTypes { get; set; } = [];

    public IEnumerable<string> LoginIds { get; set; } = [];

    public int RequestingUserId { get; set; }
}

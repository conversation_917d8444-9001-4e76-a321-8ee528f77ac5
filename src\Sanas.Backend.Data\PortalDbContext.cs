using Microsoft.EntityFrameworkCore;
using Sanas.Backend.Data.Models;

namespace Sanas.Backend.Data;

public partial class PortalDbContext : DbContext
{
    public PortalDbContext()
    {
    }

    public PortalDbContext(DbContextOptions<PortalDbContext> options)
        : base(options)
    {
    }

    public virtual DbSet<Agent> Agents { get; set; }

    public virtual DbSet<AgentPreferences> AgentPreferences { get; set; }

    public virtual DbSet<Audit> Audits { get; set; }

    public virtual DbSet<BpoSite> BpoSites { get; set; }

    public virtual DbSet<Feedback> Feedbacks { get; set; }

    public virtual DbSet<Notification> Notifications { get; set; }

    public virtual DbSet<Permission> Permissions { get; set; }

    public virtual DbSet<PortalUser> PortalUsers { get; set; }

    public virtual DbSet<Role> Roles { get; set; }

    public virtual DbSet<RolePermission> RolePermissions { get; set; }

    public virtual DbSet<SanasUser> SanasUsers { get; set; }

    public virtual DbSet<Team> Teams { get; set; }

    public virtual DbSet<TeamUser> TeamUsers { get; set; }

    public virtual DbSet<Workspace> Workspaces { get; set; }

    public virtual DbSet<WorkspaceUser> WorkspaceUsers { get; set; }
    
    public virtual DbSet<IdealCustomerProfile> IdealCustomerProfiles { get; set; }

    public virtual DbSet<AppRelease> AppReleases { get; set; }
    
    public virtual DbSet<AppDownload> AppDownloads { get; set; }

    public virtual DbSet<ModelType> ModelTypes { get; set; }
    
    public virtual DbSet<NcworkspaceUserTracking> NcworkspaceUserTrackings { get; set; }
    
    protected override void OnModelCreating(ModelBuilder modelBuilder)
    {
        modelBuilder.HasPostgresExtension("citext");

        modelBuilder.Entity<Agent>(entity =>
        {
            entity.ToTable("agents");

            entity.HasIndex(e => e.IsDisabled, "IX_agents_is_disabled");

            entity.HasIndex(e => e.Name, "IX_agents_name");

            entity.HasIndex(e => e.TeamId, "IX_agents_team_id");

            entity.HasIndex(e => e.WorkspaceId, "IX_agents_workspace_id");

            entity.Property(e => e.AgentId).HasColumnName("agent_id");
            entity.Property(e => e.AppStatus).HasColumnName("app_status");
            entity.Property(e => e.CreatedUtc)
                .HasDefaultValueSql("now()")
                .HasColumnName("created_utc");
            entity.Property(e => e.IsDisabled).HasColumnName("is_disabled");
            entity.Property(e => e.IsPasswordTemporary).HasColumnName("is_password_temporary");
            entity.Property(e => e.LastAppVersion)
                .HasColumnType("citext")
                .HasColumnName("last_app_version");
            entity.Property(e => e.LastEngagedUtc).HasColumnName("last_engaged_utc");
            entity.Property(e => e.LastLoginUtc).HasColumnName("last_login_utc");
            entity.Property(e => e.LastMachine)
                .HasColumnType("citext")
                .HasColumnName("last_machine");
            entity.Property(e => e.LastSeenUtc).HasColumnName("last_seen_utc");
            entity.Property(e => e.LoginId)
                .IsRequired()
                .HasColumnType("citext")
                .HasColumnName("login_id");
            entity.Property(e => e.Name)
                .HasColumnType("citext")
                .HasColumnName("name");
            entity.Property(e => e.Password)
                .IsRequired()
                .HasColumnName("password");
            entity.Property(e => e.TeamId).HasColumnName("team_id");
            entity.Property(e => e.UpdatedUtc)
                .HasDefaultValueSql("now()")
                .HasColumnName("updated_utc");
            entity.Property(e => e.WorkspaceId).HasColumnName("workspace_id");

            entity.Property(e => e.IcpReport)
                .HasColumnType("jsonb")
                .HasColumnName("icp_report");
            entity.Property(e => e.IcpStatus).HasColumnName("icp_status");

            entity.Property(e => e.SysUserProfileType)
                .HasComment("0 = Local,\n 1 = AD,\n 2 = AAD")
                .HasColumnName("sys_user_profile_type");
            
            entity.Property(e => e.SysUserName)
                .HasColumnType("citext")
                .HasColumnName("sys_user_name");
            
            entity.Property(e => e.SysDomainName)
                .HasColumnType("citext")
                .HasColumnName("sys_domain_name");
            
            entity.HasOne(d => d.Team).WithMany(p => p.Agents)
                .HasForeignKey(d => d.TeamId)
                .HasConstraintName("FK_agents_teams");

            entity.HasOne(d => d.Workspace).WithMany(p => p.Agents)
                .HasForeignKey(d => d.WorkspaceId)
                .HasConstraintName("FK_agents_workspaces");
        });

        modelBuilder.Entity<AgentPreferences>(entity =>
        {
            entity.HasKey(e => e.AgentPreferencesId);

            entity.ToTable("agent_preferences");

            entity.HasIndex(e => e.AgentId, "IX_agent_preferences_agent_id").IsUnique();

            entity.Property(e => e.AgentPreferencesId).HasColumnName("agent_preferences_id");
            entity.Property(e => e.AgentAccent).HasColumnName("agent_accent");
            entity.Property(e => e.AgentId).HasColumnName("agent_id");
            entity.Property(e => e.AgentVad).HasColumnName("agent_vad");
            entity.Property(e => e.AgentVoice).HasColumnName("agent_voice");
            entity.Property(e => e.CreatedUtc)
                .HasDefaultValueSql("now()")
                .HasColumnName("created_utc");
            entity.Property(e => e.IsAlcEnabled).HasColumnName("is_alc_enabled");
            entity.Property(e => e.IsAutologinEnabled).HasColumnName("is_autologin_enabled");
            entity.Property(e => e.IsFeedbackSubmissionEnabled).HasColumnName("is_feedback_submission_enabled");
            entity.Property(e => e.IsSynthesisEnabled).HasColumnName("is_synthesis_enabled");
            entity.Property(e => e.UpdatedUtc)
                .HasDefaultValueSql("now()")
                .HasColumnName("updated_utc");

            entity.HasOne(d => d.Agent).WithOne(p => p.AgentPreferences)
                .HasForeignKey<AgentPreferences>(d => d.AgentId)
                .HasConstraintName("FK_agent_preferences_agents");
        });

        modelBuilder.Entity<Audit>(entity =>
        {
            entity.ToTable("audit");

            entity.HasIndex(e => e.PortalUserId, "IX_audit_portal_user_id");

            entity.HasIndex(e => e.TableName, "IX_audit_table_name");

            entity.HasIndex(e => e.WorkspaceId, "IX_audit_workspace_id");

            entity.Property(e => e.AuditId).HasColumnName("audit_id");
            entity.Property(e => e.AuditType).HasColumnName("audit_type");
            entity.Property(e => e.ChangeDescription)
                .IsRequired()
                .HasColumnName("change_description");
            entity.Property(e => e.CreatedUtc)
                .HasDefaultValueSql("now()")
                .HasColumnName("created_utc");
            entity.Property(e => e.PortalUserId).HasColumnName("portal_user_id");
            entity.Property(e => e.RecordId).HasColumnName("record_id");
            entity.Property(e => e.TableName)
                .IsRequired()
                .HasColumnName("table_name");
            entity.Property(e => e.UpdatedUtc)
                .HasDefaultValueSql("now()")
                .HasColumnName("updated_utc");
            entity.Property(e => e.WorkspaceId).HasColumnName("workspace_id");

            entity.HasOne(d => d.PortalUser).WithMany(p => p.Audits)
                .HasForeignKey(d => d.PortalUserId)
                .HasConstraintName("FK_audit_portal_users");

            entity.HasOne(d => d.Workspace).WithMany(p => p.Audits)
                .HasForeignKey(d => d.WorkspaceId)
                .HasConstraintName("FK_audit_workspaces");
        });

        modelBuilder.Entity<BpoSite>(entity =>
        {
            entity.ToTable("bposites");

            entity.HasIndex(e => e.BPO, "IX_bposites_bpo");

            entity.HasIndex(e => e.LookupKey, "IX_bposites_lookup_key");

            entity.HasIndex(e => e.Site, "IX_bposites_site");

            entity.Property(e => e.BpoSiteId).HasColumnName("bposite_id");
            entity.Property(e => e.BPO)
                .IsRequired()
                .HasColumnType("citext")
                .HasColumnName("bpo");
            entity.Property(e => e.CreatedUtc)
                .HasDefaultValueSql("now()")
                .HasColumnName("created_utc");
            entity.Property(e => e.LookupKey)
                .IsRequired()
                .HasDefaultValueSql("(gen_random_uuid())::citext")
                .HasColumnType("citext")
                .HasColumnName("lookup_key");
            entity.Property(e => e.Site)
                .IsRequired()
                .HasColumnType("citext")
                .HasColumnName("site");
            entity.Property(e => e.UpdatedUtc)
                .HasDefaultValueSql("now()")
                .HasColumnName("updated_utc");
        });

        modelBuilder.Entity<Feedback>(entity =>
        {
            entity.ToTable("feedbacks");

            entity.HasIndex(e => e.AgentId, "IX_feedbacks_agent_id");
            entity.HasIndex(e => e.LoginId, "IX_feedbacks_login_id");
            entity.HasIndex(e => e.WorkspaceId, "IX_feedbacks_workspace_id");

            entity.Property(e => e.FeedbackId).HasColumnName("feedback_id");
            entity.Property(e => e.AgentId).HasColumnName("agent_id");
            entity.Property(e => e.AppVersion).HasColumnName("app_version");
            entity.Property(e => e.AttachmentNumber).HasColumnName("attachment_number");
            entity.Property(e => e.AttachmentType).HasColumnName("attachment_type");
            entity.Property(e => e.CreatedUtc)
                .HasDefaultValueSql("now()")
                .HasColumnName("created_utc");
            entity.Property(e => e.Description).HasColumnName("description");
            entity.Property(e => e.LoginId).HasColumnName("login_id");
            entity.Property(e => e.SanasUniqueId).HasColumnName("sanas_unique_id");
            entity.Property(e => e.SentimentType).HasColumnName("sentiment_type");
            entity.Property(e => e.TrackingNumber).HasColumnName("tracking_number");
            entity.Property(e => e.UpdatedUtc)
                .HasDefaultValueSql("now()")
                .HasColumnName("updated_utc");
            entity.Property(e => e.WorkspaceId).HasColumnName("workspace_id");

            entity.HasOne(d => d.Agent).WithMany(p => p.Feedbacks)
                .HasForeignKey(d => d.AgentId)
                .HasConstraintName("FK_feedbacks_agents");

            entity.HasOne(d => d.Workspace).WithMany(p => p.Feedbacks)
                .HasForeignKey(d => d.WorkspaceId)
                .HasConstraintName("FK_feedbacks_workspaces");
        });

        modelBuilder.Entity<Notification>(entity =>
        {
            entity.ToTable("notifications");

            entity.HasIndex(e => e.AgentId, "IX_notifications_agent_id");

            entity.HasIndex(e => e.NotificationType, "IX_notifications_notification_type");

            entity.HasIndex(e => e.TeamId, "IX_notifications_team_id");

            entity.HasIndex(e => e.WorkspaceId, "IX_notifications_workspace_id");

            entity.Property(e => e.NotificationId).HasColumnName("notification_id");
            entity.Property(e => e.AgentId).HasColumnName("agent_id");
            entity.Property(e => e.Content)
                .IsRequired()
                .HasColumnName("content");
            entity.Property(e => e.CreatedUtc)
                .HasDefaultValueSql("now()")
                .HasColumnName("created_utc");
            entity.Property(e => e.NotificationType).HasColumnName("notification_type");
            entity.Property(e => e.ReadbyUserIds)
                .HasDefaultValueSql("ARRAY[]::integer[]")
                .HasColumnName("readby_user_ids");
            entity.Property(e => e.TeamId).HasColumnName("team_id");
            entity.Property(e => e.UpdatedUtc)
                .HasDefaultValueSql("now()")
                .HasColumnName("updated_utc");
            entity.Property(e => e.WorkspaceId).HasColumnName("workspace_id");

            entity.HasOne(d => d.Agent).WithMany(p => p.Notifications)
                .HasForeignKey(d => d.AgentId)
                .HasConstraintName("FK_notifications_agents");

            entity.HasOne(d => d.Team).WithMany(p => p.Notifications).HasForeignKey(d => d.TeamId);

            entity.HasOne(d => d.Workspace).WithMany(p => p.Notifications)
                .HasForeignKey(d => d.WorkspaceId)
                .HasConstraintName("FK_notifications_workspaces");
        });

        modelBuilder.Entity<Permission>(entity =>
        {
            entity.HasKey(e => e.PermissionId).HasName("permissions_pkey");

            entity.ToTable("permissions");

            entity.Property(e => e.PermissionId)
                .UseIdentityAlwaysColumn()
                .HasColumnName("permission_id");
            entity.Property(e => e.Description)
                .HasColumnType("citext")
                .HasColumnName("description");
            entity.Property(e => e.Name)
                .IsRequired()
                .HasColumnType("citext")
                .HasColumnName("name");
        });

        modelBuilder.Entity<PortalUser>(entity =>
        {
            entity.ToTable("portal_users");

            entity.HasIndex(e => e.Email, "IX_portal_users_email");

            entity.HasIndex(e => e.FirstName, "IX_portal_users_first_name");

            entity.HasIndex(e => e.IsDisabled, "IX_portal_users_is_disabled");

            entity.HasIndex(e => e.LastName, "IX_portal_users_last_name");

            entity.Property(e => e.PortalUserId).HasColumnName("portal_user_id");
            entity.Property(e => e.CreatedUtc)
                .HasDefaultValueSql("now()")
                .HasColumnName("created_utc");
            entity.Property(e => e.Email)
                .IsRequired()
                .HasColumnType("citext")
                .HasColumnName("email");
            entity.Property(e => e.FirstName)
                .IsRequired()
                .HasColumnType("citext")
                .HasColumnName("first_name");
            entity.Property(e => e.IdpUserId)
                .IsRequired()
                .HasColumnType("citext")
                .HasColumnName("idp_user_id");
            entity.Property(e => e.InvitedUtc).HasColumnName("invited_utc");
            entity.Property(e => e.IsAccountVerified).HasColumnName("is_account_verified");
            entity.Property(e => e.IsDisabled).HasColumnName("is_disabled");
            entity.Property(e => e.IsSanasUser).HasColumnName("is_sanas_user");
            entity.Property(e => e.LastName)
                .HasColumnType("citext")
                .HasColumnName("last_name");
            entity.Property(e => e.NotificationToken).HasColumnName("notification_token");
            entity.Property(e => e.UpdatedUtc)
                .HasDefaultValueSql("now()")
                .HasColumnName("updated_utc");
        });

        modelBuilder.Entity<Role>(entity =>
        {
            entity.HasKey(e => e.RoleId).HasName("roles_pkey");

            entity.ToTable("roles");

            entity.Property(e => e.RoleId)
                .UseIdentityAlwaysColumn()
                .HasColumnName("role_id");
            entity.Property(e => e.Description)
                .HasColumnType("citext")
                .HasColumnName("description");
            entity.Property(e => e.IsSanasRole).HasColumnName("is_sanas_role");
            entity.Property(e => e.Name)
                .IsRequired()
                .HasColumnType("citext")
                .HasColumnName("name");
        });

        modelBuilder.Entity<RolePermission>(entity =>
        {
            entity.HasKey(e => new { e.RoleId, e.PermissionId }).HasName("role_permissions_pkey");

            entity.ToTable("role_permissions");

            entity.Property(e => e.RoleId).HasColumnName("role_id");
            entity.Property(e => e.PermissionId).HasColumnName("permission_id");

            entity.HasOne(d => d.Permission).WithMany(p => p.RolePermissions)
                .HasForeignKey(d => d.PermissionId)
                .HasConstraintName("FK_rolepermissions_permissions");

            entity.HasOne(d => d.Role).WithMany(p => p.RolePermissions)
                .HasForeignKey(d => d.RoleId)
                .HasConstraintName("FK_rolepermissions_roles");
        });

        modelBuilder.Entity<SanasUser>(entity =>
        {
            entity.HasKey(e => e.UserId).HasName("sanas_users_pkey");

            entity.ToTable("sanas_users");

            entity.Property(e => e.UserId)
                .ValueGeneratedNever()
                .HasColumnName("user_id");
            entity.Property(e => e.RoleIds)
                .IsRequired()
                .HasDefaultValueSql("ARRAY[]::integer[]")
                .HasColumnName("role_ids");

            entity.HasOne(d => d.User).WithOne(p => p.SanasUser)
                .HasForeignKey<SanasUser>(d => d.UserId)
                .HasConstraintName("FK_sanasusers_portalusers");
        });

        modelBuilder.Entity<Team>(entity =>
        {
            entity.ToTable("teams");

            entity.HasIndex(e => e.Name, "IX_teams_Name");

            entity.HasIndex(e => e.IsDisabled, "IX_teams_is_disabled");

            entity.HasIndex(e => e.WorkspaceId, "IX_teams_workspace_id");

            entity.Property(e => e.TeamId).HasColumnName("team_id");
            entity.Property(e => e.CreatedUtc)
                .HasDefaultValueSql("now()")
                .HasColumnName("created_utc");
            entity.Property(e => e.IsDefaultTeam).HasColumnName("is_default_team");
            entity.Property(e => e.IsDisabled).HasColumnName("is_disabled");
            entity.Property(e => e.Name)
                .IsRequired()
                .HasColumnType("citext");
            entity.Property(e => e.UpdatedUtc)
                .HasDefaultValueSql("now()")
                .HasColumnName("updated_utc");
            entity.Property(e => e.WorkspaceId).HasColumnName("workspace_id");

            entity.HasOne(d => d.Workspace).WithMany(p => p.Teams)
                .HasForeignKey(d => d.WorkspaceId)
                .HasConstraintName("FK_teams_workspaces");
        });

        modelBuilder.Entity<TeamUser>(entity =>
        {
            entity.ToTable("team_users");

            entity.HasIndex(e => e.PortalUserId, "IX_team_users_portal_user_id");

            entity.HasIndex(e => e.TeamId, "IX_team_users_team_id");

            entity.Property(e => e.TeamUserId).HasColumnName("team_user_id");
            entity.Property(e => e.PortalUserId).HasColumnName("portal_user_id");
            entity.Property(e => e.TeamId).HasColumnName("team_id");

            entity.HasOne(d => d.PortalUser).WithMany(p => p.TeamUsers)
                .HasForeignKey(d => d.PortalUserId)
                .HasConstraintName("FK_teamusers_portausers");

            entity.HasOne(d => d.Team).WithMany(p => p.TeamUsers)
                .HasForeignKey(d => d.TeamId)
                .HasConstraintName("FK_teamusers_teams");
        });

        modelBuilder.Entity<Workspace>(entity =>
        {
            entity.ToTable("workspaces");

            entity.HasIndex(e => e.BpoSiteId, "IX_workspaces_bposite_id");
            entity.HasIndex(e => e.Enterprise, "IX_workspaces_enterprise");
            entity.HasIndex(e => e.IsDisabled, "IX_workspaces_is_disabled");
            entity.HasIndex(e => e.WorkspaceKey, "IX_workspaces_workspace_key");

            entity.Property(e => e.WorkspaceId).HasColumnName("workspace_id");
            entity.Property(e => e.AllocatedSeats).HasColumnName("allocated_seats");
            entity.Property(e => e.ActivationMode)
                .HasDefaultValue(ActivationMode.UAK)
                .HasComment("0 = UAK\n1 = AA (Auto Activation)")
                .HasColumnName("activation_mode");
            entity.Property(e => e.BpoSiteId).HasColumnName("bposite_id");
            entity.Property(e => e.CountryCode)
                .IsRequired()
                .HasColumnType("citext")
                .HasColumnName("country_code");
            entity.Property(e => e.CreatedUtc)
                .HasDefaultValueSql("now()")
                .HasColumnName("created_utc");
            entity.Property(e => e.Enterprise)
                .IsRequired()
                .HasColumnType("citext")
                .HasColumnName("enterprise");
            entity.Property(e => e.InstallerId)
                .HasDefaultValueSql("gen_random_uuid()")
                .HasColumnName("installer_id");
            entity.Property(e => e.IsDisabled).HasColumnName("is_disabled");
            entity.Property(e => e.ModelTypes)
                .IsRequired()
                .HasColumnType("citext[]")
                .HasColumnName("model_types");
            entity.Property(e => e.UpdatedUtc)
                .HasDefaultValueSql("now()")
                .HasColumnName("updated_utc");
            entity.Property(e => e.WorkspaceKey)
                .IsRequired()
                .HasColumnType("citext")
                .HasColumnName("workspace_key");

            entity.HasOne(d => d.BpoSite).WithMany(p => p.Workspaces)
                .HasForeignKey(d => d.BpoSiteId)
                .HasConstraintName("FK_workspaces_bposites");

            entity.Property(w => w.AppConfig)
            .HasColumnName("app_config")
            .HasDefaultValueSql("{\"autoConfiguration\": true}")
            .HasColumnType("jsonb");
        });

        modelBuilder.Entity<WorkspaceUser>(entity =>
        {
            entity.HasKey(e => new { e.WorkspaceId, e.PortalUserId });

            entity.ToTable("workspace_users");

            entity.HasIndex(e => e.PortalUserId, "IX_workspace_users_portal_user_id");

            entity.Property(e => e.WorkspaceId).HasColumnName("workspace_id");
            entity.Property(e => e.PortalUserId).HasColumnName("portal_user_id");
            entity.Property(e => e.IsUserDisabled).HasColumnName("is_user_disabled");
            entity.Property(e => e.RoleIds)
                .IsRequired()
                .HasDefaultValueSql("ARRAY[]::integer[]")
                .HasColumnName("role_ids");

            entity.HasOne(d => d.PortalUser).WithMany(p => p.WorkspaceUsers)
                .HasForeignKey(d => d.PortalUserId)
                .HasConstraintName("FK_workspaceusers_portalusers");

            entity.HasOne(d => d.Workspace).WithMany(p => p.WorkspaceUsers)
                .HasForeignKey(d => d.WorkspaceId)
                .HasConstraintName("FK_workspaceusers_workspaces");
        });

        modelBuilder.Entity<AppRelease>(entity =>
        {
            entity.HasKey(e => e.ReleaseId).HasName("app_releases_pkey");

            entity.ToTable("app_releases");

            entity.Property(e => e.ReleaseId)
                .UseIdentityAlwaysColumn()
                .HasColumnName("release_id");
            entity.Property(e => e.CreatedUtc)
                .HasDefaultValueSql("now()")
                .HasColumnName("created_utc");
            entity.Property(e => e.IsBeta)
                .HasDefaultValue(false)
                .HasColumnName("is_beta");
            entity.Property(e => e.ReleaseDate).HasColumnName("release_date");
            entity.Property(e => e.ReleaseNotes)
                .HasColumnType("citext")
                .HasColumnName("release_notes");
            entity.Property(e => e.UpdatedUtc).HasColumnName("updated_utc");
            entity.Property(e => e.Version)
                .IsRequired()
                .HasMaxLength(20)
                .HasColumnName("version");
        });

        modelBuilder.Entity<ModelType>(entity =>
        {
            entity.HasKey(e => e.ModelTypeId).HasName("model_types_pkey");

            entity.ToTable("model_types");

            entity.HasIndex(e => e.Name, "unique_model_name").IsUnique();

            entity.Property(e => e.ModelTypeId)
                .UseIdentityAlwaysColumn()
                .HasColumnName("model_type_id");
            entity.Property(e => e.Description)
                .HasColumnType("citext")
                .HasColumnName("description");
            entity.Property(e => e.Name)
                .IsRequired()
                .HasColumnType("citext")
                .HasColumnName("name");
        });

        modelBuilder.Entity<AppDownload>(entity =>
        {
            entity.HasKey(e => e.DownloadId).HasName("app_downloads_pkey");

            entity.ToTable("app_downloads");

            entity.Property(e => e.DownloadId)
                .UseIdentityAlwaysColumn()
                .HasColumnName("download_id");
            entity.Property(e => e.CountryCode)
                .IsRequired()
                .HasColumnType("citext")
                .HasColumnName("country_code");
            entity.Property(e => e.ExeDownloadUrl)
                .IsRequired()
                .HasColumnType("citext")
                .HasColumnName("exe_download_url");
            entity.Property(e => e.ModelTypeId).HasColumnName("model_type_id");
            entity.Property(e => e.MsiDownloadUrl)
                .IsRequired()
                .HasColumnType("citext")
                .HasColumnName("msi_download_url");
            entity.Property(e => e.ReleaseId).HasColumnName("release_id");

            entity.HasOne(d => d.ModelType).WithMany(p => p.AppDownloads)
                .HasForeignKey(d => d.ModelTypeId)
                .HasConstraintName("FK_downloads_modeltypes");

            entity.HasOne(d => d.Release).WithMany(p => p.AppDownloads)
                .HasForeignKey(d => d.ReleaseId)
                .HasConstraintName("FK_downloads_releases");
        });

        modelBuilder.Entity<NcworkspaceUserTracking>(entity =>
        {
            entity.HasKey(e => e.TrackingId);

            entity.ToTable("ncworkspace_user_tracking");

            entity.Property(e => e.TrackingId)
                .UseIdentityAlwaysColumn()
                .HasColumnName("tracking_id");
            entity.Property(e => e.LoginCount)
                .HasDefaultValue(0)
                .HasColumnName("login_count");
            entity.Property(e => e.StepsCompleted)
                .IsRequired()
                .HasDefaultValueSql("ARRAY[]::citext[]")
                .HasColumnType("citext[]")
                .HasColumnName("steps_completed");
            entity.Property(e => e.UserId).HasColumnName("user_id");
            entity.Property(e => e.WorkspaceId).HasColumnName("workspace_id");
        });

        OnModelCreatingPartial(modelBuilder);
    }

    partial void OnModelCreatingPartial(ModelBuilder modelBuilder);
}

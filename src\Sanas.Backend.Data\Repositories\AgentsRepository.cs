﻿using Microsoft.EntityFrameworkCore;
using Sanas.Backend.Data.Extensions;
using Sanas.Backend.Data.Models;
using Sanas.Backend.Data.Repositories.Interfaces;

namespace Sanas.Backend.Data.Repositories;

public class AgentsRepository(PortalDbContext databaseContext) : IAgentsRepository
{
    public async Task<Agent> GetAgentByLoginAsync(string loginId, string workspaceKey)
    {
        var result = await databaseContext.Agents
            .Include(agent => agent.Team)
            .Include(agent => agent.Workspace)
            .Where(agent => agent.LoginId == loginId)
            .Where(agent => !agent.Team.IsDisabled)
            .Where(agent => !agent.Workspace.IsDisabled && agent.Workspace.WorkspaceKey == workspaceKey)
            .Include(agent => agent.AgentPreferences)
            .AsNoTracking()
            .FirstOrDefaultAsync();

        return result;
    }

    public async Task<List<string>> GetLoginIdsInWorkspaceAsync(int workspaceId)
    {
        return await databaseContext.Agents
            .AsNoTracking()
            .Where(agent => agent.WorkspaceId == workspaceId)
            .Select(agent => agent.LoginId.ToLower())
            .ToListAsync();
    }

    public async Task<bool> AgentExistsAsync(int workspaceId, string loginId)
    {
        bool exists = await databaseContext.Agents.AnyAsync(a => EF.Functions.Like(a.LoginId, loginId) && a.WorkspaceId == workspaceId);
        return exists;
    }

    public async Task<Agent> GetAgentByIdAsync(int agentId)
    {
        return await databaseContext.Agents
            .Include(agent => agent.Team)
            .Include(agent => agent.Workspace)
            .Include(agent => agent.AgentPreferences).AsSplitQuery()
            .FirstOrDefaultAsync(agent => agent.AgentId == agentId);
    }
    
    public async Task<Agent> GetAgentByIdAndWorkspaceIdAsync(int agentId, int workspaceId)
    {
        return await databaseContext.Agents
            .Include(agent => agent.Team)
            .Include(agent => agent.Workspace)
            .Include(agent => agent.AgentPreferences).AsSplitQuery()
            .FirstOrDefaultAsync(agent => agent.AgentId == agentId && agent.WorkspaceId == workspaceId);
    }
    
    public async Task<(IEnumerable<Agent> Agents, int TotalRecords)> GetByTeamIdWithPagingAsync(int teamId, AgentsQueryOptions queryOptions)
    {
        var query = databaseContext.Agents
            .Include(agent => agent.AgentPreferences)
            .AsNoTracking()
            .Where(agent => agent.TeamId == teamId)
            .Filter(queryOptions)
            .Where(queryOptions.SearchText);

        if (queryOptions.LoadAll)
        {
            return (await query.SortBy(queryOptions.SortBy).ToListAsync(), await query.CountAsync());
        }
        else
        {
            return (await query.SortBy(queryOptions.SortBy).Skip(queryOptions.Skip).Take(queryOptions.Fetch).ToListAsync(), await query.CountAsync());
        }
    }

    public async Task<(IEnumerable<Agent> Agents, int TotalRecords)> GetAllAgentsInTeamAsync(int teamId, AgentsQueryOptions queryOptions)
    {
        var query = databaseContext.Agents
            .Include(agent => agent.AgentPreferences)
            .AsNoTracking()
            .Where(agent => agent.TeamId == teamId)
            .Filter(queryOptions)
            .Where(queryOptions.SearchText);

        return (await query.SortBy(queryOptions.SortBy).ToListAsync(), await query.CountAsync());
    }

    public async Task<(IEnumerable<Agent> Agents, int TotalRecords)> GetByWorkspaceIdWithPagingAsync(int workspaceId, QueryOptions queryOptions)
    {
        var query = databaseContext.Agents
            .AsNoTracking()
            .Where(agent => agent.WorkspaceId == workspaceId)
            .Where(queryOptions.SearchText);

        if (queryOptions.LoadAll)
        {
            return (await query.SortBy(queryOptions.SortBy).ToListAsync(), await query.CountAsync());
        }
        else
        {
            return (await query.SortBy(queryOptions.SortBy).Skip(queryOptions.Skip).Take(queryOptions.Fetch).ToListAsync(), await query.CountAsync());
        }
    }

    public async Task<bool> UpdateAgentPreferencesAsync(int agentId, string preferenceName, object newValue)
    {
        var agent = await databaseContext.Agents
            .Include(a => a.AgentPreferences)
            .FirstOrDefaultAsync(a => a.AgentId == agentId);

        if (agent is not null)
        {
            var property = typeof(AgentPreferences).GetProperty(preferenceName);

            property?.SetValue(agent.AgentPreferences, newValue);

            if (string.Equals(preferenceName, "IsSynthesisEnabled", StringComparison.OrdinalIgnoreCase))
            {
                agent.AppStatus = ((bool)newValue) ? 1 : 2;
            }

            await databaseContext.SaveChangesAsync();

            return true;
        }

        return false;
    }

    public async Task<bool> UpdateAgentLastLoginTime(int agentId, DateTime loginTime)
    {
        var agent = await databaseContext.Agents
            .Include(agent => agent.AgentPreferences)
            .SingleOrDefaultAsync(pref => pref.AgentId == agentId);

        if (agent != null)
        {
            // AppStatus (0 -> Logged Out   1 -> Synthesis Enabled   2-> Synthesis Disabled)
            agent.AppStatus = (agent.AgentPreferences is null || agent.AgentPreferences.IsSynthesisEnabled) ? 1 : 2;
            agent.LastLoginUtc = loginTime;

            await databaseContext.SaveChangesAsync();

            return true;
        }

        throw new ArgumentException($"No agent record found for agent id {agentId}");
    }

    public async Task<bool> UpdateAgentLastSeenTime(int agentId, DateTime lastSeenTime)
    {
        var existingRecord = await databaseContext.Agents
            .SingleOrDefaultAsync(pref => pref.AgentId == agentId);

        if (existingRecord != null)
        {
            existingRecord.AppStatus = 0;  // Set as logged out
            existingRecord.LastSeenUtc = lastSeenTime;

            await databaseContext.SaveChangesAsync();

            return true;
        }

        throw new ArgumentException($"No agent record found for agent id {agentId}");
    }

    public async Task<Agent> UpdateAgentAsync(int agentId, Agent agent)
    {
        var existingRecord = await databaseContext.Agents
            .Include(agent => agent.AgentPreferences)
            .FirstOrDefaultAsync(pref => pref.AgentId == agentId);

        if (existingRecord != null)
        {
            existingRecord.Password = agent.Password;
            existingRecord.TeamId = agent.TeamId;
            existingRecord.Name = agent.Name;
            existingRecord.IsDisabled = agent.IsDisabled;
            existingRecord.AgentPreferences = agent.AgentPreferences;
            existingRecord.IsPasswordTemporary = agent.IsPasswordTemporary;
            existingRecord.LastMachine = agent.LastMachine;
            existingRecord.LastLoginUtc = agent.LastLoginUtc;
            existingRecord.LastAppVersion = agent.LastAppVersion;
            existingRecord.IcpReport = agent.IcpReport;
            existingRecord.IcpStatus = agent.IcpStatus;
            existingRecord.SysUserProfileType = agent.SysUserProfileType;
            existingRecord.SysUserName = agent.SysUserName;
            existingRecord.SysDomainName = agent.SysDomainName;

            await databaseContext.SaveChangesAsync();

            return existingRecord;
        }

        throw new ArgumentException($"No agent record found for agent id {agentId}");
    }

    public async Task CreateManyAsync(IEnumerable<Agent> agents)
    {
        await databaseContext.Agents.AddRangeAsync(agents);
        await databaseContext.SaveChangesAsync();
    }

    public async Task<Agent> CreateOneAsync(Agent agent)
    {
        var agentEntity = await databaseContext.Agents.AddAsync(agent);
        await databaseContext.SaveChangesAsync();
        return agentEntity.Entity;
    }

    public async Task EnableDisableManyByIdAsync(int workspaceId, IEnumerable<int> agentIds, bool disable)
    {
        await databaseContext.Agents
            .Where(agent => agent.WorkspaceId == workspaceId)
            .Where(agent => agentIds.Any(id => id == agent.AgentId))
            .ForEachAsync(agent =>
            {
                agent.IsDisabled = disable;
                if (disable)
                {
                    // agent.LastSeenUtc = DateTime.UtcNow;
                }
            });

        await databaseContext.SaveChangesAsync();
    }

    public async Task EnableDisableManyByIdAsync(int workspaceId, IEnumerable<string> loginIds, bool disable)
    {
        await databaseContext.Agents
            .Where(agent => agent.WorkspaceId == workspaceId)
            .Where(agent => loginIds.Any(id => id == agent.LoginId))
            .ForEachAsync(agent =>
            {
                agent.IsDisabled = disable;
                if (disable)
                {
                    // agent.LastSeenUtc = DateTime.UtcNow;
                }
            });

        await databaseContext.SaveChangesAsync();
    }

    public async Task<IEnumerable<Agent>> GetAgentsByIdAsync(int workspaceId, IEnumerable<int> agentIds)
    {
        return await databaseContext.Agents
            .AsNoTracking()
            .Where(agent => agentIds.Contains(agent.AgentId))
            .Where(agent => agent.WorkspaceId == workspaceId)
            .ToListAsync();
    }

    public async Task MoveAgentsToTeamAsync(int workspaceId, IEnumerable<int> agentIds, int destinationTeamId)
    {
        var destinationTeam = await databaseContext.Teams
            .SingleOrDefaultAsync(team => team.TeamId == destinationTeamId && team.WorkspaceId == workspaceId);

        if (destinationTeam is null || destinationTeam.IsDisabled)
        {
            throw new ArgumentException("Destination team is either disabled or not found in the workspace.");
        }

        var agents = await databaseContext.Agents
            .Where(agent => agentIds.Contains(agent.AgentId))
            .Where(agent => agent.WorkspaceId == workspaceId)
            .ToListAsync();

        foreach (var agent in agents)
        {
            if (agent.TeamId == destinationTeamId)
            {
                continue;
            }

            agent.TeamId = destinationTeamId;
        }

        await databaseContext.SaveChangesAsync();
    }

    public async Task UpdateICPAsync(int agentId, string icpReport)
    {
        var agent = await databaseContext.Agents.FirstOrDefaultAsync(a => a.AgentId == agentId);

        if (agent is not null)
        {
            agent.IcpReport = icpReport;
            await databaseContext.SaveChangesAsync();
        }
    }
}

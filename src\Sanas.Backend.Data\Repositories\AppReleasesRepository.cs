﻿using Microsoft.EntityFrameworkCore;
using Sanas.Backend.Data.Extensions;
using Sanas.Backend.Data.Models;

namespace Sanas.Backend.Data.Repositories;

public sealed class AppReleasesRepository(PortalDbContext dataContext) : IAppReleasesRepository
{
    public async Task CreateAppReleaseAsync(AppRelease appRelease)
    {
        if (appRelease == null)
        {
            return;
        }

        await dataContext.AppReleases.AddAsync(new AppRelease
        {
            ReleaseDate = appRelease.ReleaseDate,
            ReleaseNotes = appRelease.ReleaseNotes,
            Version = appRelease.Version,
            CreatedUtc = DateTime.UtcNow,
            UpdatedUtc = DateTime.UtcNow,
            AppDownloads = appRelease.AppDownloads,
        });

        await dataContext.SaveChangesAsync();
    }

    public async Task<AppRelease> GetByReleaseIdAsync(int releaseId)
    {
        return await dataContext.AppReleases.FirstOrDefaultAsync(r => r.ReleaseId == releaseId);
    }

    public async Task UpdateReleaseNotesAsync(int releaseId, string releaseNotes)
    {
        var release = await dataContext.AppReleases.FirstOrDefaultAsync(r => r.ReleaseId == releaseId);

        if (release is null)
        {
            throw new ArgumentException("Release not found");
        }

        release.ReleaseNotes = releaseNotes;
        release.UpdatedUtc = DateTime.UtcNow;

        await dataContext.SaveChangesAsync();
    }

    public async Task<(IEnumerable<AppRelease> AppReleases, int TotalRecords)> GetAppReleasesAsync(AppReleasesQueryOptions queryOptions)
    {
        var query = dataContext.AppDownloads
            .Include(d => d.Release)
            .Include(d => d.ModelType)
            .AsNoTracking()
            .Where(queryOptions.SearchText)
            .Filter(queryOptions);

        var totalCount = await query.CountAsync();

        var records = await query.Skip(queryOptions.Skip).Take(queryOptions.Fetch).SortBy(queryOptions.SortBy).ToListAsync();

        var result = new List<AppRelease>();

        foreach (var record in records)
        {
            var release = new AppRelease
            {
                ReleaseId = record.ReleaseId,
                ReleaseDate = record.Release.ReleaseDate,
                Version = record.Release.Version,
            };

            release.AppDownloads.Add(new AppDownload
            {
                CountryCode = record.CountryCode,
                DownloadId = record.DownloadId,
                ExeDownloadUrl = record.ExeDownloadUrl,
                MsiDownloadUrl = record.MsiDownloadUrl,
                ModelTypeId = record.ModelTypeId,
                ReleaseId = record.ReleaseId,
                ModelType = record.ModelType,
            });

            result.Add(release);
        }

        return (result, totalCount);
    }
}

﻿using Microsoft.EntityFrameworkCore;
using Sanas.Backend.Data.Models;

using Sanas.Backend.Data.Repositories.Interfaces;

namespace Sanas.Backend.Data.Repositories;

public class BpoSitesRepository(PortalDbContext dataContext) : IBpoSitesRepository
{
    public async Task<BpoSite> GetByIdAsync(int bpoSiteId)
    {
        return await dataContext.BpoSites.SingleOrDefaultAsync(bpoSite => bpoSite.BpoSiteId == bpoSiteId);
    }

    public async Task<BpoSite> GetByLookupKeyAsync(string lookupKey)
    {
        return await dataContext.BpoSites.SingleOrDefaultAsync(bpoSite => bpoSite.LookupKey == lookupKey);
    }

    public async Task<BpoSite> CreateAsync(BpoSite bpoSite)
    {
        var newRecord = await dataContext.BpoSites.AddAsync(bpoSite);
        return newRecord.Entity;
    }

    public async Task<BpoSite> GetOrCreateAsync(string bpo, string site)
    {
        var bpoSiteRecord = await dataContext.BpoSites.FirstOrDefaultAsync(bpoSite =>
                EF.Functions.Like(bpoSite.BPO, bpo) && EF.Functions.Like(bpoSite.Site, site));

        if (bpoSiteRecord is null)
        {
            var newBpoSiteRecord = await dataContext.BpoSites.AddAsync(new BpoSite
            {
                BPO = bpo,
                Site = site,
                LookupKey = Guid.NewGuid().ToString(),
            });

            await dataContext.SaveChangesAsync();

            bpoSiteRecord = newBpoSiteRecord.Entity;
        }

        return bpoSiteRecord;
    }
}

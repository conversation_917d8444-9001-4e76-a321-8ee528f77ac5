﻿using Microsoft.EntityFrameworkCore;
using Sanas.Backend.Data.Models;
using Sanas.Backend.Data.Repositories.Interfaces;

namespace Sanas.Backend.Data.Repositories;

public class FeedbackRepository(PortalDbContext databaseContext) : IFeedbackRepository
{
    public async Task<Feedback> CreateFeedbackAsync(Feedback feedback)
    {
        var newRecord = await databaseContext.Feedbacks.AddAsync(new Feedback
        {
            WorkspaceId = feedback.WorkspaceId,
            AgentId = feedback.AgentId,
            AppVersion = feedback.AppVersion,
            Description = feedback.Description,
            SentimentType = feedback.SentimentType,
            AttachmentNumber = feedback.AttachmentNumber,
            TrackingNumber = feedback.TrackingNumber,
            SanasUniqueId = feedback.SanasUniqueId,
            LoginId = feedback.LoginId,
            CreatedUtc = feedback.CreatedUtc
        });

        await databaseContext.SaveChangesAsync();

        return newRecord.Entity;
    }

    public async Task<IList<Feedback>> GetFeedbacksAsync(int workspaceId, int? pageNumber = 0, int? pageSize = 0)
    {
        var query = databaseContext.Feedbacks
       .Where(n => n.WorkspaceId == workspaceId)
       .OrderByDescending(n => n.CreatedUtc);

        if (pageSize > 0 && pageNumber > 0)
        {
            var skip = pageSize * (pageNumber <= 1 ? 0 : (pageNumber - 1)) ?? 0;
            var take = pageSize ?? 0;

            query = (IOrderedQueryable<Feedback>)query.Skip(skip).Take(take);
        }

        var records = await query.ToListAsync();

        return records;
    }

    public async Task<int> GetFeedbacksCountAsync(int workspaceId)
    {
        return await databaseContext.Feedbacks
       .Where(n => n.WorkspaceId == workspaceId)
       .CountAsync();
    }
}
﻿using System.Text.Json;
using Microsoft.EntityFrameworkCore;

namespace Sanas.Backend.Data.Repositories;

public class GenericRepository : IGenericRepository
{
    private readonly PortalDbContext _context;

    public GenericRepository(PortalDbContext context)
    {
        _context = context;
    }

    public async Task<string> GetICPAsync()
    {
        //var query = _context.Database.SqlQuery<string>($"select profile from ideal_customer_profiles");
        //var result = await query.ToListAsync();
        //return result.FirstOrDefault();
        var record = await _context.IdealCustomerProfiles.FirstOrDefaultAsync();

        return record?.Profile;
    }

    public async Task UpdateICPAsync(JsonElement icpChecklist)
    {
        // await _context.Database.ExecuteSqlAsync($"update ideal_customer_profiles set profile = {icpChecklist}");
        var record = await _context.IdealCustomerProfiles.FirstOrDefaultAsync();

        if (record is not null)
        {
            record.Profile = icpChecklist.ToString();
        }
        else
        {
            _context.IdealCustomerProfiles.Add(new Models.IdealCustomerProfile { Profile = icpChecklist.ToString() });
        }

        await _context.SaveChangesAsync();
    }

    public async Task<Dictionary<string, int>> GetModelTypesAsync()
    {
        return await _context.ModelTypes.ToDictionaryAsync(m => m.Name.ToUpperInvariant(), m => m.ModelTypeId);
    }
}

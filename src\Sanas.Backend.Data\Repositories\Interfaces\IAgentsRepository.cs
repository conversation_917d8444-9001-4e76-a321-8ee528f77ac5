﻿using Sanas.Backend.Data.Models;

namespace Sanas.Backend.Data.Repositories.Interfaces;

public interface IAgentsRepository
{
    Task<(IEnumerable<Agent> Agents, int TotalRecords)> GetByTeamIdWithPagingAsync(int teamId, AgentsQueryOptions queryOptions);

    Task<(IEnumerable<Agent> Agents, int TotalRecords)> GetAllAgentsInTeamAsync(int teamId, AgentsQueryOptions queryOptions);

    Task<(IEnumerable<Agent> Agents, int TotalRecords)> GetByWorkspaceIdWithPagingAsync(int workspaceId, QueryOptions queryOptions);

    Task<Agent> GetAgentByIdAsync(int agentId);

    Task<Agent> GetAgentByIdAndWorkspaceIdAsync(int agentId, int workspaceId);
    
    Task<IEnumerable<Agent>> GetAgentsByIdAsync(int workspaceId, IEnumerable<int> agentIds);

    Task<List<string>> GetLoginIdsInWorkspaceAsync(int workspaceId);

    Task<Agent> GetAgentByLoginAsync(string loginId, string workspaceKey);

    Task<bool> UpdateAgentPreferencesAsync(int agentId, string preferenceName, object newValue);

    Task<bool> UpdateAgentLastSeenTime(int agentId, DateTime lastSeenTime);
    
    Task<bool> UpdateAgentLastLoginTime(int agentId, DateTime loginTime);
    
    Task<bool> AgentExistsAsync(int workspaceId, string loginId);

    Task<Agent> UpdateAgentAsync(int agentId, Agent agent);

    Task CreateManyAsync(IEnumerable<Agent> agents);

    Task<Agent> CreateOneAsync(Agent agent);

    Task EnableDisableManyByIdAsync(int workspaceId, IEnumerable<int> agentIds, bool disable);

    Task EnableDisableManyByIdAsync(int workspaceId, IEnumerable<string> loginIds, bool disable);

    Task MoveAgentsToTeamAsync(int workspaceId, IEnumerable<int> agentIds, int destinationTeamId);
    
    Task UpdateICPAsync(int agentId, string icpReport);
}

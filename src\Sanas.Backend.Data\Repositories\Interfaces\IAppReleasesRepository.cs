﻿using Sanas.Backend.Data.Models;

namespace Sanas.Backend.Data.Repositories
{
    public interface IAppReleasesRepository
    {
        Task<AppRelease> GetByReleaseIdAsync(int releaseId); 
        Task CreateAppReleaseAsync(AppRelease appRelease);
        Task UpdateReleaseNotesAsync(int releaseId, string releaseNotes);
        Task<(IEnumerable<AppRelease> AppReleases, int TotalRecords)> GetAppReleasesAsync(AppReleasesQueryOptions queryOptions);
    }
}
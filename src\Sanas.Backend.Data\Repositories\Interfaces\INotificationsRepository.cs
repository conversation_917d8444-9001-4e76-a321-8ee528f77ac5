﻿using Sanas.Backend.Data.Models;
using Sanas.Backend.Data.QueryResultModels;

namespace Sanas.Backend.Data.Repositories
{
    public interface INotificationsRepository
    {
        Task<NotiticationsQueryResult> GetByWorkspaceAsync(int workspaceId, NotificationQueryOptions queryOptions);
        Task<NotiticationsQueryResult> GetByTeamsAsync(int workspaceId, IEnumerable<int> teamIds, NotificationQueryOptions queryOptions);

        Task MarkAsReadAsync(int notificationId, int userId);
        Task MarkAsUnreadAsync(int notificationId, int userId);
        Task SaveNotificationAsync(Notification notification);
        Task MarkAllAsReadAsync(int workspaceId, int userId, IEnumerable<int> types);
    }
}
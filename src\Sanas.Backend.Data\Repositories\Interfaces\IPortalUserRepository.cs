﻿using Sanas.Backend.Data.Models;

namespace Sanas.Backend.Data.Repositories.Interfaces;

public interface IPortalUserRepository
{
    Task<PortalUser> GetByIdAsync(int portalUserId);
    Task<PortalUser> GetByEmailAsync(string email, int workspaceId);
    Task<IList<int>> GetUserWorkspaceIdsAsync(string idpUserId);
    Task<IList<int>> GetUserWorkspaceIdsAsync(int portalUserId, bool includeDisabled);    
    Task<PortalUser> GetByIdpUserIdAsync(string idpUserId);

    Task<(IList<PortalUser> Users, int TotalRecords)> GetByWorkspaceIdsWithPagingAsync(IEnumerable<int> workspaceIds, QueryOptions queryOptions);
    Task<(List<PortalUser> Users, int TotalRecords)> GetByWorkspaceIdWithPagingAsync(int workspaceId, QueryOptions queryOptions);
    Task<(List<PortalUser> Users, int TotalRecords)> GetAllSanasUsersAsync(QueryOptions queryOptions);
    Task<List<PortalUser>> GetAllUsersInWorkspaceAsync(int workspaceId);
    Task<(IList<PortalUserLite> Users, int TotalRecords)> GetByRoleNameAsync(int workspaceId, string roleName, QueryOptions queryOptions);

    Task<PortalUser> CreateSanasUserAsync(PortalUser portalUser, IEnumerable<string> roleNames);
    Task<PortalUser> CreatePortalUserAsync(PortalUser portalUser, int workspaceId, IEnumerable<string> roleNames);

    Task AddUserToWorkspaceAsync(int portalUserId, int workspaceId, string roleName);
    Task UpdateUserProfileAsync(string auth0UserId, string firstName, string lastName);
    Task UpdateNotificationTokenAsync(string auth0UserId, string notificationToken);
    Task EnableDisableWorkspaceUserAsync(int workspaceId, int portalUserId, bool disable);
    Task EnableDisableSanasUserAsync(int portalUserId, bool disable);
    Task<PortalUser> DeletePortalUserAsync(int portalUserId);
    
    Task<IEnumerable<int>> GetAccessibleTeamsAsync(int workspaceId, string idpUserId);
    Task RemoveUserFromWorkspaceAsync(int workspaceId, int portalUserId);
    Task UpdateLastInvitedUtcAsync(int portalUserId, DateTime inviteDateTime);
    Task SetAccountVerifiedAsync(string email);
    Task<PortalUser> GetSanasUsersByIdAsync(int portalUserId);
    Task LinkPortalUserToSanasUsersAsync(PortalUser portalUser, IEnumerable<int> roleIds);
    Task<Dictionary<string, int>> CreateBulkUsersAsync(Dictionary<string, string> emailIdpIdMap);
    Task AssignUsersToWorkspaceAsync(IEnumerable<WorkspaceUser> workspaceUsers);
    Task CreateNCUserTrackingAsync(int userId, int workspaceId);
    Task UpdateLoginCountAsync(int workspaceId, int userId);
}
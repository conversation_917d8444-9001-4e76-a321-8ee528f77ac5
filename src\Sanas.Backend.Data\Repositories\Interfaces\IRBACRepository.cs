﻿using Sanas.Backend.Data.Models;

namespace Sanas.Backend.Data.Repositories;

public interface IRBACRepository
{
    Task<IEnumerable<string>> GetUserRolesAsync(int userId, int workspaceId);
    Task UpdateWorkspaceUserRolesAsync(int userId, int workspaceId, IEnumerable<string> roleNames);
    Task<IEnumerable<Role>> GetAvailableRolesAsync();
    Task<IList<string>> GetWorkspacePermissionsAsync(int workspaceId, int userId);
    Task<IList<string>> GetSanasPermissionsAsync(int userId);
    Task<Dictionary<Role, IEnumerable<string>>> GetSanasRolesAndPermissionsAsync(int userId);
    Task<Dictionary<int, Dictionary<Role, IEnumerable<string>>>> GetWorkspaceScopesAsync(int userId);
    Task<Dictionary<int, IEnumerable<Role>>> GetWorkspaceUsersAndTheirRolesAsync(int workspaceId);
    Task UpdateSanasUserRolesAsync(int userId, IEnumerable<string> roleNames);
}
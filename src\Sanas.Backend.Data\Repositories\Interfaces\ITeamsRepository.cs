﻿using Sanas.Backend.Data.Models;

namespace Sanas.Backend.Data.Repositories.Interfaces;

public interface ITeamsRepository
{
    Task<Team> GetByTeamIdAsync(int workspaceId, int teamId);

    Task<Team> GetByTeamNameAsync(int workspaceId, string teamName);

    Task<Team> CreateTeamAsync(int workspaceId, string teamName, bool isDefaultTeam = false);

    Task<Team> UpdateTeamAsync(int workspaceId, int teamId, string teamName, IEnumerable<int> portalUserIds);

    Task ReactivateTeamAsync(int workspaceId, int teamId);

    Task DisableTeamAsync(int workspaceId, int teamId);

    Task<IEnumerable<Team>> GetAllTeamsInWorkspaceAsync(int workspaceId);
        
    Task<(IEnumerable<Team> Teams, int TotalRecords)> GetByWorkspaceIdWithPagingAsync(int workspaceId, QueryOptions queryOptions);

    Task RemovePortalUsersFromTeamAsync(int workspaceId, int teamId, IEnumerable<int> userIds, bool removeAll);

    Task AssignPortalUsersToTeamAsync(int workspaceId, int teamId, IEnumerable<int> userIds);

    Task<int> GetAgentsCountAsync(int workspaceId, int teamId);
    Task MoveAgentsToTeamAsync(int workspaceId, IEnumerable<int> agentIds, int sourceTeamId, int destinationTeamId);

    Task SetAsDefaultTeamAsync(int workspaceId, int teamId);
    Task<IEnumerable<PortalUser>> GetTeamUsersAsync(int workspaceId, int teamId);
    Task<IEnumerable<Agent>> GetActiveAgentsInTeamAsync(int teamId);

    Task<IEnumerable<PortalUser>> GetSupervisorsInTeamAsync(int workspaceId, int teamId);
    Task RemovePortalUserFromAllTeamsInWorkspaceAsync(int workspaceId, int portalUserId);
    Task<List<Team>> CreateTeamsAsync(int workspaceId, IEnumerable<string> teamNames);
    Task AssignPortalUsersToTeamsAsync(IEnumerable<TeamUser> teamUsers);
}

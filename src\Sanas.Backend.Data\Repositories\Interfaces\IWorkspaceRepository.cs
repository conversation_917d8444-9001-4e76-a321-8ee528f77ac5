﻿using System.Text.Json;
using Sanas.Backend.Data.Models;

namespace Sanas.Backend.Data.Repositories.Interfaces;

public interface IWorkspaceRepository
{
    Task<Workspace> GetByIdAsync(int workspaceId);

    Task<Workspace> GetByWorkspaceKeyAsync(string workspaceKey);

    Task<Workspace> CreateWorkspaceAsync(Workspace workspace);

    Task<Workspace> UpdateWorkspaceAsync(int workspaceId, Workspace workspace);

    Task<Workspace> DeleteWorkspaceAsync(int workspaceId);

    Task<Team> GetDefaultTeamAsync(int workspaceId);

    Task<IEnumerable<Workspace>> GetByUserIdAsync(int portalUserId);

    Task<(IEnumerable<WorkspaceEx> Workspaces, int TotalRecords)> GetAllWithPagingAsync(QueryOptions queryOptions, bool skipAgents);

    Task<(IEnumerable<Workspace> Workspaces, int TotalRecords)> GetByUserIdWithPagingAsync(int portalUserId, QueryOptions queryOptions);
    
    Task ReactivateWorkspaceAsync(int workspaceId);

    Task<bool> ContainsTeam(int workspaceId, int teamId);

    Task<int> GetAgentsCountAsync(int workspaceId);

    Task<Dictionary<string, int>> GetAllTeamNamesWithIdsAsync(int workspaceId);

    Task<(IEnumerable<string> Bpos, int TotalRecords)> GetAllBposAsync(QueryOptions queryOptions);

    Task<(IEnumerable<string> Sites, int TotalRecords)> GetAllSitesAsync(QueryOptions queryOptions);
    
    Task<IEnumerable<PortalUser>> GetUsersByPermissionsAsync(int workspaceId, IEnumerable<string> permissions);
    Task UpdateAutoAppConfigurationAsync(int workspaceId, JsonElement appConfig);
    Task<int> GetLoggedInAgentsCountAsync(int workspaceId);
    Task<string> GetIncrementalEnterpriseNameAsync(string enterprise);
    Task<NCWorkspace> GetNCWorkspaceInfoAsync(int workspaceId);
    Task<Workspace> GetByInstallerId(Guid installerId);
}

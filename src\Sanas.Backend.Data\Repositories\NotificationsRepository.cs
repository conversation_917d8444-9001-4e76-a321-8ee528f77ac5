﻿using Microsoft.EntityFrameworkCore;
using Sanas.Backend.Data.Extensions;
using Sanas.Backend.Data.Models;
using Sanas.Backend.Data.QueryResultModels;

namespace Sanas.Backend.Data.Repositories;

public class NotificationsRepository(PortalDbContext dataContext) : INotificationsRepository
{
    public async Task SaveNotificationAsync(Notification notification)
    {
        dataContext.Notifications.Add(notification);
        await dataContext.SaveChangesAsync();
    }

    public async Task MarkAsReadAsync(int notificationId, int userId)
    {
        var notification = await dataContext.Notifications.FirstOrDefaultAsync(n => n.NotificationId == notificationId);

        if (notification is not null)
        {
            if (!notification.ReadbyUserIds.Contains(userId))
            {
                notification.ReadbyUserIds.Add(userId);
                await dataContext.SaveChangesAsync();
            }
        }
    }

    public async Task MarkAllAsReadAsync(int workspaceId, int userId, IEnumerable<int> types)
    {
        var userNotifications = await dataContext.Notifications
            .Where(n => n.WorkspaceId == workspaceId && (!types.Any() || types.Contains(n.NotificationType)))
            .ToListAsync();

        userNotifications.ForEach(notification =>
        {
            if (!notification.ReadbyUserIds.Contains(userId))
            {
                notification.ReadbyUserIds.Add(userId);
            }
        });

        await dataContext.SaveChangesAsync();
    }

    public async Task MarkAsUnreadAsync(int notificationId, int userId)
    {
        var notification = await dataContext.Notifications.FirstOrDefaultAsync(n => n.NotificationId == notificationId);

        if (notification is not null)
        {
            if (notification.ReadbyUserIds.Contains(userId))
            {
                notification.ReadbyUserIds.Remove(userId);
                await dataContext.SaveChangesAsync();
            }
        }
    }

    public async Task<NotiticationsQueryResult> GetByWorkspaceAsync(int workspaceId, NotificationQueryOptions queryOptions)
    {
        var user = await dataContext.PortalUsers.FirstOrDefaultAsync(p => p.PortalUserId == queryOptions.RequestingUserId);

        var query = dataContext.Notifications
            .Include(n => n.Team)
            .Include(n => n.Agent)
            .AsNoTracking()
            .Where(n => n.WorkspaceId == workspaceId)
            .Where(n => n.CreatedUtc >= user.CreatedUtc);

        var (totalUnread, appEventsUnread, feedbackUnread) = await GetUnreadNotificationCounts(query, user.PortalUserId);

        var totalCount = await query.Filter(queryOptions).CountAsync();
        var records = await query.Filter(queryOptions).OrderByDescending(n => n.CreatedUtc).Skip(queryOptions.Skip).Take(queryOptions.Fetch).ToListAsync();

        return new NotiticationsQueryResult
        {
            Records = records,
            TotalRecords = totalCount,
            TotalUnreadCount = totalUnread,
            UnreadAppEventsCount = appEventsUnread,
            UnreadFeedbacksCount = feedbackUnread,
        };
    }

    public async Task<NotiticationsQueryResult> GetByTeamsAsync(int workspaceId, IEnumerable<int> teamIds, NotificationQueryOptions queryOptions)
    {
        if (teamIds is not null && teamIds.Any())
        {
            var user = await dataContext.PortalUsers.FirstOrDefaultAsync(p => p.PortalUserId == queryOptions.RequestingUserId);

            var query = dataContext.Notifications
                .Include(n => n.Team)
                .Include(n => n.Agent)
                .AsNoTracking()
                .Where(n => n.WorkspaceId == workspaceId)
                .Where(n => n.CreatedUtc >= user.CreatedUtc)
                .Where(n => teamIds.Contains(n.TeamId));

            var (totalUnread, appEventsUnread, feedbackUnread) = await GetUnreadNotificationCounts(query, user.PortalUserId);

            var totalCount = await query.Filter(queryOptions).CountAsync();
            var records = await query.Filter(queryOptions).OrderByDescending(n => n.CreatedUtc).Skip(queryOptions.Skip).Take(queryOptions.Fetch).ToListAsync();

            return new NotiticationsQueryResult
            {
                Records = records,
                TotalRecords = totalCount,
                TotalUnreadCount = totalUnread,
                UnreadAppEventsCount = appEventsUnread,
                UnreadFeedbacksCount = feedbackUnread,
            };
        }

        return new();
    }

    private static async Task<(int Total, int AppEvent, int Feedback)> GetUnreadNotificationCounts(IQueryable<Notification> query, int userId)
    {
        var totalUnread = await query.CountAsync(n => !n.ReadbyUserIds.Contains(userId));
        var unreadAppEvents = await query.Where(n => n.NotificationType != 3).CountAsync(n => !n.ReadbyUserIds.Contains(userId));
        var unreadFeedbacks = await query.Where(n => n.NotificationType == 3).CountAsync(n => !n.ReadbyUserIds.Contains(userId));

        return (totalUnread, unreadAppEvents, unreadFeedbacks);
    }
}

﻿using System.Runtime.CompilerServices;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Microsoft.Extensions.Logging;
using Sanas.Backend.Data.Extensions;
using Sanas.Backend.Data.Models;
using Sanas.Backend.Data.Repositories.Interfaces;

namespace Sanas.Backend.Data.Repositories;

public class PortalUsersRepository(PortalDbContext portalDbContext, ILogger<PortalUsersRepository> logger) : IPortalUserRepository
{
    private readonly PortalDbContext _dbContext = portalDbContext;

    public async Task<PortalUser> CreateSanasUserAsync(PortalUser portalUser, IEnumerable<string> roleNames)
    {
        var roles = await _dbContext.Roles
            .Where(role => roleNames.Contains(role.Name))
            .Select(role => role.RoleId)
            .ToArrayAsync();

        if (roles.Length > 0)
        {
            var newRecord = await _dbContext.PortalUsers.AddAsync(new PortalUser
            {
                FirstName = portalUser.FirstName,
                LastName = portalUser.LastName,
                Email = portalUser.Email,
                IdpUserId = portalUser.IdpUserId,
                IsSanasUser = true,
                InvitedUtc = DateTime.UtcNow
            });

            await _dbContext.SaveChangesAsync();

            await _dbContext.SanasUsers.AddAsync(new SanasUser
            {
                UserId = newRecord.Entity.PortalUserId,
                RoleIds = [.. roles]
            });

            await _dbContext.SaveChangesAsync();

            return newRecord.Entity;
        }

        throw new ArgumentException($"Invalid role name(s) specified - {string.Join(", ", roleNames)}");
    }

    public async Task<PortalUser> CreatePortalUserAsync(PortalUser portalUser, int workspaceId, IEnumerable<string> roleNames)
    {
        var roles = await _dbContext.Roles
           .Where(role => roleNames.Contains(role.Name))
           .Select(role => role.RoleId)
           .ToArrayAsync();

        if (roles.Length > 0)
        {
            var newRecord = await _dbContext.PortalUsers.AddAsync(new PortalUser
            {
                FirstName = portalUser.FirstName,
                LastName = portalUser.LastName,
                Email = portalUser.Email,
                IdpUserId = portalUser.IdpUserId,
                InvitedUtc = DateTime.UtcNow,
                IsAccountVerified = portalUser.IsAccountVerified
            });

            await _dbContext.SaveChangesAsync();

            newRecord.Entity.WorkspaceUsers.Add(new WorkspaceUser
            {
                PortalUserId = newRecord.Entity.PortalUserId,
                WorkspaceId = workspaceId,
                RoleIds = [.. roles]
            });

            await _dbContext.SaveChangesAsync();

            return newRecord.Entity;
        }

        throw new ArgumentException($"Invalid role name specified - {string.Join(", ", roleNames)}");
    }

    public async Task EnableDisableWorkspaceUserAsync(int workspaceId, int portalUserId, bool disable)
    {
        var existingPortalUser = await _dbContext.WorkspaceUsers
            .Where(wu => wu.WorkspaceId == workspaceId)
            .FirstOrDefaultAsync(wu => wu.PortalUserId == portalUserId);

        if (existingPortalUser != null)
        {
            existingPortalUser.IsUserDisabled = disable;
            await _dbContext.SaveChangesAsync();
        }
    }

    public async Task EnableDisableSanasUserAsync(int portalUserId, bool disable)
    {
        var existingPortalUser = await _dbContext.PortalUsers
               .SingleOrDefaultAsync(user => user.PortalUserId == portalUserId);

        if (existingPortalUser != null)
        {
            existingPortalUser.IsDisabled = disable;
            await _dbContext.SaveChangesAsync();
        }
    }

    public async Task<PortalUser> DeletePortalUserAsync(int portalUserId)
    {
        var existingPortalUser = await _dbContext.PortalUsers
               .SingleOrDefaultAsync(user => user.PortalUserId == portalUserId);

        if (existingPortalUser != null)
        {
            _dbContext.PortalUsers.Remove(existingPortalUser);
            await _dbContext.SaveChangesAsync();
        }

        return existingPortalUser;
    }

    public async Task<PortalUser> GetByIdAsync(int portalUserId)
    {
        return await _dbContext.PortalUsers.AsNoTracking()
                .SingleOrDefaultAsync(user => user.PortalUserId == portalUserId);
    }

    public async Task<PortalUser> GetByIdpUserIdAsync(string idpUserId)
    {
        return await _dbContext.PortalUsers.AsNoTracking()
               .SingleOrDefaultAsync(user => user.IdpUserId == idpUserId);
    }

    public async Task<PortalUser> GetByEmailAsync(string email, int workspaceId)
    {
        if (workspaceId > 0)
        {
            return await _dbContext.WorkspaceUsers
                .Include(wu => wu.PortalUser)
                .AsNoTracking()
                .Where(wu => wu.WorkspaceId == workspaceId)
                .Where(wu => EF.Functions.ILike(wu.PortalUser.Email, email))
                .Select(wu => wu.PortalUser)
                .FirstOrDefaultAsync();
        }
        else
        {
            return await _dbContext.PortalUsers
                .AsNoTracking()
                .SingleOrDefaultAsync(user => EF.Functions.ILike(user.Email, email));
        }
    }

    public async Task UpdateUserProfileAsync(string auth0UserId, string firstName, string lastName)
    {
        var existingPortalUser = await _dbContext.PortalUsers
                .SingleOrDefaultAsync(user => user.IdpUserId == auth0UserId && !user.IsDisabled);

        if (existingPortalUser is not null)
        {
            existingPortalUser.FirstName = firstName;
            existingPortalUser.LastName = lastName;

            await _dbContext.SaveChangesAsync();
            return;
        }

        throw new ArgumentException("User not found or disabled");
    }

    public async Task<IEnumerable<int>> GetAccessibleTeamsAsync(int workspaceId, string idpUserId)
    {
        var user = _dbContext.PortalUsers.FirstOrDefault(user => user.IdpUserId == idpUserId);

        var query = from teamUser in _dbContext.TeamUsers
                    join team in _dbContext.Teams on teamUser.TeamId equals team.TeamId
                    where team.WorkspaceId == workspaceId
                    where teamUser.PortalUserId == user.PortalUserId && !team.IsDisabled
                    select team;

        return await query.Select(team => team.TeamId).ToListAsync();
    }

    public async Task<List<PortalUser>> GetAllUsersInWorkspaceAsync(int workspaceId)
    {
        return await _dbContext.WorkspaceUsers
            .Include(wu => wu.PortalUser)
            .AsNoTracking()
            .Where(w => w.WorkspaceId == workspaceId)
            .Select(w => w.PortalUser)
            .ToListAsync();
    }

    public async Task<(List<PortalUser> Users, int TotalRecords)> GetAllSanasUsersAsync(QueryOptions queryOptions)
    {
        var query = _dbContext.PortalUsers
            .Include(p => p.SanasUser)
            .Where(user => user.IsSanasUser)
            .Where(queryOptions.SearchText);

        return (await query.SortBy(queryOptions.SortBy).Skip(queryOptions.Skip).Take(queryOptions.Fetch).ToListAsync(), await query.CountAsync());
    }

    public async Task<(List<PortalUser> Users, int TotalRecords)> GetByWorkspaceIdWithPagingAsync(int workspaceId, QueryOptions queryOptions)
    {
        var query = _dbContext.WorkspaceUsers
            .Include(wu => wu.PortalUser)
            .ThenInclude(p => p.WorkspaceUsers)
            .AsNoTracking()
            .Where(w => w.WorkspaceId == workspaceId)
            .Select(w => w.PortalUser)
            .Where(queryOptions.SearchText);

        if (queryOptions.LoadAll)
        {
            return (await query.SortBy(queryOptions.SortBy).ToListAsync(), await query.CountAsync());
        }
        else
        {
            return (await query.SortBy(queryOptions.SortBy).Skip(queryOptions.Skip).Take(queryOptions.Fetch).ToListAsync(), await query.CountAsync());
        }
    }

    public async Task<(IList<PortalUser> Users, int TotalRecords)> GetByWorkspaceIdsWithPagingAsync(IEnumerable<int> workspaceIds, QueryOptions queryOptions)
    {
        var query = _dbContext.WorkspaceUsers
             .Include(wu => wu.PortalUser)
             .AsNoTracking()
             .Where(wu => !wu.IsUserDisabled && workspaceIds.Contains(wu.WorkspaceId))
             .Select(w => w.PortalUser)
             .Where(queryOptions.SearchText).AsNoTracking();

        return (await query.SortBy(queryOptions.SortBy).Skip(queryOptions.Skip).Take(queryOptions.Fetch).ToListAsync(), await query.CountAsync());
    }

    public async Task<(IList<PortalUserLite> Users, int TotalRecords)> GetByRoleNameAsync(int workspaceId, string roleName, QueryOptions queryOptions)
    {
        var queryString = $@"
                SELECT p.*, r.name as role_name
                FROM portal_users p
                JOIN workspace_users wu ON p.portal_user_id = wu.portal_user_id and not wu.is_user_disabled
                LEFT OUTER JOIN roles r ON r.role_id = ANY(wu.role_ids)
                WHERE wu.workspace_id = {workspaceId} 
                    AND {(string.IsNullOrWhiteSpace(roleName) ? " 1 = 1" : " r.name = '" + roleName + "'")}";

        var query = _dbContext.Database
            .SqlQuery<PortalUserLite>(FormattableStringFactory.Create(queryString))
            .Where(queryOptions.SearchText);

        return (await query.SortBy(queryOptions.SortBy).Skip(queryOptions.Skip).Take(queryOptions.Fetch).ToListAsync(), await query.CountAsync());
    }

    public async Task AddUserToWorkspaceAsync(int portalUserId, int workspaceId, string roleName)
    {
        var role = await _dbContext.Roles.FirstOrDefaultAsync(role => EF.Functions.ILike(role.Name, roleName));

        if (role is not null)
        {
            if (!_dbContext.WorkspaceUsers.Any(wu => wu.PortalUserId == portalUserId && wu.WorkspaceId == workspaceId))
            {
                _dbContext.WorkspaceUsers.Add(new WorkspaceUser
                {
                    PortalUserId = portalUserId,
                    WorkspaceId = workspaceId,
                    RoleIds = [role.RoleId],
                    IsUserDisabled = false
                });

                await _dbContext.SaveChangesAsync();
            }

            return;
        }

        throw new ArgumentException($"Invalid role name - {roleName}");
    }

    public async Task<IList<int>> GetUserWorkspaceIdsAsync(string idpUserId)
    {
        return await _dbContext.WorkspaceUsers
            .Include(wu => wu.PortalUser)
            .Include(wu => wu.Workspace)
            .AsNoTracking()
            .Where(wu => EF.Functions.ILike(wu.PortalUser.IdpUserId, idpUserId))
            .Where(wu => !wu.IsUserDisabled && !wu.Workspace.IsDisabled)
            .Select(w => w.WorkspaceId)
            .ToListAsync();
    }

    public async Task<IList<int>> GetUserWorkspaceIdsAsync(int portalUserId, bool includeDisabled)
    {
        return await _dbContext.WorkspaceUsers
            .Include(wu => wu.PortalUser)
            .Include(wu => wu.Workspace)
            .AsNoTracking()
            .Where(wu => (includeDisabled ? true : !wu.IsUserDisabled) && wu.PortalUserId == portalUserId)
            .Where(wu => !wu.Workspace.IsDisabled)
            .Select(w => w.WorkspaceId)
            .ToListAsync();
    }

    public async Task RemoveUserFromWorkspaceAsync(int workspaceId, int portalUserId)
    {
        var existingRecord = await _dbContext.WorkspaceUsers
            .FirstOrDefaultAsync(wu => wu.PortalUserId == portalUserId && wu.WorkspaceId == workspaceId);

        if (existingRecord is not null)
        {
            _dbContext.WorkspaceUsers.Remove(existingRecord);
            await _dbContext.SaveChangesAsync();
        }
    }

    public async Task UpdateNotificationTokenAsync(string auth0UserId, string notificationToken)
    {
        var existingPortalUser = await _dbContext.PortalUsers
                .SingleOrDefaultAsync(user => user.IdpUserId == auth0UserId && !user.IsDisabled);

        if (existingPortalUser is not null)
        {
            existingPortalUser.NotificationToken = notificationToken;
            await _dbContext.SaveChangesAsync();
            return;
        }

        throw new ArgumentException("User not found or disabled");
    }

    public async Task UpdateLastInvitedUtcAsync(int portalUserId, DateTime inviteDateTime)
    {
        var user = await _dbContext.PortalUsers.FirstOrDefaultAsync(p => p.PortalUserId == portalUserId);
        if (user is not null)
        {
            user.InvitedUtc = inviteDateTime;
            await _dbContext.SaveChangesAsync();
        }
    }

    public async Task SetAccountVerifiedAsync(string email)
    {
        var portalUser = await _dbContext.PortalUsers
            .FirstOrDefaultAsync(u => EF.Functions.ILike(u.Email, email));

        if (portalUser is not null)
        {
            portalUser.IsAccountVerified = true;
            await _dbContext.SaveChangesAsync();
            return;
        }

        throw new ArgumentException($"Failed to set account verified. No user with email {email} found.");
    }

    public async Task<PortalUser> GetSanasUsersByIdAsync(int portalUserId)
    {
        return await _dbContext.SanasUsers
            .Include(sanasUser => sanasUser.User)
            .Where(sanasUser => sanasUser.UserId == portalUserId)
            .Select(sanasUser => sanasUser.User)
            .FirstOrDefaultAsync();
    }

    public async Task LinkPortalUserToSanasUsersAsync(PortalUser portalUser, IEnumerable<int> roleIds)
    {
        try
        {
            var user = await _dbContext.PortalUsers.FirstOrDefaultAsync(u => u.PortalUserId == portalUser.PortalUserId);
            user.FirstName = portalUser.FirstName;
            user.LastName = portalUser.LastName;
            user.IsSanasUser = true;
            await _dbContext.SaveChangesAsync();
        }
        catch (Exception e)
        {
            logger.LogWarning("Skipping profile update due to error. {Error}", e.Message);
        }

        var sanasUser = await _dbContext.SanasUsers.FirstOrDefaultAsync(sanasUser => sanasUser.UserId == portalUser.PortalUserId);

        if (sanasUser is null)
        {
            await _dbContext.SanasUsers.AddAsync(new SanasUser { UserId = portalUser.PortalUserId, RoleIds = [.. roleIds] });
        }
        else
        {
            sanasUser.RoleIds = [.. roleIds];
        }

        await _dbContext.SaveChangesAsync();
    }

    public async Task<Dictionary<string, int>> CreateBulkUsersAsync(Dictionary<string, string> emailIdpIdMap)
    {
        var records = new List<EntityEntry<PortalUser>>();

        var users = emailIdpIdMap.Select(map => new PortalUser
        {
            Email = map.Key,
            IdpUserId = map.Value,
            CreatedUtc = DateTime.UtcNow,
            UpdatedUtc = DateTime.UtcNow,
            FirstName = map.Key,
            LastName = null,
            InvitedUtc = DateTime.UtcNow,
            IsDisabled = false
        });

        foreach (var user in users)
        {
            var record = _dbContext.PortalUsers.Add(user);
            records.Add(record);
        }

        await _dbContext.SaveChangesAsync();

        return records.ToDictionary(r => r.Entity.Email, r => r.Entity.PortalUserId);
    }

    public async Task AssignUsersToWorkspaceAsync(IEnumerable<WorkspaceUser> workspaceUsers)
    {
        _dbContext.WorkspaceUsers.AddRange(workspaceUsers);
        await _dbContext.SaveChangesAsync();
    }

    public async Task CreateNCUserTrackingAsync(int userId, int workspaceId)
    {
        await _dbContext.NcworkspaceUserTrackings.AddAsync(new NcworkspaceUserTracking
        {
            UserId = userId,
            WorkspaceId = workspaceId,
            StepsCompleted = [],
            LoginCount = 0
        });

        await _dbContext.SaveChangesAsync();
    }

    public async Task UpdateLoginCountAsync(int workspaceId, int userId)
    {
        var record = await _dbContext.NcworkspaceUserTrackings.FirstOrDefaultAsync(t => t.WorkspaceId == workspaceId && t.UserId == userId);
        if (record is not null)
        {
            record.LoginCount += 1;
            await _dbContext.SaveChangesAsync();
        }
    }
}

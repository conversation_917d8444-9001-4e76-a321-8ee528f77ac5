﻿using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Sanas.Backend.Data.Models;

namespace Sanas.Backend.Data.Repositories;

public class RBACRepository(ILogger<RBACRepository> logger, PortalDbContext dataContext) : IRBACRepository
{
    /*
    Task UpdateUserRolesAsync(string userId, int workspaceId, IEnumerable<string> roleIds);
    Task<Dictionary<string, UserWithRoles>> GetUsersWithRoles(IEnumerable<string> userIds, int workspaceId);
    Task<IEnumerable<string>> GetUserRolesAsync(string userId, int workspaceId);
    Task<IEnumerable<string>> GetUserPermissionsAsync(string userId, int workspaceId, bool isSanasUser);
    Task<IEnumerable<Role>> GetAvailableRolesAsync();
    Task<IEnumerable<Permission>> GetAvailablePermissionsAsync();*/

    public async Task UpdateWorkspaceUserRolesAsync(int userId, int workspaceId, IEnumerable<string> roleNames)
    {
        var roleIds = await dataContext.Roles
            .Where(role => roleNames.Contains(role.Name))
            .Select(role => role.RoleId)
            .ToListAsync();

        if (roleIds.Count == roleNames.Count())
        {
            var existingRecord = await dataContext.WorkspaceUsers
                .FirstOrDefaultAsync(wu => wu.PortalUserId == userId && wu.WorkspaceId == workspaceId);

            if (existingRecord is not null)
            {
                existingRecord.RoleIds = [.. roleIds];

                await dataContext.SaveChangesAsync();
                logger.LogInformation("Successfull updated the role of user {userId} to {roleName} in workspace {workspaceId}", userId, string.Join(",", roleNames), workspaceId);
                return;
            }

            logger.LogError("Failed to update user role(s). UserId {UserId} not found in workspace {workspaceId}", userId, workspaceId);
            throw new ArgumentException("Workspace user not found");
        }

        logger.LogError("Failed to update user role(s). One more invalid role names specified");
        throw new ArgumentException("One or more role names are invalid");
    }

    public async Task<IEnumerable<string>> GetUserRolesAsync(int userId, int workspaceId)
    {
        var workspaceUser = await dataContext.WorkspaceUsers
            .AsNoTracking()
            .FirstOrDefaultAsync(wu => !wu.IsUserDisabled && wu.PortalUserId == userId && wu.WorkspaceId == workspaceId);

        if (workspaceUser is not null)
        {
            return await dataContext.Roles
                .Where(role => workspaceUser.RoleIds.Contains(role.RoleId))
                .Select(role => role.Name)
                .ToListAsync();
        }

        return [];
    }

    public async Task<IEnumerable<Role>> GetAvailableRolesAsync()
    {
        return await dataContext.Roles.ToListAsync();
    }

    public async Task<IList<string>> GetWorkspacePermissionsAsync(int workspaceId, int userId)
    {
        if (workspaceId > 0 && userId > 0)
        {
            var workspaceUser = await dataContext.WorkspaceUsers
            .AsNoTracking()
            .FirstOrDefaultAsync(wu => !wu.IsUserDisabled && wu.PortalUserId == userId && wu.WorkspaceId == workspaceId);

            if (workspaceUser is not null)
            {
                var workspacePermissions = await dataContext.RolePermissions
                    .Include(r => r.Permission)
                    .Where(r => workspaceUser.RoleIds.Contains(r.RoleId))
                    .Select(r => r.Permission.Name)
                    .ToListAsync();

                return workspacePermissions;
            }
        }

        return [];
    }

    public async Task<IList<string>> GetSanasPermissionsAsync(int userId)
    {
        var sanasUser = await dataContext.SanasUsers
            .FirstOrDefaultAsync(u => u.UserId == userId);

        if (sanasUser is not null)
        {
            var sanasPermissions = await dataContext.RolePermissions
                .Include(r => r.Permission)
                .Where(r => sanasUser.RoleIds.Contains(r.RoleId))
                .Select(r => r.Permission.Name)
                .ToListAsync();

            return sanasPermissions;
        }

        return [];
    }

    public async Task<Dictionary<Role, IEnumerable<string>>> GetSanasRolesAndPermissionsAsync(int userId)
    {
        var sanasUser = await dataContext.SanasUsers.AsNoTracking().FirstOrDefaultAsync(u => u.UserId == userId);

        if (sanasUser is not null)
        {
            var rolePermissions = await dataContext.Roles
                .Include(r => r.RolePermissions)
                .ThenInclude(rp => rp.Permission)
                .Where(r => sanasUser.RoleIds.Contains(r.RoleId))
                .Select(r => new { Role = r, RolePermissions = r.RolePermissions.Select(rp => rp.Permission.Name) })
                .ToDictionaryAsync(r => r.Role, r => r.RolePermissions);

            return rolePermissions;
        }

        return [];
    }

    public async Task<Dictionary<int, Dictionary<Role, IEnumerable<string>>>> GetWorkspaceScopesAsync(int userId)
    {
        var result = new Dictionary<int, Dictionary<Role, IEnumerable<string>>>();

        var userWorkspaces = await dataContext.WorkspaceUsers
            .AsNoTracking()
            .Where(wu => wu.PortalUserId == userId && !wu.IsUserDisabled)
            .ToListAsync();

        if (userWorkspaces.Count > 0)
        {
            foreach (var userWorkspace in userWorkspaces)
            {
                var rolePermissions = await dataContext.Roles
                .Include(r => r.RolePermissions)
                .ThenInclude(rp => rp.Permission)
                .Where(r => userWorkspace.RoleIds.Contains(r.RoleId))
                .Select(r => new { Role = r, RolePermissions = r.RolePermissions.Select(rp => rp.Permission.Name) })
                .ToDictionaryAsync(r => r.Role, r => r.RolePermissions);

                result.Add(userWorkspace.WorkspaceId, rolePermissions);
            }
        }

        return result;
    }

    public async Task<Dictionary<int, IEnumerable<Role>>> GetWorkspaceUsersAndTheirRolesAsync(int workspaceId)
    {
        var result = new Dictionary<int, IEnumerable<Role>>();

        var usersAndRoles = await dataContext.WorkspaceUsers
            .AsNoTracking()
            .Where(wu => wu.WorkspaceId == workspaceId)
            .ToListAsync();

        var roles = await dataContext.Roles
            .ToListAsync();

        foreach (var item in usersAndRoles)
        {
            var roleNames = roles.Where(role => item.RoleIds.Contains(role.RoleId));
            result.Add(item.PortalUserId, roleNames);
        }

        return result;
    }

    public async Task UpdateSanasUserRolesAsync(int userId, IEnumerable<string> roleNames)
    {
        var roleIds = await dataContext.Roles
            .Where(role => roleNames.Contains(role.Name))
            .Select(role => role.RoleId)
            .ToListAsync();

        if (roleIds.Count == roleNames.Count())
        {
            var existingRecord = await dataContext.SanasUsers
                .FirstOrDefaultAsync(su => su.UserId == userId);

            if (existingRecord is not null)
            {
                existingRecord.RoleIds = [.. roleIds];

                await dataContext.SaveChangesAsync();
                logger.LogInformation("Successfull updated the role of Sanas user {userId} to {roleName}", userId, string.Join(",", roleNames));
                return;
            }

            logger.LogError("Failed to update Sanas user role(s). UserId {UserId} not found", userId);
            throw new ArgumentException("Sanas user not found");
        }

        logger.LogError("Failed to update Sanas user role(s). One more invalid role names specified");
        throw new ArgumentException("One or more role names are invalid");
    }
}

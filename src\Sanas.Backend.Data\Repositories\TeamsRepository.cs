﻿using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.ChangeTracking;
using Sanas.Backend.Data.Extensions;
using Sanas.Backend.Data.Models;
using Sanas.Backend.Data.Repositories.Interfaces;

namespace Sanas.Backend.Data.Repositories;

public class TeamsRepository(PortalDbContext dataContext) : ITeamsRepository
{
    public async Task<Team> CreateTeamAsync(int workspaceId, string teamName, bool isDefaultTeam = false)
    {
        var record = await dataContext.Teams.AddAsync(new Team
        {
            Name = teamName.Trim(),
            WorkspaceId = workspaceId,
            IsDefaultTeam = isDefaultTeam
        });

        await dataContext.SaveChangesAsync();

        return record.Entity;
    }

    public async Task<List<Team>> CreateTeamsAsync(int workspaceId, IEnumerable<string> teamNames)
    {
        var records = new List<EntityEntry<Team>>();

        foreach (var teamName in teamNames)
        {
            var record = await dataContext.Teams.AddAsync(new Team
            {
                Name = teamName.Trim(),
                WorkspaceId = workspaceId
            });

            records.Add(record);
        }

        await dataContext.SaveChangesAsync();

        return records.Select(record => record.Entity).ToList();
    }

    public async Task<Team> GetDefaultTeamAsync(int workspaceId)
    {
        return await dataContext.Teams
            .AsNoTracking()
            .FirstOrDefaultAsync(team => team.IsDefaultTeam && team.WorkspaceId == workspaceId);
    }

    public async Task DisableTeamAsync(int workspaceId, int teamId)
    {
        var team = await dataContext.Teams
            .Include(team => team.Workspace)
            .Include(team => team.Agents)
            .Where(team => team.TeamId == teamId && team.WorkspaceId == workspaceId)
            .Where(team => !team.Workspace.IsDisabled)
            .FirstOrDefaultAsync();

        if (team is not null)
        {
            if (team.IsDefaultTeam)
            {
                throw new ArgumentException("Default team cannot be disabled");
            }

            team.IsDisabled = true;

            await dataContext.SaveChangesAsync();
        }
        else
        {
            throw new ArgumentException("Team not found");
        }
    }

    public async Task ReactivateTeamAsync(int workspaceId, int teamId)
    {
        var team = await dataContext.Teams
            .Include(team => team.Workspace)
            .Where(team => team.TeamId == teamId && team.WorkspaceId == workspaceId)
            .Where(team => !team.Workspace.IsDisabled)
            .FirstOrDefaultAsync();

        if (team is not null)
        {
            team.IsDisabled = false;
            await dataContext.SaveChangesAsync();
        }
        else
        {
            throw new ArgumentException("Team not found");
        }
    }

    public async Task MoveAgentsToTeamAsync(int workspaceId, IEnumerable<int> agentIds, int sourceTeamId, int destinationTeamId)
    {
        if (sourceTeamId == destinationTeamId)
        {
            return;
        }

        var sourceTeam = await dataContext.Teams.SingleOrDefaultAsync(team => team.TeamId == sourceTeamId);
        var destinationTeam = await dataContext.Teams.SingleOrDefaultAsync(team => team.TeamId == destinationTeamId);

        if (sourceTeam is null || destinationTeam is null ||
            (sourceTeam.WorkspaceId == workspaceId && destinationTeam.WorkspaceId == workspaceId))
        {
            throw new ArgumentException("Either source or destination team not found.");
        }

        await dataContext.Agents
            .Include(agent => agent.Workspace)
            .Where(agent => agentIds.Contains(agent.AgentId))
            .Where(agent => agent.TeamId == sourceTeamId)
            .ForEachAsync(agent => agent.TeamId = destinationTeamId);

        await dataContext.SaveChangesAsync();
    }

    public async Task<Team> UpdateTeamAsync(int workspaceId, int teamId, string teamName, IEnumerable<int> portalUserIds)
    {
        var record = await dataContext.Teams
            .Include(team => team.Workspace)
            .Where(team => team.TeamId == teamId && team.WorkspaceId == workspaceId)
            .Where(team => !team.Workspace.IsDisabled)
            .FirstOrDefaultAsync();

        if (record is not null)
        {
            if (await dataContext.Teams.AnyAsync(team => team.WorkspaceId == record.WorkspaceId && team.TeamId != teamId && EF.Functions.ILike(team.Name.Trim(), teamName.Trim())))
            {
                throw new ArgumentException($"Team name \"{teamName}\" is already in use");
            }

            record.Name = teamName.Trim();
            await dataContext.SaveChangesAsync();

            if (portalUserIds is null)
            {
                return record;
            }

            if (!portalUserIds.Any())
            {
                await RemovePortalUsersFromTeamAsync(workspaceId, teamId, null, removeAll: true);
                return record;
            }

            var teamUserIds = await dataContext.TeamUsers
                .Where(tu => tu.TeamId == teamId)
                .Select(tu => tu.PortalUserId)
                .ToListAsync();

            var usersToDelete = teamUserIds.Except(portalUserIds);
            var usersToAdd = portalUserIds.Except(teamUserIds);

            if (usersToDelete.Any())
            {
                await RemovePortalUsersFromTeamAsync(workspaceId, teamId, usersToDelete, removeAll: false);
            }

            if (usersToAdd.Any())
            {
                await AssignPortalUsersToTeamAsync(workspaceId, teamId, usersToAdd);
            }

            return record;
        }
        else
        {
            throw new ArgumentException("Team not found");
        }
    }

    public async Task<Team> GetByTeamIdAsync(int workspaceId, int teamId)
    {
        return await dataContext.Teams
            .Include(team => team.Workspace)
            .AsNoTracking()
            .Where(team => team.TeamId == teamId && team.WorkspaceId == workspaceId)
            .Where(team => !team.Workspace.IsDisabled)
            .FirstOrDefaultAsync();
    }

    public async Task<Team> GetByTeamNameAsync(int workspaceId, string teamName)
    {
        return await dataContext.Teams
            .Include(team => team.Workspace)
            .AsNoTracking()
            .Where(team => EF.Functions.ILike(team.Name.Trim(), teamName.Trim()) && team.WorkspaceId == workspaceId)
            .FirstOrDefaultAsync(team => team.WorkspaceId == workspaceId);
    }

    public async Task<IEnumerable<Team>> GetAllTeamsInWorkspaceAsync(int workspaceId)
    {
        return await dataContext.Teams
            .AsNoTracking()
            .Where(team => team.WorkspaceId == workspaceId)
            .ToListAsync();
    }

    public async Task<(IEnumerable<Team> Teams, int TotalRecords)> GetByWorkspaceIdWithPagingAsync(int workspaceId, QueryOptions queryOptions)
    {
        var query = dataContext.Teams
            .AsNoTracking()
            .Where(team => team.WorkspaceId == workspaceId)
            .Where(queryOptions.SearchText);
        var result = await query.SortBy(queryOptions.SortBy).Skip(queryOptions.Skip).Take(queryOptions.Fetch).ToListAsync();
        var totalCount = await query.CountAsync();
        return (result, totalCount);
    }

    public async Task<IEnumerable<PortalUser>> GetSupervisorsInTeamAsync(int workspaceId, int teamId)
    {
        var workspaceUserIds = await dataContext.WorkspaceUsers
            .Where(wu => wu.WorkspaceId == workspaceId && !wu.IsUserDisabled)
            .Select(wu => wu.PortalUserId)
            .ToArrayAsync();

        return await dataContext.TeamUsers
            .Where(item => item.TeamId == teamId)
            .Where(item => workspaceUserIds.Contains(item.PortalUserId))
            .Select(item => new PortalUser
            {
                PortalUserId = item.PortalUserId,
                FirstName = item.PortalUser.FirstName,
                LastName = item.PortalUser.LastName,
                Email = item.PortalUser.Email,
                IsDisabled = item.PortalUser.IsDisabled,
            }).ToListAsync();
    }

    public async Task AssignPortalUsersToTeamAsync(int workspaceId, int teamId, IEnumerable<int> userIds)
    {
        if (await dataContext.Teams
            .Include(team => team.Workspace)
            .AsNoTracking()
            .Where(team => team.TeamId == teamId && team.WorkspaceId == workspaceId)
            .Where(team => !team.Workspace.IsDisabled)
            .FirstOrDefaultAsync() is null)
        {
            throw new ArgumentException("Team not found. Check if it belong to the specified workspace or if the workspace is disabled.");
        }

        var teamUsers = await dataContext.TeamUsers
            .AsNoTracking()
            .Where(tu => tu.TeamId == teamId)
            .ToListAsync();

        foreach (var userId in userIds)
        {
            if (!teamUsers.Any(tu => tu.PortalUserId == userId))
            {
                dataContext.TeamUsers.Add(new TeamUser() { PortalUserId = userId, TeamId = teamId });
            }
        }

        await dataContext.SaveChangesAsync();
    }

    public async Task RemovePortalUsersFromTeamAsync(int workspaceId, int teamId, IEnumerable<int> userIds, bool removeAll)
    {
        var teamUsers = await dataContext.TeamUsers
            .Include(teamUser => teamUser.Team)
            .Where(tu => tu.TeamId == teamId && tu.Team.WorkspaceId == workspaceId)
            .ToListAsync();

        if (removeAll)
        {
            foreach (var teamUser in teamUsers)
            {
                dataContext.TeamUsers.Remove(teamUser);
            }
        }
        else
        {
            foreach (var userId in userIds)
            {
                if (teamUsers.FirstOrDefault(tu => tu.PortalUserId == userId) is TeamUser teamUser)
                {
                    dataContext.TeamUsers.Remove(teamUser);
                }
            }
        }

        await dataContext.SaveChangesAsync();
    }

    public async Task RemovePortalUserFromAllTeamsInWorkspaceAsync(int workspaceId, int portalUserId)
    {
        var assignedTeams = await dataContext.TeamUsers
            .Where(tu => tu.PortalUserId == portalUserId)
            .ToArrayAsync();

        dataContext.TeamUsers.RemoveRange(assignedTeams);
        await dataContext.SaveChangesAsync();
    }

    public async Task<int> GetAgentsCountAsync(int workspaceId, int teamId)
    {
        return await dataContext.Agents
            .Where(agent => !agent.IsDisabled && agent.TeamId == teamId)
            .CountAsync();
    }

    public async Task SetAsDefaultTeamAsync(int workspaceId, int teamId)
    {
        var team = await dataContext.Teams
            .FirstOrDefaultAsync(team => team.WorkspaceId == workspaceId && team.TeamId == teamId && !team.IsDisabled);

        if (team is null)
        {
            throw new ArgumentException("Team not found or disabled.");
        }

        var teamsInWorkspace = await dataContext.Teams.Where(team => team.WorkspaceId == workspaceId).ToListAsync();

        teamsInWorkspace.ForEach(team => team.IsDefaultTeam = team.TeamId == teamId);

        await dataContext.SaveChangesAsync();
    }

    public async Task<IEnumerable<PortalUser>> GetTeamUsersAsync(int workspaceId, int teamId)
    {
        var workspaceUserIds = await dataContext.WorkspaceUsers
            .Where(wu => wu.WorkspaceId == workspaceId)
            .Where(wu => !wu.IsUserDisabled)
            .Select(wu => wu.PortalUserId)
            .ToArrayAsync();

        return await dataContext.TeamUsers
            .Include(tu => tu.PortalUser)
            .Where(tu => tu.TeamId == teamId)
            .Where(tu => workspaceUserIds.Contains(tu.PortalUserId))
            .Where(tu => !tu.PortalUser.IsDisabled)
            .Select(tu => tu.PortalUser)
            .ToListAsync();
    }

    public async Task<IEnumerable<Agent>> GetActiveAgentsInTeamAsync(int teamId)
    {
        return await dataContext.Agents
            .Include(agent => agent.AgentPreferences)
            .Where(agent => agent.TeamId == teamId && !agent.IsDisabled)
            .ToListAsync();
    }

    public async Task AssignPortalUsersToTeamsAsync(IEnumerable<TeamUser> teamUsers)
    {
        /*var assignments = teamUsersMap.Select(map => new TeamUser
        {
            PortalUserId = map.PortalUserId,
            TeamId = map.TeamId
        });*/

        await dataContext.TeamUsers.AddRangeAsync(teamUsers);

        await dataContext.SaveChangesAsync();
    }
}

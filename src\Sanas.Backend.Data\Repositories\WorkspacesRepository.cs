﻿using System.Text.Json;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using Sanas.Backend.Data.Extensions;
using Sanas.Backend.Data.Models;
using Sanas.Backend.Data.Repositories.Interfaces;
namespace Sanas.Backend.Data.Repositories;

public class WorkspacesRepository(
    ILogger<WorkspacesRepository> logger,
    PortalDbContext dataContext,
    IBpoSitesRepository bpoSitesRepository) : IWorkspaceRepository
{
    public async Task<Workspace> CreateWorkspaceAsync(Workspace workspace)
    {
        if (dataContext.Workspaces.Include(w => w.BpoSite)
            .Any(w =>
                    EF.Functions.Like(w.Enterprise, workspace.Enterprise) &&
                    EF.Functions.Like(w.BpoSite.BPO, workspace.BpoSite.BPO) &&
                    EF.Functions.Like(w.BpoSite.Site, workspace.BpoSite.Site))
                )
        {
            throw new ArgumentException($"Another workspace exists with given enterprise, bpo and site combination.");
        }

        var bpoSite = await bpoSitesRepository.GetOrCreateAsync(workspace.BpoSite.BPO, workspace.BpoSite.Site);

        var result = await dataContext.Workspaces.AddAsync(new()
        {
            WorkspaceKey = await GenerateWorkspaceKeyAsync(workspace.Enterprise, workspace.BpoSite.BPO, workspace.BpoSite.Site),
            Enterprise = workspace.Enterprise,
            AllocatedSeats = workspace.AllocatedSeats,
            BpoSiteId = bpoSite.BpoSiteId,
            CountryCode = workspace.CountryCode,
            ModelTypes = workspace.ModelTypes
        });

        await dataContext.SaveChangesAsync();

        return result.Entity;
    }

    public async Task<Workspace> UpdateWorkspaceAsync(int workspaceId, Workspace workspace)
    {
        if (dataContext.Workspaces.Include(w => w.BpoSite)
            .Any(w =>
                    w.WorkspaceId != workspaceId &&
                    EF.Functions.Like(w.Enterprise, workspace.Enterprise) &&
                    EF.Functions.Like(w.BpoSite.BPO, workspace.BpoSite.BPO) &&
                    EF.Functions.Like(w.BpoSite.Site, workspace.BpoSite.Site))
                )
        {
            throw new ArgumentException($"Another workspace exists with given enterprise, bpo and site combination.");
        }

        var existingWorkspace = await dataContext.Workspaces.FirstOrDefaultAsync(w => w.WorkspaceId == workspaceId && !w.IsDisabled);

        if (existingWorkspace is not null)
        {
            var bpoSite = await bpoSitesRepository.GetOrCreateAsync(workspace.BpoSite.BPO, workspace.BpoSite.Site);
            var oldBpoSiteId = existingWorkspace.BpoSiteId;
            existingWorkspace.Enterprise = workspace.Enterprise;
            existingWorkspace.CountryCode = workspace.CountryCode;
            existingWorkspace.ModelTypes = workspace.ModelTypes;
            existingWorkspace.BpoSiteId = bpoSite.BpoSiteId;
            existingWorkspace.AllocatedSeats = workspace.AllocatedSeats;

            await dataContext.SaveChangesAsync();

            if (existingWorkspace.BpoSiteId != oldBpoSiteId)
            {
                CheckAndRemoveBpoSite(oldBpoSiteId);
            }

            return existingWorkspace;
        }
        else
        {
            throw new ArgumentException($"Workspace not found or disabled.");
        }
    }

    private async void CheckAndRemoveBpoSite(int bpoSiteId)
    {
        BpoSite bpoSite = default;

        try
        {
            bpoSite = await dataContext.BpoSites.FirstOrDefaultAsync(b => b.BpoSiteId == bpoSiteId);

            if (bpoSite != null && !dataContext.Workspaces.Any(w => w.BpoSiteId == bpoSiteId))
            {
                dataContext.BpoSites.Remove(bpoSite);
                await dataContext.SaveChangesAsync();

                logger.LogInformation($"Dead record [{bpoSite.BPO},{bpoSite.Site}] has been removed from BpoSites table.");
            }
        }
        catch (Exception e)
        {
            logger.LogWarning($"An error occurred while removing dead record [{bpoSite.BPO},{bpoSite.Site}] from BpoSites table. {{Error}}", e.ToString());
        }
    }

    public async Task<Workspace> DeleteWorkspaceAsync(int workspaceId)
    {
        var record = await dataContext.Workspaces
            .FirstOrDefaultAsync(w => w.WorkspaceId == workspaceId);

        if (record is not null)
        {
            record.IsDisabled = true;
            await dataContext.SaveChangesAsync();
        }

        return record;
    }

    public async Task<Workspace> GetByIdAsync(int workspaceId)
    {
        return await dataContext.Workspaces
            .Include(workspace => workspace.BpoSite)
            .Include(w => w.Agents.Where(a => !a.IsDisabled))
            .AsNoTracking()
            .SingleOrDefaultAsync(workspace => workspace.WorkspaceId == workspaceId);
    }
    
    public async Task<Workspace> GetByWorkspaceKeyAsync(string workspaceKey)
    {
        return await dataContext.Workspaces
            .Include(workspace => workspace.BpoSite)
            .Include(w => w.Agents.Where(a => !a.IsDisabled))
            .AsNoTracking()
            .SingleOrDefaultAsync(workspace => workspace.WorkspaceKey == workspaceKey.ToUpper());
    }

    public async Task<Team> GetDefaultTeamAsync(int workspaceId)
    {
        return await dataContext.Teams
            .AsNoTracking()
            .Where(team => team.WorkspaceId == workspaceId)
            .SingleOrDefaultAsync(team => team.IsDefaultTeam);
    }

    public async Task<(IEnumerable<Workspace> Workspaces, int TotalRecords)> GetByUserIdWithPagingAsync(int portalUserId, QueryOptions queryOptions)
    {
        var query = dataContext.Workspaces
            // .Include(w => w.WorkspaceUsers.Where(wu => wu.PortalUserId == portalUserId && !wu.IsUserDisabled))
            .Include(w => w.BpoSite)
            .Include(w => w.Agents.Where(a => !a.IsDisabled))
            .AsNoTracking()
            .Where(w => !w.IsDisabled)
            .Where(w => w.WorkspaceUsers.Any(wu => wu.PortalUserId == portalUserId && !wu.IsUserDisabled))
            .Where(queryOptions.SearchText);

        /*var query = dataContext.WorkspaceUsers
            .Include(wu => wu.Workspace)
            .ThenInclude(workspace => workspace.Agents.Where(agent => !agent.IsDisabled))
            .ThenInclude(w => w.BpoSite)
            .AsNoTracking()
            .Where(wu => !wu.IsUserDisabled && wu.PortalUserId == portalUserId && !wu.Workspace.IsDisabled)
            .Select(wu => wu.Workspace)
            .Where(queryOptions.SearchText);*/

        return (await query.SortBy(queryOptions.SortBy).Skip(queryOptions.Skip).Take(queryOptions.Fetch).ToListAsync(), await query.CountAsync());
    }

    /*public async Task<(IEnumerable<Workspace> Workspaces, int TotalRecords)> GetAllWithPagingAsync(QueryOptions queryOptions, bool skipAgents)
    {
        var query = dataContext.Workspaces
            .Include(workspace => workspace.BpoSite)
            .AsNoTracking();

        if (!skipAgents)
        {
            query = query.Include(workspace => workspace.Agents.Where(agent => !agent.IsDisabled))
            .AsNoTracking();
        }

        query = query.Where(queryOptions.SearchText);

        var pagedResult = await query.SortBy(queryOptions.SortBy).Skip(queryOptions.Skip).Take(queryOptions.Fetch).ToListAsync();
        var totalRecordCount = await query.CountAsync();

        return (pagedResult, totalRecordCount);
    }*/

    public async Task<(IEnumerable<WorkspaceEx> Workspaces, int TotalRecords)> GetAllWithPagingAsync(QueryOptions queryOptions, bool skipAgents)
    {
        var query = dataContext.Workspaces
            .Include(workspace => workspace.BpoSite)
            .Where(queryOptions.SearchText)
            .AsNoTracking();

        var totalRecordCount = await query.CountAsync();

        var result = await query
            .SortBy(queryOptions.SortBy)
            .Skip(queryOptions.Skip)
            .Take(queryOptions.Fetch)
            .Select(workspace => new WorkspaceEx
            {
                ActivationMode = workspace.ActivationMode,
                WorkspaceId = workspace.WorkspaceId,
                BpoSite = workspace.BpoSite,
                AllocatedSeats = workspace.AllocatedSeats,
                AppConfig = workspace.AppConfig,
                BpoSiteId = workspace.BpoSiteId,
                Enterprise = workspace.Enterprise,
                IsDisabled = workspace.IsDisabled,
                ModelTypes = workspace.ModelTypes,
                CountryCode = workspace.CountryCode,
                CreatedUtc = workspace.CreatedUtc,
                UpdatedUtc = workspace.UpdatedUtc,
                WorkspaceKey = workspace.WorkspaceKey,
                InstallerId = workspace.InstallerId,
                AgentCount = skipAgents ? 0 : dataContext.Agents
                    .AsNoTracking()
                    .Where(agent => agent.WorkspaceId == workspace.WorkspaceId && !agent.IsDisabled)
                    .Count()
            })
            .ToListAsync();

        return (result, totalRecordCount);
    }

    public async Task<IEnumerable<Workspace>> GetByUserIdAsync(int portalUserId)
    {
        var query = dataContext.WorkspaceUsers
            .Include(wu => wu.Workspace)
            .ThenInclude(w => w.BpoSite)
            .AsNoTracking()
            .Where(wu => wu.PortalUserId == portalUserId && !wu.IsUserDisabled)
            .Select(wu => wu.Workspace);

        return await query.ToListAsync();
    }

    public async Task ReactivateWorkspaceAsync(int workspaceId)
    {
        var record = await dataContext.Workspaces
            .FirstOrDefaultAsync(workspace => workspace.WorkspaceId == workspaceId);

        if (record is not null)
        {
            record.IsDisabled = false;
            await dataContext.SaveChangesAsync();
        }
        else
        {
            throw new ArgumentException("Workspace not found");
        }
    }

    public async Task<string> GenerateWorkspaceKeyAsync(string enterprise, string bpo, string site)
    {
        var prefix = $"{enterprise.ToUpperInvariant()[0]}{bpo.Trim('_')[0]}{site.Trim('_')[0]}";

        var suffixes = await dataContext.Workspaces
            .Where(workspace => workspace.WorkspaceKey.StartsWith(prefix))
            .Select(workspace => int.Parse(workspace.WorkspaceKey.Substring(3)))
            .ToListAsync();

        var workspaceKey = $"{prefix}001";

        if (suffixes.Count > 0)
        {
            workspaceKey = prefix + (suffixes.Max() + 1).ToString("d3");
        }

        return workspaceKey;
    }

    public async Task<bool> ContainsTeam(int workspaceId, int teamId)
    {
        return await dataContext.Teams
            .AnyAsync(team => !team.IsDisabled && team.WorkspaceId == workspaceId && team.TeamId == teamId);
    }

    public async Task<int> GetAgentsCountAsync(int workspaceId)
    {
        return await dataContext
            .Agents.Where(a => !a.IsDisabled && a.WorkspaceId == workspaceId && !a.Team.IsDisabled)
            .CountAsync();
    }

    public async Task<int> GetLoggedInAgentsCountAsync(int workspaceId)
    {
        return await dataContext
            .Agents.Where(a => !a.IsDisabled && !a.Team.IsDisabled && a.WorkspaceId == workspaceId && ((a.LastLoginUtc > a.LastSeenUtc) || (a.LastLoginUtc.HasValue && !a.LastSeenUtc.HasValue)))
            .CountAsync();
    }

    public async Task<Dictionary<string, int>> GetAllTeamNamesWithIdsAsync(int workspaceId)
    {
        return await dataContext.Teams
            .AsNoTracking()
            .Where(team => team.WorkspaceId == workspaceId && !team.IsDisabled)
            .ToDictionaryAsync(team => team.Name.ToLower(), team => team.TeamId);
    }

    public async Task<(IEnumerable<string> Bpos, int TotalRecords)> GetAllBposAsync(QueryOptions queryOptions)
    {
        var query = dataContext.BpoSites.Select(item => item.BPO).Distinct();
        if (!string.IsNullOrWhiteSpace(queryOptions.SearchText))
        {
            query = query.Where(item => item.StartsWith(queryOptions.SearchText));
        }

        var totalRecordCount = await query.CountAsync();
        var sql = query.ToQueryString();
        var pagedResult = await query.OrderBy(item => item).Skip(queryOptions.Skip).Take(queryOptions.Fetch).ToListAsync();

        return (pagedResult, totalRecordCount);
    }

    public async Task<(IEnumerable<string> Sites, int TotalRecords)> GetAllSitesAsync(QueryOptions queryOptions)
    {
        var query = dataContext.BpoSites.Select(item => item.Site).Distinct();
        if (!string.IsNullOrWhiteSpace(queryOptions.SearchText))
        {
            query = query.Where(item => item.StartsWith(queryOptions.SearchText));
        }

        var pagedResult = await query.OrderBy(item => item).Skip(queryOptions.Skip).Take(queryOptions.Fetch).ToListAsync();
        var totalRecordCount = await query.CountAsync();
        return (pagedResult, totalRecordCount);
    }

    public async Task<IEnumerable<PortalUser>> GetUsersByPermissionsAsync(int workspaceId, IEnumerable<string> permissions)
    {
        var rolesWithPermissions = await dataContext.RolePermissions
            .Include(rp => rp.Permission)
            .Where(rp => permissions.Contains(rp.Permission.Name))
            .Select(rp => rp.RoleId)
            .ToListAsync();

        var usersWithRoles = await dataContext.WorkspaceUsers
            .Include(wu => wu.PortalUser)
            .Where(wu => !wu.IsUserDisabled && wu.WorkspaceId == workspaceId)
            .Where(wu => wu.RoleIds.Intersect(rolesWithPermissions).Any())
            .Select(wu => wu.PortalUser)
            .ToListAsync();

        return usersWithRoles;
    }

    public async Task<string> GetIncrementalEnterpriseNameAsync(string enterprise)
    {
        var enterprises = await dataContext.Workspaces
            .Where(w => EF.Functions.ILike(w.Enterprise, $"{enterprise}%"))
            .Select(w => w.Enterprise)
            .ToListAsync();

        if (enterprises.Count == 0)
        {
            return enterprise;
        }
        else
        {
            var max = 0;

            foreach (var e in enterprises)
            {
                if (int.TryParse(e[enterprise.Length..], out int count))
                {
                    if (count > max)
                    {
                        max = count;
                    }
                }
            }

            return $"{enterprise}{max + 1}";
        }
    }

    public async Task UpdateAutoAppConfigurationAsync(int workspaceId, JsonElement appConfig)
    {
        var workspace = await dataContext.Workspaces.FirstOrDefaultAsync(w => w.WorkspaceId == workspaceId);
        if (workspace is null)
        {
            throw new ArgumentException("Workspace not found");
        }

        workspace.AppConfig = appConfig.ToString();
        await dataContext.SaveChangesAsync();
    }

    public async Task<NCWorkspace> GetNCWorkspaceInfoAsync(int workspaceId)
    {
        var record = await dataContext.NcworkspaceUserTrackings.FirstOrDefaultAsync(n => n.WorkspaceId == workspaceId);

        if (record is not null)
        {
            var user = await dataContext.PortalUsers.FirstOrDefaultAsync(u => u.PortalUserId == record.UserId);
            if (user is not null)
            {
                var workspace = await dataContext.Workspaces
                    .Include(w => w.BpoSite)
                    .FirstOrDefaultAsync(w => w.WorkspaceId == workspaceId);

                var agent = await dataContext.Agents.
                    FirstOrDefaultAsync(a => a.WorkspaceId == workspaceId && EF.Functions.ILike(a.LoginId, user.Email));

                var downloads = await dataContext.AppDownloads
                    .Include(d => d.Release)
                    .Where(d => d.ModelType.Name == "NC")
                    .OrderByDescending(d => d.Release.CreatedUtc).Take(50)
                    .ToListAsync();

                var latest = PickLatest(downloads);

                return new NCWorkspace
                {
                    Workspace = workspace,
                    LoginCount = record.LoginCount,
                    StepsCompleted = record.StepsCompleted,
                    Agent = agent,
                    AppVersion = latest?.Release.Version,
                    MsiDownloadLink = latest?.MsiDownloadUrl,
                    ExeDownloadLink = latest?.ExeDownloadUrl
                };
            }
        }

        return null;
    }

    private static AppDownload PickLatest(List<AppDownload> downloads)
    {
        return downloads.OrderByDescending(d => d.Release.Version, new AppVersionComparer())
            .FirstOrDefault();
    }

    public async Task<Workspace> GetByInstallerId(Guid installerId)
    {
        return await dataContext.Workspaces.FirstOrDefaultAsync(w => !w.IsDisabled && w.InstallerId == installerId);
    }
}

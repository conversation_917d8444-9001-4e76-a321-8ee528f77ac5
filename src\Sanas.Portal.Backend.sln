﻿
Microsoft Visual Studio Solution File, Format Version 12.00
# Visual Studio Version 17
VisualStudioVersion = 17.7.34202.233
MinimumVisualStudioVersion = 10.0.40219.1
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Sanas.Backend.API", "Sanas.Backend.API\Sanas.Backend.API.csproj", "{746C88B0-8A76-4F24-8E8F-AF311BD2786C}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Sanas.Backend.Data", "Sanas.Backend.Data\Sanas.Backend.Data.csproj", "{44C6B5C9-F030-40E5-B647-19BCF4B7AF05}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Sanas.Auth0Client", "Sanas.Auth0Client\Sanas.Auth0Client.csproj", "{24D02646-189F-4704-BA2D-F2C773EDF225}"
EndProject
Project("{9A19103F-16F7-4668-BE54-9A1E7A4F7556}") = "Sanas.Backend.BO", "Sanas.Backend.BO\Sanas.Backend.BO.csproj", "{712C1A2A-1C70-4C8C-9D10-7E367DE24377}"
EndProject
Project("{2150E333-8FDC-42A3-9474-1A3956D46DE8}") = "Solution Items", "Solution Items", "{C81FB189-56B6-4C8C-A7F3-D1035327813A}"
	ProjectSection(SolutionItems) = preProject
		.editorconfig = .editorconfig
	EndProjectSection
EndProject
Global
	GlobalSection(SolutionConfigurationPlatforms) = preSolution
		Debug|Any CPU = Debug|Any CPU
		Release|Any CPU = Release|Any CPU
	EndGlobalSection
	GlobalSection(ProjectConfigurationPlatforms) = postSolution
		{746C88B0-8A76-4F24-8E8F-AF311BD2786C}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{746C88B0-8A76-4F24-8E8F-AF311BD2786C}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{746C88B0-8A76-4F24-8E8F-AF311BD2786C}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{746C88B0-8A76-4F24-8E8F-AF311BD2786C}.Release|Any CPU.Build.0 = Release|Any CPU
		{44C6B5C9-F030-40E5-B647-19BCF4B7AF05}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{44C6B5C9-F030-40E5-B647-19BCF4B7AF05}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{44C6B5C9-F030-40E5-B647-19BCF4B7AF05}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{44C6B5C9-F030-40E5-B647-19BCF4B7AF05}.Release|Any CPU.Build.0 = Release|Any CPU
		{24D02646-189F-4704-BA2D-F2C773EDF225}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{24D02646-189F-4704-BA2D-F2C773EDF225}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{24D02646-189F-4704-BA2D-F2C773EDF225}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{24D02646-189F-4704-BA2D-F2C773EDF225}.Release|Any CPU.Build.0 = Release|Any CPU
		{712C1A2A-1C70-4C8C-9D10-7E367DE24377}.Debug|Any CPU.ActiveCfg = Debug|Any CPU
		{712C1A2A-1C70-4C8C-9D10-7E367DE24377}.Debug|Any CPU.Build.0 = Debug|Any CPU
		{712C1A2A-1C70-4C8C-9D10-7E367DE24377}.Release|Any CPU.ActiveCfg = Release|Any CPU
		{712C1A2A-1C70-4C8C-9D10-7E367DE24377}.Release|Any CPU.Build.0 = Release|Any CPU
	EndGlobalSection
	GlobalSection(SolutionProperties) = preSolution
		HideSolutionNode = FALSE
	EndGlobalSection
	GlobalSection(ExtensibilityGlobals) = postSolution
		SolutionGuid = {2FEBCEE2-7DF6-40E6-8977-BADF609C9185}
	EndGlobalSection
EndGlobal

﻿<?xml version="1.0" encoding="utf-8"?>
<RuleSet Name="Rules for BP.FVC projects" Description="Code analysis rules for BP.FVC projects." ToolsVersion="16.0">
  <Rules AnalyzerId="Microsoft.Analyzers.ManagedCodeAnalysis" RuleNamespace="Microsoft.Rules.Managed">
    <Rule Id="CA1001" Action="Warning" />
    <Rule Id="CA1002" Action="Warning" />
    <Rule Id="CA1004" Action="Warning" />
    <Rule Id="CA1007" Action="Warning" />
    <Rule Id="CA1008" Action="Warning" />
    <Rule Id="CA1009" Action="Warning" />
    <Rule Id="CA1010" Action="Warning" />
    <Rule Id="CA1011" Action="Warning" />
    <Rule Id="CA1012" Action="Warning" />
    <Rule Id="CA1013" Action="Warning" />
    <Rule Id="CA1016" Action="Warning" />
    <Rule Id="CA1023" Action="Warning" />
    <Rule Id="CA1025" Action="Warning" />
    <Rule Id="CA1027" Action="Warning" />
    <Rule Id="CA1028" Action="Warning" />
    <Rule Id="CA1033" Action="Warning" />
    <Rule Id="CA1035" Action="Warning" />
    <Rule Id="CA1036" Action="Warning" />
    <Rule Id="CA1038" Action="Warning" />
    <Rule Id="CA1039" Action="Warning" />
    <Rule Id="CA1043" Action="Warning" />
    <Rule Id="CA1044" Action="Warning" />
    <Rule Id="CA1047" Action="Warning" />
    <Rule Id="CA1048" Action="Warning" />
    <Rule Id="CA1049" Action="Warning" />
    <Rule Id="CA1050" Action="Warning" />
    <Rule Id="CA1051" Action="Warning" />
    <Rule Id="CA1052" Action="Warning" />
    <Rule Id="CA1053" Action="Warning" />
    <Rule Id="CA1058" Action="Warning" />
    <Rule Id="CA1059" Action="Warning" />
    <Rule Id="CA1060" Action="Warning" />
    <Rule Id="CA1061" Action="Warning" />
    <Rule Id="CA1063" Action="Warning" />
    <Rule Id="CA1064" Action="Warning" />
    <Rule Id="CA1065" Action="Warning" />
    <Rule Id="CA1301" Action="Warning" />
    <Rule Id="CA1307" Action="Warning" />
    <Rule Id="CA1400" Action="Warning" />
    <Rule Id="CA1401" Action="Warning" />
    <Rule Id="CA1403" Action="Warning" />
    <Rule Id="CA1404" Action="Warning" />
    <Rule Id="CA1405" Action="Warning" />
    <Rule Id="CA1410" Action="Warning" />
    <Rule Id="CA1415" Action="Warning" />
    <Rule Id="CA1500" Action="Warning" />
    <Rule Id="CA1501" Action="Warning" />
    <Rule Id="CA1502" Action="Warning" />
    <Rule Id="CA1504" Action="Warning" />
    <Rule Id="CA1505" Action="Warning" />
    <Rule Id="CA1506" Action="None" />
    <Rule Id="CA1700" Action="Warning" />
    <Rule Id="CA1701" Action="Warning" />
    <Rule Id="CA1702" Action="Warning" />
    <Rule Id="CA1703" Action="Warning" />
    <Rule Id="CA1704" Action="Warning" />
    <Rule Id="CA1707" Action="Warning" />
    <Rule Id="CA1708" Action="Warning" />
    <Rule Id="CA1710" Action="Warning" />
    <Rule Id="CA1712" Action="Warning" />
    <Rule Id="CA1713" Action="Warning" />
    <Rule Id="CA1714" Action="Warning" />
    <Rule Id="CA1715" Action="Warning" />
    <Rule Id="CA1717" Action="Warning" />
    <Rule Id="CA1719" Action="Warning" />
    <Rule Id="CA1720" Action="Warning" />
    <Rule Id="CA1721" Action="Warning" />
    <Rule Id="CA1722" Action="Warning" />
    <Rule Id="CA1725" Action="Warning" />
    <Rule Id="CA1726" Action="Warning" />
    <Rule Id="CA1800" Action="Warning" />
    <Rule Id="CA1802" Action="Warning" />
    <Rule Id="CA1804" Action="Warning" />
    <Rule Id="CA1806" Action="Warning" />
    <Rule Id="CA1809" Action="Warning" />
    <Rule Id="CA1812" Action="Warning" />
    <Rule Id="CA1813" Action="Warning" />
    <Rule Id="CA1815" Action="Warning" />
    <Rule Id="CA1820" Action="Warning" />
    <Rule Id="CA1821" Action="Warning" />
    <Rule Id="CA1822" Action="Warning" />
    <Rule Id="CA1900" Action="Warning" />
    <Rule Id="CA1901" Action="Warning" />
    <Rule Id="CA2001" Action="Warning" />
    <Rule Id="CA2002" Action="Warning" />
    <Rule Id="CA2100" Action="Warning" />
    <Rule Id="CA2101" Action="Warning" />
    <Rule Id="CA2104" Action="Warning" />
    <Rule Id="CA2105" Action="Warning" />
    <Rule Id="CA2111" Action="Warning" />
    <Rule Id="CA2112" Action="Warning" />
    <Rule Id="CA2114" Action="Warning" />
    <Rule Id="CA2119" Action="Warning" />
    <Rule Id="CA2121" Action="Warning" />
    <Rule Id="CA2123" Action="Warning" />
    <Rule Id="CA2131" Action="Warning" />
    <Rule Id="CA2132" Action="Warning" />
    <Rule Id="CA2133" Action="Warning" />
    <Rule Id="CA2134" Action="Warning" />
    <Rule Id="CA2137" Action="Warning" />
    <Rule Id="CA2138" Action="Warning" />
    <Rule Id="CA2140" Action="Warning" />
    <Rule Id="CA2141" Action="Warning" />
    <Rule Id="CA2146" Action="Warning" />
    <Rule Id="CA2147" Action="Warning" />
    <Rule Id="CA2149" Action="Warning" />
    <Rule Id="CA2200" Action="Warning" />
    <Rule Id="CA2202" Action="Warning" />
    <Rule Id="CA2205" Action="Warning" />
    <Rule Id="CA2207" Action="Warning" />
    <Rule Id="CA2208" Action="Warning" />
    <Rule Id="CA2211" Action="Warning" />
    <Rule Id="CA2212" Action="Warning" />
    <Rule Id="CA2213" Action="Warning" />
    <Rule Id="CA2214" Action="Warning" />
    <Rule Id="CA2215" Action="Warning" />
    <Rule Id="CA2216" Action="Warning" />
    <Rule Id="CA2217" Action="Warning" />
    <Rule Id="CA2218" Action="Warning" />
    <Rule Id="CA2219" Action="Warning" />
    <Rule Id="CA2220" Action="Warning" />
    <Rule Id="CA2221" Action="Warning" />
    <Rule Id="CA2222" Action="Warning" />
    <Rule Id="CA2223" Action="Warning" />
    <Rule Id="CA2224" Action="Warning" />
    <Rule Id="CA2226" Action="Warning" />
    <Rule Id="CA2229" Action="Warning" />
    <Rule Id="CA2230" Action="Warning" />
    <Rule Id="CA2231" Action="Warning" />
    <Rule Id="CA2235" Action="Warning" />
    <Rule Id="CA2236" Action="Warning" />
    <Rule Id="CA2237" Action="Warning" />
    <Rule Id="CA2238" Action="Warning" />
    <Rule Id="CA2240" Action="Warning" />
    <Rule Id="CA2241" Action="Warning" />
    <Rule Id="CA2242" Action="Warning" />
    <Rule Id="CA2243" Action="Warning" />
  </Rules>
  <Rules AnalyzerId="Microsoft.CodeAnalysis.CSharp.EditorFeatures" RuleNamespace="Microsoft.CodeAnalysis.CSharp.EditorFeatures">
    <Rule Id="IDE0031" Action="Warning" />
    <Rule Id="IDE0031WithoutSuggestion" Action="Warning" />
    <Rule Id="IDE1007" Action="None" />
  </Rules>
  <Rules AnalyzerId="Microsoft.CodeAnalysis.CSharp.Features" RuleNamespace="Microsoft.CodeAnalysis.CSharp.Features">
    <Rule Id="IDE0001" Action="Warning" />
    <Rule Id="IDE0002" Action="Warning" />
    <Rule Id="IDE0003" Action="Warning" />
    <Rule Id="IDE0004" Action="Warning" />
    <Rule Id="IDE0005" Action="Warning" />
    <Rule Id="IDE0007" Action="Warning" />
    <Rule Id="IDE0007WithoutSuggestion" Action="Warning" />
    <Rule Id="IDE0008" Action="None" />
    <Rule Id="IDE0008WithoutSuggestion" Action="None" />
    <Rule Id="IDE0009" Action="Warning" />
    <Rule Id="IDE0009WithoutSuggestion" Action="Warning" />
    <Rule Id="IDE0011" Action="Warning" />
    <Rule Id="IDE0011WithoutSuggestion" Action="Warning" />
    <Rule Id="IDE0012" Action="Warning" />
    <Rule Id="IDE0013" Action="Warning" />
    <Rule Id="IDE0014" Action="Warning" />
    <Rule Id="IDE0015" Action="Warning" />
    <Rule Id="IDE0016" Action="Info" />
    <Rule Id="IDE0016WithoutSuggestion" Action="Info" />
    <Rule Id="IDE0017" Action="Info" />
    <Rule Id="IDE0017WithoutSuggestion" Action="Info" />
    <Rule Id="IDE0018" Action="Warning" />
    <Rule Id="IDE0018WithoutSuggestion" Action="Warning" />
    <Rule Id="IDE0019" Action="Info" />
    <Rule Id="IDE0019WithoutSuggestion" Action="Info" />
    <Rule Id="IDE0020" Action="Info" />
    <Rule Id="IDE0020WithoutSuggestion" Action="Info" />
    <Rule Id="IDE0021" Action="Info" />
    <Rule Id="IDE0021WithoutSuggestion" Action="Info" />
    <Rule Id="IDE0022" Action="Info" />
    <Rule Id="IDE0022WithoutSuggestion" Action="Info" />
    <Rule Id="IDE0023" Action="Info" />
    <Rule Id="IDE0023WithoutSuggestion" Action="Info" />
    <Rule Id="IDE0024" Action="Info" />
    <Rule Id="IDE0024WithoutSuggestion" Action="Info" />
    <Rule Id="IDE0025" Action="Info" />
    <Rule Id="IDE0025WithoutSuggestion" Action="Info" />
    <Rule Id="IDE0026" Action="Info" />
    <Rule Id="IDE0026WithoutSuggestion" Action="Info" />
    <Rule Id="IDE0027" Action="Info" />
    <Rule Id="IDE0027WithoutSuggestion" Action="Info" />
    <Rule Id="IDE0028" Action="Info" />
    <Rule Id="IDE0028WithoutSuggestion" Action="Info" />
    <Rule Id="IDE0029" Action="Info" />
    <Rule Id="IDE0029WithoutSuggestion" Action="Info" />
    <Rule Id="IDE0030" Action="Info" />
    <Rule Id="IDE0030WithoutSuggestion" Action="Info" />
    <Rule Id="IDE1005" Action="Warning" />
    <Rule Id="IDE1005WithoutSuggestion" Action="Info" />
    <Rule Id="IDE1006" Action="None" />
    <Rule Id="IDE1006WithoutSuggestion" Action="None" />
  </Rules>
  <Rules AnalyzerId="Microsoft.CodeAnalysis.NetAnalyzers" RuleNamespace="Microsoft.CodeAnalysis.NetAnalyzers">
    <Rule Id="CA1000" Action="None" />
    <Rule Id="CA1711" Action="None" />
    <Rule Id="CA2201" Action="None" />
  </Rules>
  <Rules AnalyzerId="StyleCop.Analyzers" RuleNamespace="StyleCop.Analyzers">
    <Rule Id="CS1591" Action="None" />
    <Rule Id="SA0001" Action="None" />
    <Rule Id="SA1028" Action="None" />
    <Rule Id="SA1101" Action="None" />
    <Rule Id="SA1200" Action="None" />
    <Rule Id="SA1201" Action="None" />
    <Rule Id="SA1201A" Action="None" />
    <Rule Id="SA1202" Action="None" />
    <Rule Id="SA1204" Action="None" />
    <Rule Id="SA1305" Action="Warning" />
    <Rule Id="SA1309" Action="None" />
    <Rule Id="SA1407" Action="None" />
    <Rule Id="SA1408" Action="None" />
    <Rule Id="SA1504" Action="None" />
    <Rule Id="SA1515" Action="None" />
    <Rule Id="SA1516" Action="None" />
    <Rule Id="SA1518" Action="None" />
    <Rule Id="SA1413" Action="None" />
    <Rule Id="SA1600" Action="None" />
    <Rule Id="SA1601" Action="None" />
    <Rule Id="SA1602" Action="None" />
    <Rule Id="SA1616" Action="None" />
    <Rule Id="SA1623" Action="None" />
    <Rule Id="SA1629" Action="None" />
    <Rule Id="SA1633" Action="None" />
    <Rule Id="SA1634" Action="None" />
    <Rule Id="SA1635" Action="None" />
    <Rule Id="SA1636" Action="None" />
    <Rule Id="SA1637" Action="None" />
    <Rule Id="SA1638" Action="None" />
    <Rule Id="SA1640" Action="None" />
    <Rule Id="SA1641" Action="None" />
    <Rule Id="SA1652" Action="None" />
    <Rule Id="SX1101" Action="Warning" />
    <Rule Id="SX1309" Action="Warning" />
    <Rule Id="SX1309S" Action="Warning" />
  </Rules>
</RuleSet>
﻿namespace Sanas.Backend.API.Controllers.Tests;

[TestFixture()]
public class PortalUsersControllerTests
{
    [Test()]
    public void PortalUsersController_Test()
    {

    }

    [Test()]
    public void GetAllAsync_Test()
    {

    }

    [Test()]
    public void GetByWorkspaceAsync_Test()
    {

    }

    [Test()]
    public void GetPortalUserAsync_Test()
    {

    }

    [Test()]
    public void CreatePortalUserAsync_Test()
    {

    }

    [Test()]
    public void UpdatePortalUserAsync_Test()
    {

    }

    [Test()]
    public void DeletePortalUserAsync_Test()
    {

    }

    [Test()]
    public void GetCurrentUserAsync_Test()
    {

    }
}
﻿namespace Sanas.Backend.API.Controllers.Tests;

[TestFixture()]
public class WorkspacesController_Tests
{
    [Test()]
    public void GetAllAsync_Test()
    {

    }

    [Test()]
    public void WorkspacesController_Test()
    {

    }

    [Test()]
    public void GetAllAsync_Test1()
    {

    }

    [Test()]
    public void GetWorkspaceByIdAsync_Test()
    {

    }

    [Test()]
    public void CreateWorkspaceAsync_Test()
    {

    }

    [Test()]
    public void UpdateWorkspaceAsync_Test()
    {

    }

    [Test()]
    public void DeleteWorkspaceAsync_Test()
    {

    }

    [Test()]
    public void GetPortalUsersInWorkspaceAsync_Test()
    {

    }

    [Test()]
    public void GetTeamsInWorkspaceAsync_Test()
    {

    }
}